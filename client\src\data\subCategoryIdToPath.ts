export const subCategoryIdToPath: Record<string, string> = {
    // Home Instructions
    "101": "/category/homeinstructions/pets",
    "102": "/category/homeinstructions/trash",
    "103": "/category/homeinstructions/other",
    "104": "/category/homeinstructions/security",
    "105": "/category/homeinstructions/homelocation",
  
    // Home Documents
    "301": "/category/homedocuments/utility",
    "302": "/category/homedocuments/gas",
    "303": "/category/homedocuments/water",
    "304": "/category/homedocuments/trashcollection",
    "305": "/category/homedocuments/hvac",
    "306": "/category/homedocuments/pest",
    "307": "/category/homedocuments/lawn",
    "308": "/category/homedocuments/cable",
    "309": "/category/homedocuments/internet",
  
    // Will Instructions
    "105A": "/category/willinstructions/location",
    "105B": "/category/willinstructions/legal",

    // Funeral Arrangements
    "205A": "/category/funeralarrangements/details",
    "205B": "/category/funeralarrangements/ceremonylocation",
    "205C": "/category/funeralarrangements/clergy",
    "205D": "/category/funeralarrangements/notification",
    "205E": "/category/funeralarrangements/proceedings",
  
    // Important Contacts
    "207A": "/category/importantcontacts/friendsfamily",
    "207B": "/category/importantcontacts/work",
    "207C": "/category/importantcontacts/religiousaffiliation",
    "207D": "/category/importantcontacts/clubs",
  
    // Social Media
    "206A": "/category/socialmedia/email",
    "206B": "/category/socialmedia/facebook",
    "206C": "/category/socialmedia/instagram",
    "206D": "/category/socialmedia/otheraccounts",
    "206E": "/category/socialmedia/cellphone",

    // Insurance
    "401A": "/category/insurance/home",
    "401B": "/category/insurance/medical",
    "401C": "/category/insurance/life",

    // Ownership Information
    "402A": "/category/ownershipinfo/propertyinformation",
    "402B": "/category/ownershipinfo/vehicleinformation",
    "402C": "/category/ownershipinfo/driverinformation",

    // Financial Plans
    "501A": "/category/financialplans/financialplanner",
    "501B": "/category/financialplans/investments",
    "501C": "/category/financialplans/stocks",
    "501D": "/category/financialplans/bonds",
    "501E": "/category/financialplans/moneymarket",
    
    // Banking Information
    "405A": "/category/bankinginformation/bankdetails",

    // Passwords
    "1204A": "/category/passwords/otherpasswords",
  };