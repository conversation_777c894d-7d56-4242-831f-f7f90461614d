import { useState, useEffect } from 'react';
import CategoryReviewPage from '@/mobile/components/category/CategoryReviewPage';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import willInstructionsData from '@/data/willInstructions.json';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchUserInputs, selectUserInputs, selectLoading, selectError } from '@/store/slices/willInstructionsSlice';
import { Button } from '@/components/ui/button';

interface ReviewItem {
  id: string;
  title: string;
  subtitle?: string;
  data: string;
  onEdit: () => void;
}

// Map question IDs to their subcategory IDs
const questionToSubcategoryMap: Record<string, string> = {};

// Initialize the question to subcategory mapping
Object.entries(willInstructionsData).forEach(([_, questions]) => {
  questions.forEach(question => {
    // Map to our custom subcategory keys
    let subKey = '';
    if (question.sectionId === '105A') subKey = '105A';
    if (question.sectionId === '105B') subKey = '105B';
    if (subKey) questionToSubcategoryMap[question.id] = subKey;
  });
});

const WillInstructionReviewPage = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const { categoryName } = useParams<{ categoryName: string }>();
  const [topics, setTopics] = useState<ReviewItem[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Get data from Redux store
  const userInputs = useAppSelector(selectUserInputs);
  const isLoading = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      // Transform the data for the review page
      const allTopics: ReviewItem[] = [];

      // Process all user inputs
      userInputs.forEach((userInput) => {
        // Deduplicate answersBySection by originalSectionId (keep the last one)
        const dedupedAnswersBySection = userInput.answersBySection.reduce((acc, section) => {
          acc[section.originalSectionId] = section; // This will keep the last occurrence
          return acc;
        }, {} as Record<string, typeof userInput.answersBySection[0]>);

        Object.values(dedupedAnswersBySection).forEach((section) => {
          // Skip sections that aren't part of Will Instructions
          if (!section.originalSectionId?.startsWith('105')) {
            return;
          }

          section.answers.forEach((answer) => {
            allTopics.push({
              id: answer.originalQuestionId,
              title: answer.question,
              subtitle: `Section: ${section.originalSectionId}`,
              data: answer.answer,
              onEdit: () => {
                // Determine which subcategory to navigate to
                let route = '/category/willinstructions';
                // Location sections
                if (section.originalSectionId === '105A') {
                  route = '/category/willinstructions/location';
                }
                // Legal section
                else if (section.originalSectionId === '105B') {
                  route = '/category/willinstructions/legal';
                }
                navigate(`${route}?questionId=${answer.originalQuestionId}`);
              }
            });
          });
        });
      });
      setTopics(allTopics);
    }
  }, [userInputs, isLoading, navigate]);

  if (isLoading) {
    return (
      <>
        <GradiantHeader title="Will Instructions" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  if (error || reduxError) {
    return (
      <>
        <GradiantHeader title="Will Instructions" showAvatar={true} />
        <div className="p-4">
          <Alert variant="destructive">
            <AlertDescription>{error || reduxError}</AlertDescription>
          </Alert>
        </div>
      </>
    );
  }

  if (!user || !user.id) {
    return (
      <>
        <GradiantHeader title="Will Instructions" showAvatar={true} />
        <div className="p-4">
          <Alert variant="destructive">
            <AlertDescription>You must be logged in to view your answers</AlertDescription>
          </Alert>
        </div>
      </>
    );
  }

  if (userInputs.length === 0 && !isLoading) {
    return (
      <>
        <GradiantHeader title="Will Instructions" showAvatar={true} />
        <div className="p-4">
          <Alert>
            <AlertDescription>No will instructions answers found. Please complete some questions first.</AlertDescription>
          </Alert>
        </div>
      </>
    );
  }

  return (
    <>
      <CategoryReviewPage
        categoryTitle="Will Instructions"
        infoTitle="How to edit your information"
        infoDescription="Review the details about your will instructions. Tap Edit on any item to update it."
        topics={topics}
        onPrint={() => window.print()}
        afterTopics={
          <Button 
            onClick={() => navigate('/category/funeralarrangements')}
            className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
          >
            Continue to Funeral Arrangements
          </Button>
        }
      />
    </>
  );
};

export default WillInstructionReviewPage;
