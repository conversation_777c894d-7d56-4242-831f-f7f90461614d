import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CategoryReviewPage from '@/web/components/Category/CategoryReviewPage';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { useAuth } from '@/contexts/AuthContext';
import homeInstructionsData from '@/data/homeIntsructions.json';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  UserInput as ReduxUserInput
} from '../../../../store/slices/homeInstructionsSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { useAccessControl } from '@/hooks/useAccessControl';
import { CATEGORIES } from '@/constants/categories';
import { createUserInfo } from '@/utils/avatarUtils';

// Define interfaces for the data structure
interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

interface UserInput {
  userId: string;
  categoryId: string;
  originalCategoryId: string;
  subCategoryId: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
}

// Map subcategory IDs to their routes
const subcategoryRoutes: Record<string, string> = {
  '101': '/category/homeinstructions/pets',
  '102': '/category/homeinstructions/trash',
  '103': '/category/homeinstructions/other',
  '104': '/category/homeinstructions/security',
  '105': '/category/homeinstructions/homelocation',
};

// Map question IDs to their subcategory IDs
const questionToSubcategoryMap: Record<string, string> = {};

// Initialize the question to subcategory mapping
Object.entries(homeInstructionsData).forEach(([subcategoryId, questions]) => {
  questions.forEach(question => {
    questionToSubcategoryMap[question.id] = subcategoryId;
  });
});

export default function HomeInstructionsReview() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { canAccessCategory, userAccess } = useAccessControl();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);

  // Get data from Redux store using selectors
  const userInputs = useAppSelector((state: any) => state.homeInstructions.userInputs) as ReduxUserInput[];
  const loading = useAppSelector((state: any) => state.homeInstructions.loading);
  const error = useAppSelector((state: any) => state.homeInstructions.error);

  // Fallback user info if not authenticated
  const userInfo = createUserInfo(user);

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in HomeInstructionsReview component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in HomeInstructionsReview component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Process user inputs to create topics for review
  useEffect(() => {
    if (!user?.id) {
      return;
    }

    if (userInputs.length > 0 && !loading) {
      // Transform the data for the review page
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      // Process all user inputs
      userInputs.forEach((userInput: ReduxUserInput) => {
        const subcategoryId = userInput.originalSubCategoryId;

        // Process answers by section
        userInput.answersBySection.forEach((section) => {
          section.answers.forEach((answer) => {
            // Find the original question from our data
            const questionId = answer.originalQuestionId;
            const subcategoryData = homeInstructionsData[subcategoryId as keyof typeof homeInstructionsData];
            const questionData = subcategoryData?.find((q: any) => q.id === questionId);

            if (questionData) {
              allTopics.push({
                id: questionId,
                title: questionData.text,
                subtitle: `Section: ${section.originalSectionId}`,
                data: answer.answer,
                onEdit: () => {
                  // Navigate to the appropriate subcategory page with question ID as a parameter
                  const route = subcategoryRoutes[subcategoryId as keyof typeof subcategoryRoutes];
                  if (route) {
                    navigate(`${route}?questionId=${questionId}`);
                  }
                }
              });
            }
          });
        });
      });

      setTopics(allTopics);
    }
  }, [userInputs, loading, navigate, user]);

  if (loading) {
    return <div className="flex justify-center items-center h-screen">Loading your answers...</div>;
  }

  if (error) {
    return <div className="flex justify-center items-center h-screen text-red-500">{error}</div>;
  }

  if (!user?.id) {
    return <div className="flex justify-center items-center h-screen text-red-500">You must be logged in to view your answers</div>;
  }

  // Check if user can access Home Documents
  const canAccessHomeDocuments = canAccessCategory(CATEGORIES.HOME_DOCUMENTS);

  // Handle navigation to Home Documents
  const handleContinueToHomeDocuments = () => {
    if (canAccessHomeDocuments) {
      navigate('/category/homedocuments');
    } else {
      // For temporary_key users, show upgrade message or navigate to subscription
      if (userAccess?.subscriptionType === 'temporary_key') {
        // You can either show a toast message or navigate to subscription page
        navigate('/subscription');
      }
    }
  };

  return (
    <div className="flex flex-col items-center">
      <CategoryReviewPage
        categoryTitle="Home Instructions"
        infoTitle="How to edit your information"
        infoDescription="Now, you are about to enter details about your home, life, and essential information to be passed on to your family members. Each section has several questions. Fill out as much as you can/like. You can always come back to fill out more information later."
        topics={topics}
        user={userInfo}
        onPrint={() => window.print()}
        afterTopics={
          <button
            onClick={handleContinueToHomeDocuments}
            className={`px-8 py-3 rounded-md transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1 ${
              canAccessHomeDocuments
                ? 'bg-[#2BCFD5] text-white hover:bg-[#1F4168]'
                : 'bg-gray-400 text-white cursor-not-allowed'
            }`}
            disabled={!canAccessHomeDocuments}
          >
            {canAccessHomeDocuments 
              ? 'Continue to Home Documents' 
              : 'Upgrade to Continue'
            }
          </button>
        }
      />
    </div>
  );
}