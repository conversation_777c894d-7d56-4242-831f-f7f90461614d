{"version": 3, "file": "accessControlMiddleware.js", "sourceRoot": "", "sources": ["../../src/middleware/accessControlMiddleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,0DAAkC;AAClC,4DAAoC;AAEpC,wEAAgD;AAEhD,wCAAyC;AACzC,0CAAmE;AAenE,wDAAwD;AACxD,MAAM,0BAA0B,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAEvD,mDAAmD;AACnD,MAAM,8BAA8B,GAAG,CAAC,IAAS,EAAW,EAAE;IAC5D,IAAI,CAAC,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,0BAA0B,KAAK,IAAI,EAAE,CAAC;QAC/E,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,OAAO,EAAE,CAAC;IACxE,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC/B,MAAM,WAAW,GAAG,WAAW,GAAG,WAAW,CAAC;IAE9C,OAAO,WAAW,IAAI,0BAA0B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;AAClF,CAAC,CAAC;AAEF,sDAAsD;AACtD,MAAM,wBAAwB,GAAG,CAAO,MAAc,EAAiB,EAAE;IACvE,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,IAAI,IAAI,8BAA8B,CAAC,IAAI,CAAC,EAAE,CAAC;YACjD,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,IAAI,CAAC,wBAAwB,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3C,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;YAElB,wCAAwC;YACxC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,MAAM,WAAW,GAAG,MAAM,eAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACvD,MAAM,SAAS,GAAG,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,SAAS,MAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ,CAAA,IAAI,OAAO,CAAC;gBAC7E,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,IAAI,YAAY,CAAC;gBACpF,IAAA,yCAAiC,EAAC,IAAI,CAAC,KAAK,EAAE,aAAa,EAAE,SAAS,CAAC;qBACpE,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;qBAChF,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,GAAG,CAAC,CAAC,CAAC;YAC7F,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;IAChE,CAAC;AACH,CAAC,CAAA,CAAC;AAEF,gCAAgC;AAChC,MAAM,yBAAyB,GAAG,CAAO,MAAc,EAAuD,EAAE;IAC9G,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;QAC7B,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,MAAa,CAAC;QAChC,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;SACpC,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;QACjE,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;IAC7B,CAAC;AACH,CAAC,CAAA,CAAC;AAEF,4BAA4B;AAC5B,MAAM,WAAW,GAAG,CAAO,MAAc,EAAoB,EAAE;IAC7D,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,MAAM,eAAK,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,CAAC,KAAK,CAAC;IACjB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAA,CAAC;AAEF,uCAAuC;AACvC,MAAM,aAAa,GAAG,CAAO,MAAc,EAAoB,EAAE;IAC/D,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,qBAAW,CAAC,OAAO,CAAC;YAC5C,aAAa,EAAE,MAAM;YACrB,MAAM,EAAE,UAAU,CAAC,qCAAqC;SACzD,CAAC,CAAC;QACH,OAAO,CAAC,CAAC,WAAW,CAAC;IACvB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC3D,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAA,CAAC;AAEF,iCAAiC;AAC1B,MAAM,uBAAuB,GAAG,CACrC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAClE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;YACpD,OAAO;QACT,CAAC;QAED,gCAAgC;QAChC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG,MAAM,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAElH,4BAA4B;QAC5B,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE3D,2BAA2B;QAC3B,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE/D,8DAA8D;QAC9D,IAAI,IAAI,CAAC,gBAAgB,KAAK,WAAW,IAAI,CAAC,OAAO,EAAE,CAAC;YACtD,MAAM,wBAAwB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;YACpD,2CAA2C;YAC3C,MAAM,WAAW,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClD,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QAED,kFAAkF;QAClF,IAAI,iBAAkD,CAAC;QACvD,IAAI,eAAgD,CAAC;QAErD,8CAA8C;QAC9C,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9B,KAAK,eAAe;gBAClB,iBAAiB,GAAG,CAAC,UAAkB,EAAE,EAAE;oBACzC,mEAAmE;oBACnE,OAAO,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,CAAC;gBAClD,CAAC,CAAC;gBACF,eAAe,GAAG,CAAC,UAAkB,EAAE,EAAE;oBACvC,iEAAiE;oBACjE,OAAO,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,CAAC;gBAClD,CAAC,CAAC;gBACF,MAAM;YAER,KAAK,WAAW;gBACd,IAAI,OAAO,EAAE,CAAC;oBACZ,2CAA2C;oBAC3C,iBAAiB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;oBAC/B,eAAe,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACN,oCAAoC;oBACpC,MAAM,kBAAkB,GAAG,IAAI,CAAC,sBAAsB,IAAI,8BAA8B,CAAC,IAAI,CAAC,CAAC;oBAC/F,iBAAiB,GAAG,GAAG,EAAE,CAAC,kBAAkB,CAAC;oBAC7C,eAAe,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,sCAAsC;gBACvE,CAAC;gBACD,MAAM;YAER,KAAK,gBAAgB;gBACnB,wDAAwD;gBACxD,IAAI,QAAQ,KAAK,eAAQ,CAAC,KAAK,EAAE,CAAC;oBAChC,iBAAiB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;oBAC/B,eAAe,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;gBAC/B,CAAC;qBAAM,IAAI,QAAQ,KAAK,eAAQ,CAAC,OAAO,EAAE,CAAC;oBACzC,iBAAiB,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;oBACjE,eAAe,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBACjE,CAAC;qBAAM,IAAI,QAAQ,KAAK,eAAQ,CAAC,MAAM,EAAE,CAAC;oBACxC,iBAAiB,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;oBACjE,eAAe,GAAG,GAAG,EAAE,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;gBACjE,CAAC;qBAAM,CAAC;oBACN,mDAAmD;oBACnD,iBAAiB,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;oBAC/B,eAAe,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC;gBAC/B,CAAC;gBACD,MAAM;YAER;gBACE,uBAAuB;gBACvB,iBAAiB,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC;gBAChC,eAAe,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC;QAClC,CAAC;QAED,+CAA+C;QAC/C,GAAG,CAAC,UAAU,GAAG;YACf,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,IAAI,eAAe;YAC1D,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,OAAO;YACP,QAAQ;YACR,eAAe;YACf,sBAAsB,EAAE,IAAI,CAAC,sBAAsB,IAAI,KAAK;YAC5D,iBAAiB;YACjB,eAAe;SAChB,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC5D,CAAC;AACH,CAAC,CAAA,CAAC;AA3GW,QAAA,uBAAuB,2BA2GlC;AAEF,sCAAsC;AAC/B,MAAM,qBAAqB,GAAG,CAAC,kBAA0B,YAAY,EAAE,EAAE;IAC9E,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;YACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC5E,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC;YAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,iCAAiC;gBAC1C,gBAAgB,EAAE,GAAG,CAAC,UAAU,CAAC,gBAAgB;gBACjD,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ;gBACjC,OAAO,EAAE,GAAG,CAAC,UAAU,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAzBW,QAAA,qBAAqB,yBAyBhC;AAEF,gDAAgD;AACzC,MAAM,yBAAyB,GAAG,CAAC,kBAA0B,YAAY,EAAE,EAAE;IAClF,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;YACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC5E,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;YAChD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,sCAAsC;gBAC/C,gBAAgB,EAAE,GAAG,CAAC,UAAU,CAAC,gBAAgB;gBACjD,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ;gBACjC,OAAO,EAAE,GAAG,CAAC,UAAU,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAzBW,QAAA,yBAAyB,6BAyBpC;AAEF,uCAAuC;AAChC,MAAM,kBAAkB,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACvG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;QACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;QACpE,OAAO;IACT,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAC5B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;QAC3D,OAAO;IACT,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAZW,QAAA,kBAAkB,sBAY7B;AAEF,oCAAoC;AAC7B,MAAM,WAAW,GAAG,CAAC,YAAsB,EAAE,EAAE;IACpD,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;YACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,UAAU,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC7C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,QAAQ,YAAY,8BAA8B;gBAC3D,WAAW,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ;aACrC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAjBW,QAAA,WAAW,eAiBtB;AAEF,0CAA0C;AACnC,MAAM,iBAAiB,GAAG,CAAC,kBAA0B,EAAE,EAAE;IAC9D,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;;QAC5E,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;YACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,CAAA,MAAA,GAAG,CAAC,UAAU,CAAC,eAAe,0CAAE,QAAQ,CAAC,kBAAkB,CAAC,CAAA,EAAE,CAAC;YAClE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,cAAc,kBAAkB,8BAA8B;gBACvE,kBAAkB,EAAE,GAAG,CAAC,UAAU,CAAC,eAAe;aACnD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAjBW,QAAA,iBAAiB,qBAiB5B;AAEF,wCAAwC;AACjC,MAAM,uBAAuB,GAAG,CAAC,YAAsB,EAAE,EAAE;IAChE,OAAO,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC5E,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;YACpB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;YACpE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC5D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,qBAAqB,GAAG,CAAC,UAAU,CAAC,gBAAgB,iCAAiC;gBAC9F,aAAa,EAAE,YAAY;aAC5B,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAjBW,QAAA,uBAAuB,2BAiBlC"}