import { useLocation, useParams, useNavigate } from "react-router-dom";
import GradiantHeader from "@/mobile/components/header/gradiantHeader";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const categoryNames: Record<string, string> = {
  homeinstructions: 'Home Instructions',
  homedocuments: 'Home Documents',
  willlocation: 'Will Location',
  funeralarrangements: 'Funeral Arrangements',
  importantcontacts: 'Important Contacts',
  socialmediaphone: 'Social Media and Phone',
  insurance: 'Insurance',
};

export default function CategoryConfirmPage() {
  const { categoryId } = useParams();
  const location = useLocation();
  const navigate = useNavigate();

  // Prefer state, fallback to URL
  const categoryName = location.state?.categoryName;

  const handleStart = () => {
    // Navigate to info page for this category
    navigate(`/category/${categoryId}/info`);
  };

  const handleBack = () => {
    navigate("/dashboard");
  };

  return (
    <div className="min-h-screen bg-background">
      <GradiantHeader title="Current Category" showAvatar={true} />
      <div className="max-w-md mx-auto space-y-4 px-4">
        <Card className="shadow-md bg-gray-50 rounded-xl mt-4">
          <CardContent className="pt-6 px-6 pb-4 space-y-3">
            <h2 className="text-base font-semibold text-purple-700">
              How to Add Your Information
            </h2>
            <p className="text-sm text-gray-700 leading-relaxed">
              Now, you are about to enter details about your home, life, and essential information to be passed on to your family members. Each section has several questions. Fill out as much as you can/like. You can always come back to fill out more information later.
            </p>
            <hr className="border-t border-gray-5 mt-4" />
          </CardContent>
        </Card>
        <div className="space-y-2">
          <Button
            className="w-full min-h-[48px] h-auto bg-[#2BCFD5] hover:bg-[#25b6bb] text-white text-base sm:text-lg font-semibold rounded-lg px-4 py-3 sm:py-4 transition-colors mb-2 flex items-center justify-center"
            onClick={handleStart}
          >
            Get Started with {categoryName}
          </Button>
          <Button
            variant="outline"
            className="w-full h-12 bg-white text-black border-2 border-gray-300 flex items-center justify-center text-lg font-semibold rounded-lg py-2"
            onClick={handleBack}
          >
            Back to All Categories
          </Button>
        </div>
      </div>
    </div>
  );
}
