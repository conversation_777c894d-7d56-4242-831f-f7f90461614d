import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import GoodToKnowBox from '@/web/components/Global/GoodToKnowBox';
import SubCategoryFooterNav from '@/web/components/Global/SubCategoryFooterNav';
import SubCategoryHeader from '@/web/components/Global/SubCategoryHeader';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import SubCategoryTitle from '@/web/components/Global/SubCategoryTitle';
import {
  buildValidationSchema,
  generateInitialValues,
  handleDependentAnswers,
  Question,
  QuestionItem
} from '@/web/components/Category/Insurance/FormFields';
import ScrollToQuestion from '@/web/components/Category/Insurance/ScrollToQuestion';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { Form, Formik, FormikHelpers } from 'formik';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import * as Yup from 'yup';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  UserInput
} from '../../../../store/slices/insuranceSlice';
import { createUserInfo } from '@/utils/avatarUtils';

const LifeInsurance = () => {
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const navigate = useNavigate();
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  // Get data from Redux store using selectors
  const questions = useAppSelector((state: any) => state.insurance.questions['401'] || []).filter((q: any) => q.sectionId === '401C');
  const loading = useAppSelector((state: any) => state.insurance.loading);
  const userInputs = useAppSelector((state: any) => state.insurance.userInputs);

  const tabs = categoryTabsConfig.insurance;

  // Load existing data when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in LifeInsurance component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in LifeInsurance component:', error);
        }
      }
    };
    fetchData();
  }, [dispatch, user]);

  // Populate form with existing data
  useEffect(() => {
    if (userInputs.length > 0) {
      const existingInput = userInputs.find((input: UserInput) =>
        input.originalSubCategoryId === '401' &&
        input.answersBySection.some(section => section.originalSectionId === '401C')
      );

      if (existingInput) {
        const answers = convertUserInputToFormValues(existingInput);
        setSavedAnswers(answers);
        setExistingInputId(existingInput._id || null);
      }
    }
  }, [userInputs]);

  // Handle form submission
  const handleSubmit = async (values: Record<string, any>, { setSubmitting }: FormikHelpers<Record<string, any>>) => {
    try {
      if (!user || !user.id) {
        throw new Error('You must be logged in to save answers');
      }

      // Group answers by section and filter out empty answers
      const answersBySection = questions.reduce((sections: Record<string, any[]>, question: Question) => {
        if (!sections[question.sectionId]) {
          sections[question.sectionId] = [];
        }
        const answer = values[question.id];
        if (answer !== undefined && answer !== '') {
          // Handle type conversion for server validation
          const type = question.type === 'dropdown' ? 'choice' : question.type;
          sections[question.sectionId].push({
            index: sections[question.sectionId].length,
            originalQuestionId: question.id,
            question: question.text,
            type: type,
            answer
          });
        }
        return sections;
      }, {});

      const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
        originalSectionId: sectionId,
        isCompleted: true,
        answers: answers as Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>
      }));

      const hasAnswers = formattedAnswersBySection.some(section => section.answers.length > 0);
      if (!hasAnswers) {
        setSubmitting(false);
        return;
      }

      if (existingInputId) {
        await dispatch(updateUserInput({
          id: existingInputId,
          userData: {
            userId: user.id,
            categoryId: generateObjectId(),
            originalCategoryId: '8',
            subCategoryId: generateObjectId(),
            originalSubCategoryId: '401',
            answersBySection: formattedAnswersBySection
          } as UserInput
        })).unwrap();
      } else {
        const userData: Omit<UserInput, '_id'> = {
          userId: user.id,
          categoryId: generateObjectId(),
          originalCategoryId: '8',
          subCategoryId: generateObjectId(),
          originalSubCategoryId: '401',
          answersBySection: formattedAnswersBySection
        };
        await dispatch(saveUserInput(userData)).unwrap();
      }
      setSubmitting(false);
      navigate('/category/insurance/review');
    } catch (error: any) {
      setSubmitting(false);
    }
  };

  const userInfo = createUserInfo(user);

  // Generate initial values and validation schema
  const initialValues = { ...generateInitialValues(questions), ...savedAnswers };
  const validationSchema = buildValidationSchema(questions, Yup);

  if (questions.length === 0 || loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      <SubCategoryHeader
        title="Insurance"
        backTo="/dashboard"
        user={{
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
          email: user?.email || '<EMAIL>',
          avatar: userInfo.avatar
        }}
      />
      <SubCategoryTabs tabs={tabs} />
      <div className="container mx-auto px-6">
        <SubCategoryTitle
          mainCategory="Insurance"
          category="Life Insurance"
          description="Provide details about your life insurance policy."
        />
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize={true}
              >
                {({ values, setValues, isSubmitting, isValid, dirty }) => {
                  // Handle dependent answers
                  useEffect(() => {
                    handleDependentAnswers(values, questions, setValues);
                  }, [values, setValues]);

                  return (
                    <Form>
                      <ScrollToQuestion questions={questions}>
                        {(refs) => (
                          <>
                            {[...questions]
                              .sort((a, b) => a.order - b.order)
                              .map((question: Question) => (
                                <div
                                  key={question.id}
                                  id={`question-${question.id}`}
                                  ref={(el) => {
                                    if (el) refs[question.id] = el;
                                  }}
                                >
                                  <QuestionItem question={question} values={values} />
                                </div>
                              ))}
                          </>
                        )}
                      </ScrollToQuestion>
                      <div className="mt-8 flex justify-end">
                        <Button
                          type="submit"
                          disabled={isSubmitting || !isValid || !dirty}
                          className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                        >
                          Save & Continue
                        </Button>
                      </div>
                      <GoodToKnowBox
                        title="Editing my Answers"
                        description="Each topic below is a part of your insurance documents, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you."
                      />

                      <SubCategoryFooterNav
                        leftLabel="Medical Insurance"
                        leftTo="/category/insurance/medical"
                        rightLabel="Review"
                        rightTo="/category/insurance/review"
                      />
                    </Form>
                  );
                }}
              </Formik>
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default LifeInsurance;
