export type SubscriptionType = 'temporary_key' | 'spare_key' | 'all_access_key';

export interface UserAccessInfo {
  _id: string;
  subscriptionType: SubscriptionType;
  allowedCategoryId?: string;
  isOwner: boolean;
  isInvited: boolean;
  userRole?: string;
  userPermissions?: string[];
  emergencyAccessRequested: boolean;
  emergencyAccessRequestedAt?: string;
  emergencyAccessGranted: boolean;
  emergencyAccessGrantedAt?: string;
  emergencyAccessDenied: boolean;
  emergencyAccessDeniedAt?: string;
  role?: any;
}

export interface EmergencyAccessStatus {
  subscriptionType: SubscriptionType;
  isOwner: boolean;
  emergencyAccessRequested: boolean;
  emergencyAccessRequestedAt?: string;
  emergencyAccessGranted: boolean;
  emergencyAccessGrantedAt?: string;
  emergencyAccessDenied: boolean;
  emergencyAccessDeniedAt?: string;
  canRequestAccess: boolean;
  canGrantAccess: boolean;
  canDenyAccess: boolean;
  timeRemaining?: number;
}

export interface KeyHolder {
  _id: string;
  firstName?: string;
  lastName?: string;
  email: string;
  subscriptionType: SubscriptionType;
  emergencyAccessRequested: boolean;
  emergencyAccessRequestedAt?: string;
  emergencyAccessGranted: boolean;
  emergencyAccessGrantedAt?: string;
  emergencyAccessDenied: boolean;
  emergencyAccessDeniedAt?: string;
}

export interface AccessControlState {
  userAccess: UserAccessInfo | null;
  emergencyAccessStatus: EmergencyAccessStatus | null;
  keyHolders: KeyHolder[];
  loading: boolean;
  error: string | null;
  userAccessLoading: boolean;
  emergencyAccessLoading: boolean;
  keyHoldersLoading: boolean;
}

export interface CategoryAccess {
  categoryId: string;
  canAccess: boolean;
  canEdit: boolean;
  reason?: string;
}

export interface AccessControlConfig {
  subscriptionType: SubscriptionType;
  isOwner: boolean;
  allowedCategoryId?: string;
  emergencyAccessGranted: boolean;
} 