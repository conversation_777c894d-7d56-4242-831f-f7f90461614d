import { Button } from '@/components/ui/button';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import { motion } from 'framer-motion';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const QuickStartPage: React.FC = () => {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate(`/category/quickstart/essential`);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="min-h-screen overflow-y-auto bg-background"
    >
      <GradiantHeader
        title="Quick Start"
        showAvatar={true}
      />
      
      <div className="container mx-auto px-4 py-8 max-w-md">
        <div className="w-full max-w-md mx-auto px-4 py-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6 mb-6">
            <h2 className="text-xl font-bold mb-4 text-gray-800">Welcome to Quick Start</h2>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">How Quick Start Works</h3>
              <p className="text-blue-700 text-sm">
                Quick Start helps you fill out essential information that will automatically populate 
                answers in other categories. This saves you time and ensures consistency across your profile.
              </p>
            </div>

            <div className="space-y-4 mb-6">
              <div className="flex items-start space-x-3">
                <div className="bg-green-100 rounded-full p-2">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Essential Information</h4>
                  <p className="text-gray-600 text-sm">Provide basic details like contact information, address, and preferences</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="bg-blue-100 rounded-full p-2">
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Auto-Population</h4>
                  <p className="text-gray-600 text-sm">Your answers will automatically fill in relevant fields across all categories</p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="bg-purple-100 rounded-full p-2">
                  <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">Time Saving</h4>
                  <p className="text-gray-600 text-sm">Complete your profile faster by avoiding duplicate data entry</p>
                </div>
              </div>
            </div>

            <div className="flex flex-col gap-3">
              <Button 
                onClick={handleGetStarted}
                className="bg-[#2BCFD5] hover:bg-[#19bbb5] text-white w-full py-3"
              >
                Get Started
              </Button>
              <Button 
                variant="outline" 
                onClick={() => navigate('/dashboard')}
                className="w-full py-3"
              >
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default QuickStartPage; 