import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

interface ScrollToTopProps {
  /**
   * Whether to use smooth scrolling animation
   * @default true
   */
  smooth?: boolean;
  
  /**
   * Custom scroll behavior - 'auto', 'smooth', or 'instant'
   * @default 'smooth'
   */
  behavior?: ScrollBehavior;
  
  /**
   * Custom top offset (useful if you have a fixed header)
   * @default 0
   */
  topOffset?: number;
  
  /**
   * Whether to scroll to top on every route change
   * @default true
   */
  enabled?: boolean;
}

/**
 * ScrollToTop component that automatically scrolls to the top of the page
 * whenever the route changes in React Router v6+.
 * 
 * This component should be placed inside the Router but outside the Routes
 * to ensure it works for all route changes.
 * 
 * @example
 * // Basic usage
 * <ScrollToTop />
 * 
 * // With custom options
 * <ScrollToTop smooth={false} topOffset={80} />
 */
const ScrollToTop = ({ 
  smooth = true, 
  behavior = 'smooth', 
  topOffset = 0, 
  enabled = true 
}: ScrollToTopProps = {}) => {
  const { pathname } = useLocation();

  useEffect(() => {
    if (!enabled) return;

    // Use setTimeout to ensure the DOM has updated
    const scrollToTop = () => {
      setTimeout(() => {
        window.scrollTo({
          top: topOffset,
          left: 0,
          behavior: smooth ? behavior : 'auto'
        });
      }, 0);
    };

    scrollToTop();
  }, [pathname, smooth, behavior, topOffset, enabled]);

  return null; // This component doesn't render anything
};

export default ScrollToTop; 