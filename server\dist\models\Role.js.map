{"version": 3, "file": "Role.js", "sourceRoot": "", "sources": ["../../src/models/Role.ts"], "names": [], "mappings": ";;;;;AAAA,wDAAgC;AAChC,wCAA4D;AAE5D,MAAM,UAAU,GAAG,IAAI,kBAAQ,CAAC,MAAM,CAAC;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,eAAQ,CAAC;QAC7B,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;KACb;IACD,WAAW,EAAE;QACX,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,IAAI;KACf;IACD,WAAW,EAAE,CAAC;YACZ,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,IAAI;SACf,CAAC;IACF,QAAQ,EAAE;QACR,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,IAAI;KACd;CACF,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;AAEzB,wDAAwD;AACxD,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;AAEnC,qCAAqC;AACrC,UAAU,CAAC,OAAO,CAAC,eAAe,GAAG;IACnC,OAAO;QACL;YACE,IAAI,EAAE,eAAQ,CAAC,KAAK;YACpB,WAAW,EAAE,kEAAkE;YAC/E,WAAW,EAAE;gBACX,YAAY;gBACZ,YAAY;gBACZ,cAAc;gBACd,cAAc;aACf;YACD,QAAQ,EAAE,IAAI;SACf;QACD;YACE,IAAI,EAAE,eAAQ,CAAC,OAAO;YACtB,WAAW,EAAE,6DAA6D;YAC1E,WAAW,EAAE;gBACX,YAAY;aACb;YACD,QAAQ,EAAE,IAAI;SACf;QACD;YACE,IAAI,EAAE,eAAQ,CAAC,MAAM;YACrB,WAAW,EAAE,0DAA0D;YACvE,WAAW,EAAE;gBACX,YAAY;gBACZ,YAAY;gBACZ,cAAc;gBACd,cAAc;aACf;YACD,QAAQ,EAAE,IAAI;SACf;KACF,CAAC;AACJ,CAAC,CAAC;AAEF,2DAA2D;AAC3D,UAAU,CAAC,OAAO,CAAC,aAAa,GAAG,UAAS,UAAkB;IAC5D,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC/C,CAAC,CAAC;AAEF,MAAM,IAAI,GAAG,kBAAQ,CAAC,KAAK,CAAoB,MAAM,EAAE,UAAU,CAAC,CAAC;AAEnE,kBAAe,IAAI,CAAC"}