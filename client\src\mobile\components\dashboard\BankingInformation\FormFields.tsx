import React from 'react';
import { Field } from 'formik';

export interface BankingQuestion {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  options?: string[];
  dependsOn?: {
    questionId: string;
    value: string;
  };
  placeholder?: string;
  [key: string]: any;
}

export const buildInitialValues = (questions: BankingQuestion[]) =>
  questions.reduce((acc, q) => ({ ...acc, [q.id]: "" }), {});

export const validate = (questions: BankingQuestion[]) => (values: Record<string, string>) => {
  const errors: Record<string, string> = {};
  // All fields are optional for banking information
  return errors;
};

export const QuestionItem = ({ 
  question, 
  values 
}: { 
  question: BankingQuestion; 
  values: Record<string, any>;
}) => {
  const renderField = () => {
    switch (question.type) {
      case 'boolean':
        return (
          <div className="flex space-x-4">
            {["Yes", "No"].map(option => (
              <label
                key={option}
                className={
                  "flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer " +
                  (values[question.id] === option
                    ? "bg-[#2BCFD5] text-white border-[#2BCFD5]"
                    : "bg-gray-50 hover:bg-[#25b6bb] hover:text-white")
                }
                style={{ transition: "all 0.2s" }}
              >
                <Field type="radio" name={question.id} value={option} className="hidden" />
                {option}
              </label>
            ))}
          </div>
        );
      
      case 'dropdown':
        return (
          <Field as="select" name={question.id} className="w-full border rounded-lg px-3 py-2">
            <option value="">Select...</option>
            {question.options?.map((option: string) => (
              <option key={option} value={option}>{option}</option>
            ))}
          </Field>
        );
      
      case 'textarea':
        return (
          <Field
            as="textarea"
            name={question.id}
            rows={4}
            className="w-full border rounded-lg px-3 py-2"
            placeholder={question.placeholder}
          />
        );
      
      case 'password':
        return (
          <Field
            name={question.id}
            type="password"
            className="w-full border rounded-lg px-3 py-2"
            placeholder={question.placeholder}
          />
        );
      
      default: // text, number, etc.
        return (
          <Field
            name={question.id}
            type={question.type === 'number' ? 'number' : 'text'}
            className="w-full border rounded-lg px-3 py-2"
            placeholder={question.placeholder}
          />
        );
    }
  };

  return (
    <div className="mb-4">
      <label className="block font-medium text-gray-700 mb-2">
        {question.text}
      </label>
      {renderField()}
    </div>
  );
};
