import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CategoryReviewPage from '@/web/components/Category/CategoryReviewPage';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { useAuth } from '@/contexts/AuthContext';
import importantContactsData from '@/data/importantContacts.json';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  selectUserInputs,
  selectLoading,
  selectError
} from '@/store/slices/importantContactsSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';

// Define interfaces for the data structure
interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

interface UserInput {
  userId: string;
  categoryId: string;
  originalCategoryId: string;
  subCategoryId: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
}

// Map section IDs to their routes
const sectionRoutes: Record<string, string> = {
  '207A': '/category/importantcontacts/friendsfamily',
  '207B': '/category/importantcontacts/work',
  '207C': '/category/importantcontacts/religiousaffiliation',
  '207D': '/category/importantcontacts/clubs',
};

// Map question IDs to their section IDs
const questionToSectionMap: Record<string, string> = {};

// Initialize the question to section mapping
Object.entries(importantContactsData).forEach(([categoryId, questions]) => {
  questions.forEach((question: any) => {
    if (question.sectionId) {
      questionToSectionMap[question.id] = question.sectionId;
    }
  });
});

export default function ImportantContactReview() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);

  // Get data from Redux store
  const userInputs = useAppSelector(selectUserInputs);
  const isLoading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: createUserInfo(user).avatar
  };

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchData = async () => {
          if (user?.id) {
            try {
              const ownerId = await getCachedOwnerIdFromUser(user);
              if (ownerId) {
                dispatch(fetchUserInputs(ownerId));
              } else {
                console.error('No owner ID found for user in HomeInstructionsReview component');
              }
            } catch (error) {
              console.error('Error fetching owner ID in HomeInstructionsReview component:', error);
            }
          }
        };
    
        fetchData();
  }, [dispatch, user]);

  // Process user inputs to create topics for review page
  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      // Transform the data for the review page
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      // Process all user inputs
      userInputs.forEach((userInput) => {
        userInput.answersBySection.forEach((section) => {
          // For each question in this section, group all answers for that question
          const allQuestions = importantContactsData['207'] || [];
          const sectionQuestions = allQuestions.filter((q: any) => q.sectionId === section.originalSectionId);

          sectionQuestions.forEach((questionData: any) => {
            const questionId = questionData.id;
            const answersForQuestion = section.answers.filter(a => a.originalQuestionId === questionId);

            let displayData = 'No answer provided';
            if (questionData.isContacts) {
              // Gather all contacts from all answers
              const allContactAnswers = answersForQuestion.flatMap(a => {
                try {
                  const parsed = JSON.parse(a.answer);
                  if (Array.isArray(parsed)) return parsed;
                  if (parsed && typeof parsed === 'object') return [parsed];
                  return [];
                } catch {
                  return [];
                }
              });
              if (allContactAnswers.length > 0) {
                displayData = allContactAnswers
                  .map((c: any) => {
                    if (typeof c === 'string') return c;
                    if (c && (c.name || c.info || c.phone)) {
                      const phoneNumber = c.phone || c.info || '';
                      return `${c.name || ''}${phoneNumber ? ` (${phoneNumber})` : ''}`;
                    }
                    return null;
                  })
                  .filter(Boolean)
                  .join(', ');
              }
            } else if (answersForQuestion.length > 0) {
              displayData = answersForQuestion.map(a => a.answer).join(', ');
            }

            allTopics.push({
              id: `${questionId}_${section.originalSectionId}`,
              title: questionData.text,
              subtitle: `Section: ${section.originalSectionId}`,
              data: displayData,
              onEdit: () => {
                const route = sectionRoutes[questionToSectionMap[questionId]];
                if (route) {
                  navigate(`${route}?questionId=${questionId}`);
                }
              }
            });
          });
        });
      });

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, navigate]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">Loading your answers...</div>;
  }

  if (error) {
    return <div className="flex justify-center items-center h-screen text-red-500">{error}</div>;
  }

  if (!user || !user.id) {
    return <div className="flex justify-center items-center h-screen text-red-500">You must be logged in to view your answers</div>;
  }

  if (userInputs.length === 0 && !isLoading) {
    return <div className="flex justify-center items-center h-screen">No important contact answers found. Please complete some questions first.</div>;
  }

  return (
    <div className="flex flex-col items-center">
    <CategoryReviewPage
      categoryTitle="Important Contacts"
      infoTitle="How to edit your information"
      infoDescription="Now, you are about to enter details about your important contacts, including friends, family, work contacts, religious affiliations, and club memberships. Each section has several questions. Fill out as much as you can/like. You can always come back to fill out more information later."
      topics={topics}
      user={userInfo}
      onPrint={() => window.print()}
      afterTopics={
        <button
          onClick={() => navigate('/category/socialmedia')}
          className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
        >
          Continue to Social Media
        </button>
      }
    />
    </div>
  );
}