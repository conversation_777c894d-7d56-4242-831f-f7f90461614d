"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const crypto_1 = __importDefault(require("crypto"));
const RequestedCategories_1 = require("../types/RequestedCategories");
const requestedCategoriesSchema = new mongoose_1.default.Schema({
    ownerId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'Owner',
        required: true
    },
    requestedUserId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    categoryIds: [{
            type: String,
            required: true
        }],
    status: {
        type: String,
        enum: Object.values(RequestedCategories_1.RequestStatus),
        default: RequestedCategories_1.RequestStatus.PENDING,
        required: true
    },
    requestMessage: {
        type: String,
        trim: true,
        maxlength: 500
    },
    approvalToken: {
        type: String,
        default: null
    },
    approvalTokenExpire: {
        type: Date,
        default: null
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});
// Add indexes for better query performance
requestedCategoriesSchema.index({ ownerId: 1 });
requestedCategoriesSchema.index({ requestedUserId: 1 });
requestedCategoriesSchema.index({ status: 1 });
requestedCategoriesSchema.index({ ownerId: 1, requestedUserId: 1 });
requestedCategoriesSchema.index({ approvalToken: 1 });
// Virtual to populate owner data
requestedCategoriesSchema.virtual('owner', {
    ref: 'Owner',
    localField: 'ownerId',
    foreignField: '_id',
    justOne: true
});
// Virtual to populate requested user data
requestedCategoriesSchema.virtual('requestedUser', {
    ref: 'User',
    localField: 'requestedUserId',
    foreignField: '_id',
    justOne: true
});
// Note: categoryIds are stored as strings (numeric IDs from frontend)
// No virtual population needed since we store the IDs directly
// Static method to find requests by owner ID
requestedCategoriesSchema.statics.findByOwnerId = function (ownerId) {
    return this.find({ ownerId }).populate('owner').populate('requestedUser');
};
// Static method to find requests by user ID
requestedCategoriesSchema.statics.findByUserId = function (userId) {
    return this.find({ requestedUserId: userId }).populate('owner').populate('requestedUser');
};
// Static method to find pending requests
requestedCategoriesSchema.statics.findPendingRequests = function (ownerId) {
    const query = { status: RequestedCategories_1.RequestStatus.PENDING };
    if (ownerId) {
        query.ownerId = ownerId;
    }
    return this.find(query).populate('owner').populate('requestedUser');
};
// Static method to find requests by owner and categories
requestedCategoriesSchema.statics.findByOwnerAndCategories = function (ownerId, categoryIds) {
    return this.find({
        ownerId,
        categoryIds: { $in: categoryIds }
    }).populate('owner').populate('requestedUser');
};
// Instance method to generate approval token
requestedCategoriesSchema.methods.generateApprovalToken = function () {
    const token = crypto_1.default.randomBytes(32).toString('hex');
    const hashedToken = crypto_1.default.createHash('sha256').update(token).digest('hex');
    this.approvalToken = hashedToken;
    this.approvalTokenExpire = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    return token; // return the raw token (unhashed) to be sent via email
};
const RequestedCategories = mongoose_1.default.model('RequestedCategories', requestedCategoriesSchema);
exports.default = RequestedCategories;
//# sourceMappingURL=RequestedCategories.js.map