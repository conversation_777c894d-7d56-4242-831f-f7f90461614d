import api from './api';
import { 
  UserAccessInfo, 
  EmergencyAccessStatus, 
  KeyHolder,
  SubscriptionType 
} from '@/types/accessControl';

class AccessControlService {
  // Get user access information
  async getUserAccessInfo(): Promise<UserAccessInfo> {
    const response = await api.get('/v1/api/auth/access-info');
    return response.data as UserAccessInfo;
  }

  // Update user subscription
  async updateUserSubscription(
    subscriptionType: SubscriptionType, 
    allowedCategoryId?: string
  ): Promise<{ message: string; user: any }> {
    const response = await api.put('/v1/api/auth/subscription', {
      subscriptionType,
      allowedCategoryId
    });
    return response.data as { message: string; user: any };
  }

  // Request emergency access
  async requestEmergencyAccess(): Promise<{ message: string; requestedAt: string; waitTime: string }> {
    const response = await api.post('/v1/api/emergency-access/request');
    return response.data as { message: string; requestedAt: string; waitTime: string };
  }

  // Grant emergency access (for owners)
  async grantEmergencyAccess(keyHolderId: string): Promise<{ message: string; grantedAt: string }> {
    const response = await api.post('/v1/api/emergency-access/grant', { keyHolderId });
    return response.data as { message: string; grantedAt: string };
  }

  // Deny emergency access (for owners)
  async denyEmergencyAccess(keyHolderId: string): Promise<{ message: string; deniedAt: string }> {
    const response = await api.post('/v1/api/emergency-access/deny', { keyHolderId });
    return response.data as { message: string; deniedAt: string };
  }

  // Get emergency access status
  async getEmergencyAccessStatus(): Promise<EmergencyAccessStatus> {
    const response = await api.get('/v1/api/emergency-access/status');
    return response.data as EmergencyAccessStatus;
  }

  // Get key holders for owner
  async getKeyHoldersForOwner(): Promise<KeyHolder[]> {
    const response = await api.get('/v1/api/emergency-access/key-holders');
    return response.data as KeyHolder[];
  }
}

export const accessControlService = new AccessControlService();
export default accessControlService;
 