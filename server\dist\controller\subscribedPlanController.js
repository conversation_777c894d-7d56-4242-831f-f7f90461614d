"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncUserSubscriptionTypes = exports.renewSubscription = exports.getExpiredSubscriptions = exports.cancelSubscription = exports.changePlan = exports.getOwnerSubscription = exports.getSubscriptionById = exports.getAllSubscriptions = exports.createSubscription = void 0;
const SubscribedPlan_1 = __importDefault(require("../models/SubscribedPlan"));
const PricingPlan_1 = __importDefault(require("../models/PricingPlan"));
const Owner_1 = __importDefault(require("../models/Owner"));
const User_1 = __importDefault(require("../models/User"));
const mongoose_1 = __importDefault(require("mongoose"));
// Create a new subscription
const createSubscription = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { planId, ownerId, currentPlan } = req.body;
        // Use currentPlan as the primary field, fallback to planId
        const selectedPlanId = currentPlan || planId;
        // Validate that the plan exists
        const plan = yield PricingPlan_1.default.findById(selectedPlanId);
        if (!plan) {
            res.status(404).json({
                status: 'fail',
                message: 'Pricing plan not found'
            });
            return;
        }
        // Validate that the owner exists
        const owner = yield Owner_1.default.findById(ownerId);
        if (!owner) {
            res.status(404).json({
                status: 'fail',
                message: 'Owner not found'
            });
            return;
        }
        // Check if owner already has an active subscription
        const existingSubscription = yield SubscribedPlan_1.default.findOne({ ownerId });
        if (existingSubscription) {
            res.status(400).json({
                status: 'fail',
                message: 'Owner already has an active subscription. Use change plan endpoint to modify.'
            });
            return;
        }
        const subscription = new SubscribedPlan_1.default({
            planId: selectedPlanId,
            ownerId,
            currentPlan: selectedPlanId,
            previousPlans: []
        });
        yield subscription.save();
        // Update the owner with the subscription ID
        yield Owner_1.default.findByIdAndUpdate(ownerId, { subscribedPlanId: subscription._id });
        // Update all users associated with this owner to have the new subscription type
        yield User_1.default.updateMany({ ownerId: ownerId }, {
            subscriptionType: plan.type,
            allowedCategoryId: null // Reset allowed category for new subscription type
        });
        // Populate the response with plan and owner details
        const populatedSubscription = yield SubscribedPlan_1.default.findById(subscription._id)
            .populate('currentPlan', 'type price displayPrice tagline duration')
            .populate('ownerId', 'firstName lastName email')
            .populate('previousPlans', 'type price displayPrice');
        res.status(201).json({
            status: 'success',
            data: {
                subscription: populatedSubscription
            }
        });
    }
    catch (error) {
        console.error('Error creating subscription:', error);
        res.status(500).json({
            status: 'error',
            message: 'Error creating subscription',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.createSubscription = createSubscription;
// Get all subscriptions
const getAllSubscriptions = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { active, ownerId } = req.query;
        // Build filter object
        const filter = {};
        if (ownerId) {
            if (!mongoose_1.default.Types.ObjectId.isValid(ownerId)) {
                res.status(400).json({
                    status: 'fail',
                    message: 'Invalid owner ID'
                });
                return;
            }
            filter.ownerId = ownerId;
        }
        let subscriptions = yield SubscribedPlan_1.default.find(filter)
            .populate('currentPlan', 'type price displayPrice tagline duration')
            .populate('ownerId', 'firstName lastName email')
            .populate('previousPlans', 'type price displayPrice')
            .sort({ createdAt: -1 });
        // Filter by active status if specified
        if (active !== undefined) {
            const isActiveFilter = active === 'true';
            subscriptions = subscriptions.filter(sub => sub.isActive === isActiveFilter);
        }
        res.status(200).json({
            status: 'success',
            results: subscriptions.length,
            data: {
                subscriptions
            }
        });
    }
    catch (error) {
        console.error('Error fetching subscriptions:', error);
        res.status(500).json({
            status: 'error',
            message: 'Error fetching subscriptions',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getAllSubscriptions = getAllSubscriptions;
// Get subscription by ID
const getSubscriptionById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({
                status: 'fail',
                message: 'Invalid subscription ID'
            });
            return;
        }
        const subscription = yield SubscribedPlan_1.default.findById(id)
            .populate('currentPlan', 'type price displayPrice tagline duration features')
            .populate('ownerId', 'firstName lastName email')
            .populate('previousPlans', 'type price displayPrice tagline');
        if (!subscription) {
            res.status(404).json({
                status: 'fail',
                message: 'Subscription not found'
            });
            return;
        }
        res.status(200).json({
            status: 'success',
            data: {
                subscription
            }
        });
    }
    catch (error) {
        console.error('Error fetching subscription:', error);
        res.status(500).json({
            status: 'error',
            message: 'Error fetching subscription',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getSubscriptionById = getSubscriptionById;
// Get owner's current subscription
const getOwnerSubscription = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { ownerId } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(ownerId)) {
            res.status(400).json({
                status: 'fail',
                message: 'Invalid owner ID'
            });
            return;
        }
        const subscription = yield SubscribedPlan_1.default.findOne({ ownerId })
            .populate('currentPlan', 'type price displayPrice tagline duration features')
            .populate('ownerId', 'firstName lastName email')
            .populate('previousPlans', 'type price displayPrice tagline');
        if (!subscription) {
            res.status(404).json({
                status: 'fail',
                message: 'No subscription found for this owner'
            });
            return;
        }
        res.status(200).json({
            status: 'success',
            data: {
                subscription,
                isActive: subscription.isActive,
                daysRemaining: subscription.daysRemaining
            }
        });
    }
    catch (error) {
        console.error('Error fetching owner subscription:', error);
        res.status(500).json({
            status: 'error',
            message: 'Error fetching owner subscription',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getOwnerSubscription = getOwnerSubscription;
// Change/upgrade plan
const changePlan = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { ownerId } = req.params;
        const { newPlanId } = req.body;
        if (!mongoose_1.default.Types.ObjectId.isValid(ownerId)) {
            res.status(400).json({
                status: 'fail',
                message: 'Invalid owner ID'
            });
            return;
        }
        // Validate that the new plan exists
        const newPlan = yield PricingPlan_1.default.findById(newPlanId);
        if (!newPlan) {
            res.status(404).json({
                status: 'fail',
                message: 'New pricing plan not found'
            });
            return;
        }
        // Find existing subscription
        const subscription = yield SubscribedPlan_1.default.findOne({ ownerId });
        if (!subscription) {
            res.status(404).json({
                status: 'fail',
                message: 'No active subscription found for this owner'
            });
            return;
        }
        // Add current plan to previous plans array
        subscription.previousPlans.push(subscription.currentPlan);
        // Update current plan
        subscription.currentPlan = newPlanId;
        subscription.planId = newPlanId;
        yield subscription.save(); // This will trigger the pre-save hook to recalculate expiryAt
        // Update all users associated with this owner to have the new subscription type
        yield User_1.default.updateMany({ ownerId: ownerId }, {
            subscriptionType: newPlan.type,
            allowedCategoryId: null // Reset allowed category for new subscription type
        });
        // Populate the response
        const updatedSubscription = yield SubscribedPlan_1.default.findById(subscription._id)
            .populate('currentPlan', 'type price displayPrice tagline duration features')
            .populate('ownerId', 'firstName lastName email')
            .populate('previousPlans', 'type price displayPrice tagline');
        res.status(200).json({
            status: 'success',
            message: 'Plan changed successfully',
            data: {
                subscription: updatedSubscription
            }
        });
    }
    catch (error) {
        console.error('Error changing plan:', error);
        res.status(500).json({
            status: 'error',
            message: 'Error changing plan',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.changePlan = changePlan;
// Cancel subscription (soft delete)
const cancelSubscription = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({
                status: 'fail',
                message: 'Invalid subscription ID'
            });
            return;
        }
        const subscription = yield SubscribedPlan_1.default.findByIdAndDelete(id);
        if (!subscription) {
            res.status(404).json({
                status: 'fail',
                message: 'Subscription not found'
            });
            return;
        }
        // Remove the subscription reference from the owner
        yield Owner_1.default.findByIdAndUpdate(subscription.ownerId, { subscribedPlanId: null });
        // Reset all users associated with this owner back to temporary_key
        yield User_1.default.updateMany({ ownerId: subscription.ownerId }, {
            subscriptionType: 'temporary_key',
            allowedCategoryId: null // Reset allowed category for temporary_key
        });
        res.status(200).json({
            status: 'success',
            message: 'Subscription cancelled successfully'
        });
    }
    catch (error) {
        console.error('Error cancelling subscription:', error);
        res.status(500).json({
            status: 'error',
            message: 'Error cancelling subscription',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.cancelSubscription = cancelSubscription;
// Get expired subscriptions
const getExpiredSubscriptions = (_req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const now = new Date();
        const expiredSubscriptions = yield SubscribedPlan_1.default.find({
            expiryAt: { $lt: now }
        })
            .populate('currentPlan', 'type price displayPrice tagline duration')
            .populate('ownerId', 'firstName lastName email')
            .sort({ expiryAt: -1 });
        res.status(200).json({
            status: 'success',
            results: expiredSubscriptions.length,
            data: {
                expiredSubscriptions
            }
        });
    }
    catch (error) {
        console.error('Error fetching expired subscriptions:', error);
        res.status(500).json({
            status: 'error',
            message: 'Error fetching expired subscriptions',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getExpiredSubscriptions = getExpiredSubscriptions;
// Renew subscription (extend current plan)
const renewSubscription = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        if (!mongoose_1.default.Types.ObjectId.isValid(id)) {
            res.status(400).json({
                status: 'fail',
                message: 'Invalid subscription ID'
            });
            return;
        }
        const subscription = yield SubscribedPlan_1.default.findById(id);
        if (!subscription) {
            res.status(404).json({
                status: 'fail',
                message: 'Subscription not found'
            });
            return;
        }
        // Trigger recalculation of expiry date by modifying updatedAt
        subscription.markModified('updatedAt');
        yield subscription.save(); // This will recalculate expiryAt
        const renewedSubscription = yield SubscribedPlan_1.default.findById(subscription._id)
            .populate('currentPlan', 'type price displayPrice tagline duration features')
            .populate('ownerId', 'firstName lastName email')
            .populate('previousPlans', 'type price displayPrice tagline');
        res.status(200).json({
            status: 'success',
            message: 'Subscription renewed successfully',
            data: {
                subscription: renewedSubscription
            }
        });
    }
    catch (error) {
        console.error('Error renewing subscription:', error);
        res.status(500).json({
            status: 'error',
            message: 'Error renewing subscription',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.renewSubscription = renewSubscription;
// Sync user subscription types with their actual subscriptions
const syncUserSubscriptionTypes = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Get all active subscriptions
        const subscriptions = yield SubscribedPlan_1.default.find()
            .populate('currentPlan', 'type')
            .populate('ownerId', '_id');
        let updatedCount = 0;
        let errors = [];
        for (const subscription of subscriptions) {
            try {
                const plan = subscription.currentPlan;
                const owner = subscription.ownerId;
                if (plan && owner) {
                    // Update all users associated with this owner
                    const result = yield User_1.default.updateMany({ ownerId: owner._id }, {
                        subscriptionType: plan.type,
                        allowedCategoryId: null
                    });
                    updatedCount += result.modifiedCount;
                    console.log(`Updated ${result.modifiedCount} users for owner ${owner._id} to subscription type: ${plan.type}`);
                }
            }
            catch (error) {
                const errorMsg = `Error updating users for subscription ${subscription._id}: ${error}`;
                console.error(errorMsg);
                errors.push(errorMsg);
            }
        }
        // Also reset users who don't have active subscriptions back to temporary_key
        const ownersWithSubscriptions = subscriptions.map(sub => sub.ownerId._id.toString());
        const resetResult = yield User_1.default.updateMany({
            ownerId: {
                $exists: true,
                $ne: null,
                $nin: ownersWithSubscriptions
            }
        }, {
            subscriptionType: 'temporary_key',
            allowedCategoryId: null
        });
        res.status(200).json({
            status: 'success',
            message: 'User subscription types synced successfully',
            data: {
                usersUpdated: updatedCount,
                usersResetToTemporary: resetResult.modifiedCount,
                totalSubscriptionsProcessed: subscriptions.length,
                errors: errors.length > 0 ? errors : undefined
            }
        });
    }
    catch (error) {
        console.error('Error syncing user subscription types:', error);
        res.status(500).json({
            status: 'error',
            message: 'Error syncing user subscription types',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.syncUserSubscriptionTypes = syncUserSubscriptionTypes;
//# sourceMappingURL=subscribedPlanController.js.map