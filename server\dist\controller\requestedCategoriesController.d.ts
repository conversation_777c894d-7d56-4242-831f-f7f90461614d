import { Request, Response } from 'express';
import { AuthRequest } from '../middleware/authMiddleware';
export declare const requestCategories: (req: AuthRequest, res: Response) => Promise<void>;
export declare const getMyRequests: (req: AuthRequest, res: Response) => Promise<void>;
export declare const getOwnerRequests: (req: AuthRequest, res: Response) => Promise<void>;
export declare const approveRequest: (req: Request, res: Response) => Promise<void>;
export declare const approveRequestFromDashboard: (req: AuthRequest, res: Response) => Promise<void>;
