import { createSlice, createAsyncThunk, PayloadAction, createSelector } from '@reduxjs/toolkit';
import userInputService from '../../services/userInputService';
import financialPlansData from '../../data/financialPlans.json';

// Define types for our state
export interface SubCategory {
  id: string;
  title: string;
  questionsCount: number;
}

export interface Question {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  options?: string[];
  dependsOn?: {
    questionId: string;
    value: string;
  };
  placeholder?: string;
  validationRules?: {
    minLength?: number;
    maxLength?: number;
  };
}

export interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

export interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

export interface UserInput {
  userId: string;
  categoryId?: string;
  originalCategoryId: string;
  subCategoryId?: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
  _id?: string;
}

interface FinancialPlansState {
  subcategories: SubCategory[];
  questions: Record<string, Question[]>;
  userInputs: UserInput[];
  loading: boolean;
  error: string | null;
  progressStats: {
    totalQuestions: number;
    answeredQuestions: number;
    completionPercentage: number;
  };
}

// Define initial state
const initialState: FinancialPlansState = {
  subcategories: [
    { id: '501A', title: 'Financial Planner', questionsCount: (financialPlansData['501'] || []).filter(q => q.sectionId === '501A').length },
    { id: '501B', title: 'Investments', questionsCount: (financialPlansData['501'] || []).filter(q => q.sectionId === '501B').length },
    { id: '501C', title: 'Stocks', questionsCount: (financialPlansData['501'] || []).filter(q => q.sectionId === '501C').length },
    { id: '501D', title: 'Bonds', questionsCount: (financialPlansData['501'] || []).filter(q => q.sectionId === '501D').length },
    { id: '501E', title: 'Money Market', questionsCount: (financialPlansData['501'] || []).filter(q => q.sectionId === '501E').length },
  ],
  questions: financialPlansData,
  userInputs: [],
  loading: false,
  error: null,
  progressStats: {
    totalQuestions: Object.values(financialPlansData).reduce(
      (sum, questions) => sum + questions.length, 0
    ),
    answeredQuestions: 0,
    completionPercentage: 0
  }
};

// Create async thunks
export const fetchUserInputs = createAsyncThunk<UserInput[], string>(
  'financialPlans/fetchUserInputs',
  async (userOrOwnerId: string, { rejectWithValue }) => {
    try {
      // Try owner-based fetching first, fallback to user-based
      const response = await userInputService.getUserInputsByCategory(userOrOwnerId, '9', true);
      return response as UserInput[];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user inputs';
      return rejectWithValue(errorMessage);
    }
  }
);

export const saveUserInput = createAsyncThunk<UserInput, Omit<UserInput, '_id'>>(
  'financialPlans/saveUserInput',
  async (userData: Omit<UserInput, '_id'>, { rejectWithValue }) => {
    try {
      const response = await userInputService.saveUserInput(userData);
      return response as UserInput;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save user input';
      return rejectWithValue(errorMessage);
    }
  }
);

export const updateUserInput = createAsyncThunk<
  UserInput,
  { id: string, userData: Omit<UserInput, '_id'> }
>(
  'financialPlans/updateUserInput',
  async ({ id, userData }: { id: string, userData: Omit<UserInput, '_id'> }, { rejectWithValue }) => {
    try {
      const response = await userInputService.updateUserInput(id, userData);
      return response as UserInput;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update user input';
      return rejectWithValue(errorMessage);
    }
  }
);

// Create slice
const financialPlansSlice = createSlice({
  name: 'financialPlans',
  initialState,
  reducers: {
    updateProgressStats: (state) => {
      // Calculate total questions
      const totalQuestions = Object.values(state.questions).reduce(
        (sum, questions) => sum + questions.length, 0
      );

      // Calculate answered questions with deduplication
      const answeredQuestionIds = new Set<string>();
      state.userInputs.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
              answeredQuestionIds.add(answer.originalQuestionId);
            }
          });
        });
      });

      const answeredQuestions = answeredQuestionIds.size;

      // Calculate completion percentage
      const completionPercentage = totalQuestions > 0
        ? Math.round((answeredQuestions / totalQuestions) * 100)
        : 0;

      state.progressStats = {
        totalQuestions,
        answeredQuestions,
        completionPercentage
      };
    },
  },
  extraReducers: (builder) => {
    // Handle fetchUserInputs
    builder.addCase(fetchUserInputs.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchUserInputs.fulfilled, (state, action: PayloadAction<UserInput[]>) => {
      state.loading = false;
      state.userInputs = action.payload;

      // Update progress stats after fetching with deduplication
      const answeredQuestionIds = new Set<string>();
      action.payload.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
              answeredQuestionIds.add(answer.originalQuestionId);
            }
          });
        });
      });

      const answeredQuestions = answeredQuestionIds.size;
      state.progressStats.answeredQuestions = answeredQuestions;
      state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
        ? Math.round((answeredQuestions / state.progressStats.totalQuestions) * 100)
        : 0;
    });
    builder.addCase(fetchUserInputs.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Handle saveUserInput
    builder.addCase(saveUserInput.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(saveUserInput.fulfilled, (state, action: PayloadAction<UserInput>) => {
      state.loading = false;
      state.userInputs.push(action.payload);
      // Update progress stats after saving
      state.progressStats.answeredQuestions += action.payload.answersBySection.reduce(
        (sum, section) => sum + section.answers.length, 0
      );
      state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
        ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
        : 0;
    });
    builder.addCase(saveUserInput.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Handle updateUserInput
    builder.addCase(updateUserInput.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateUserInput.fulfilled, (state, action: PayloadAction<UserInput>) => {
      state.loading = false;
      const index = state.userInputs.findIndex(input => input._id === action.payload._id);
      if (index !== -1) {
        // Update existing user input
        state.userInputs[index] = action.payload;
      }
      // Recalculate progress stats
      state.progressStats.answeredQuestions = state.userInputs.reduce(
        (sum, input) => sum + input.answersBySection.reduce(
          (sectionSum, section) => sectionSum + section.answers.length, 0
        ), 0
      );
      state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
        ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
        : 0;
    });
    builder.addCase(updateUserInput.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
  },
});

export const { updateProgressStats } = financialPlansSlice.actions;

// Basic selectors
export const selectFinancialPlansState = (state: { financialPlans: FinancialPlansState }) =>
  state.financialPlans;

export const selectSubcategories = createSelector(
  [selectFinancialPlansState],
  (financialPlans) => financialPlans.subcategories
);

export const selectQuestions = createSelector(
  [selectFinancialPlansState],
  (financialPlans) => financialPlans.questions
);

export const selectUserInputs = createSelector(
  [selectFinancialPlansState],
  (financialPlans) => financialPlans.userInputs
);

export const selectLoading = createSelector(
  [selectFinancialPlansState],
  (financialPlans) => financialPlans.loading
);

export const selectError = createSelector(
  [selectFinancialPlansState],
  (financialPlans) => financialPlans.error
);

export const selectProgressStats = createSelector(
  [selectFinancialPlansState],
  (financialPlans) => financialPlans.progressStats
);

// Memoized selectors with parameters
export const selectSubcategoryById = (subcategoryId: string) =>
  createSelector(
    [selectSubcategories],
    (subcategories) => subcategories.find(subcategory => subcategory.id === subcategoryId)
  );

export const selectQuestionsBySubcategoryId = (subcategoryId: string) =>
  createSelector(
    [selectQuestions],
    (questions) => questions[subcategoryId] || []
  );

// New selector to filter questions by sectionId within the 501 array
export const selectQuestionsBySectionId = (sectionId: string) =>
  createSelector(
    [selectQuestions],
    (questions) => {
      // Get all questions from the 501 array and filter by sectionId
      const allQuestions = questions['501'] || [];
      return allQuestions.filter(question => question.sectionId === sectionId);
    }
  );

export const selectUserInputsBySubcategoryId = (subcategoryId: string) =>
  createSelector(
    [selectUserInputs],
    (userInputs) => userInputs.filter(input => input.originalSubCategoryId === subcategoryId)
  );

export default financialPlansSlice.reducer;
