import { Formik, Field, Form, ErrorMessage } from "formik";
import GradiantHeader from "@/mobile/components/header/gradiantHeader";
import Footer from "@/mobile/components/layout/Footer";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAuth } from '@/contexts/AuthContext';
import { useState, useEffect } from "react";
import { Alert, AlertDescription } from '@/components/ui/alert';
import ScrollToQuestion from '@/mobile/components/dashboard/HomeInstructions/ScrollToQuestion';
import { castToQuestionType } from '@/mobile/utils/questionUtils';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  UserInput,
  selectUserInputsBySubcategoryId,
  selectQuestionsBySubcategoryId,
  selectLoading,
  selectError
} from '@/store/slices/homeInstructionsSlice';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Initial values for Home Location fields (adjust keys as per your JSON)
const initialValues = {
  l1: "",
  l2: "",
  l3: "",
  l4: "",
  l5: "",
};

export default function HomeLocationInstructionsPage() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { categoryName } = useParams<{ categoryName: string }>();
  const [savedAnswers, setSavedAnswers] = useState<Record<string, any>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [formError, setError] = useState<string | null>(null);
  const { user } = useAuth();

  // Get data from Redux store
  const locationQuestions = useAppSelector((state) => selectQuestionsBySubcategoryId('105')(state));
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId('105')(state));
  const isLoading = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Cast questions to the correct type - memoize to prevent recalculation on every render
  const typedQuestions = useState(() => castToQuestionType(locationQuestions))[0];

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in HomeLocationInstructionsPage component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in HomeLocationInstructionsPage component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (userInputs && userInputs.length > 0) {
      // Use the first matching record
      const userInput = userInputs[0];

      // Only update state if we have a new ID or if it's the first time
      if (userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);

        // Convert the saved answers to form values
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      } else if (!existingInputId && userInput._id) {
        // First time setting the ID
        setExistingInputId(userInput._id);

        // Convert the saved answers to form values
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      }
    }
  }, [userInputs, existingInputId]);

  // Handle target question in a separate effect to avoid infinite loops
  useEffect(() => {
    if (targetQuestionId && typedQuestions.length > 0) {
      // Find the question and scroll to it if needed
      const questionElement = document.getElementById(`question-${targetQuestionId}`);
      if (questionElement) {
        questionElement.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [targetQuestionId]);

  if (isLoading) {
    return (
      <>
        <GradiantHeader title="Home Instructions"
        showAvatar={true}
        />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  return (
    <>
      <GradiantHeader title="Home Instructions"
       showAvatar={true}
      />
      <div style={{ padding: 16 }}>
        {/* Tab Bar */}
        <div className="flex gap-2 mb-4 bg-gray-50 rounded-lg p-1">
          {categoryTabsConfig.homeinstructions.map(tab => {
            const isActive = tab.label === "Home Location";
            return (
              <button
                key={tab.label}
                type="button"
                className={
                  "flex-1 py-2 rounded-md font-medium " +
                  (isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500 hover:bg-[#25b6bb] hover:text-white")
                }
                disabled={isActive}
                onClick={() => {
                  if (!isActive) navigate(tab.path);
                }}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {(formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        <Formik
          initialValues={Object.keys(savedAnswers).length > 0 ? savedAnswers : initialValues}
          validate={(values: Record<string, string>) => {
            const errors: Record<string, string> = {};
            // Only validate non-display type questions
            typedQuestions.forEach(question => {
              if (question.type !== 'display' && !values[question.id]) {
                errors[question.id] = "Required";
              }
            });
            return errors;
          }}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              // Check if user is authenticated
              if (!user || !user.id) {
                setError('You must be logged in to save answers');
                return;
              }

              // Format the answers for the backend
              const answers = Object.entries(values)
                .filter(([_, value]) => value !== "")
                .map(([key, value], index) => {
                  const question = typedQuestions.find(q => q.id === key);
                  return {
                    index,
                    originalQuestionId: key,
                    question: question?.text || key,
                    type: question?.type || "text",
                    answer: value
                  };
                });

              // Format the answers by section
              // Group by sectionId from questions
              const answersBySection: Record<string, any[]> = {};
              answers.forEach(ans => {
                const question = typedQuestions.find(q => q.id === ans.originalQuestionId);
                const sectionId = question?.sectionId || '105A';
                if (!answersBySection[sectionId]) answersBySection[sectionId] = [];
                answersBySection[sectionId].push(ans);
              });
              const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
                originalSectionId: sectionId,
                isCompleted: true,
                answers
              }));

              // Check if we're updating an existing record or creating a new one
              if (existingInputId) {
                try {
                  await dispatch(updateUserInput({
                    id: existingInputId,
                    userData: {
                      userId: user.id,
                      categoryId: generateObjectId(),
                      originalCategoryId: '1',
                      subCategoryId: generateObjectId(),
                      originalSubCategoryId: '105',
                      answersBySection: formattedAnswersBySection
                    } as UserInput
                  })).unwrap();
                  setSubmitting(false);
                  navigate(`/category/homeinstructions/pets`);
                  return;
                } catch (error) {
                  setExistingInputId(null);
                }
              }

              // If no existing record or update failed, create a new one
              if (!existingInputId) {
                const userData: Omit<UserInput, '_id'> = {
                  userId: user.id,
                  categoryId: generateObjectId(),
                  originalCategoryId: '1',
                  subCategoryId: generateObjectId(),
                  originalSubCategoryId: '105',
                  answersBySection: formattedAnswersBySection
                };
                const result = await dispatch(saveUserInput(userData)).unwrap();
                if (result && result._id) {
                  setExistingInputId(result._id);
                }
                setSubmitting(false);
                navigate(`/category/homeinstructions/pets`);
              }
            } catch (err: any) {
              setError(err.message || 'Failed to save your answers. Atleast one question must be answered.');
              setSubmitting(false);
            }
          }}
        >
          {({ values, isSubmitting, setFieldValue }) => (
            <Form>
              <div className="bg-gray-50 p-5 rounded-xl shadow-sm border">
                <div className="flex items-center justify-between">
                  <p className="text-lg font-semibold">
                    Home Instructions: <span className="text-[#2BCFD5]">Home Location</span>
                  </p>
                  <CircularProgress 
                    value={1} 
                    max={1} 
                    size={40} 
                    stroke={3}
                    color="#2BCFD5"
                  />
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-xl shadow-sm border mt-4">
                <ScrollToQuestion questions={typedQuestions}>
                  {(refs) => (
                    <>
                      {typedQuestions.map((question, idx) => (
                        <div
                          key={question.id}
                          id={`question-${question.id}`}
                          ref={(el: HTMLDivElement | null) => {
                            refs[question.id] = el;
                          }}
                        >
                          {question.type === 'display' ? (
                            <div className="text-lg font-medium text-gray-700 mb-4">
                              {question.text}
                            </div>
                          ) : (
                            <>
                              <label className="block font-medium text-gray-700 mb-2">
                                {question.text}
                              </label>
                              <Field
                                name={question.id}
                                type="text"
                                className="w-full border rounded-lg px-3 py-2 mb-2"
                                placeholder={question.placeholder || question.text}
                              />
                              <ErrorMessage name={question.id} component="div" className="text-red-500 text-sm mt-1" />
                            </>
                          )}
                        </div>
                      ))}
                    </>
                  )}
                </ScrollToQuestion>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold mt-6 hover:bg-[#25b6bb]"
                >
                  Save
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <Footer />
    </>
  );
} 