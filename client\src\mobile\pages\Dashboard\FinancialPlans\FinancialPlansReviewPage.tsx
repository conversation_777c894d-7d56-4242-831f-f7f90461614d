import { useState, useEffect } from 'react';
import { useNavigate, useParams } from "react-router-dom";
import GradiantHeader from "@/mobile/components/header/gradiantHeader";
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import CategoryReviewPage from '@/mobile/components/category/CategoryReviewPage';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  selectUserInputs,
  selectQuestions,
  selectLoading,
  selectError
} from '@/store/slices/financialPlansSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Button } from '@/components/ui/button';

// Map subcategory IDs to their routes
const subcategoryRoutes: Record<string, string> = {
  '501A': 'financialplanner',
  '501B': 'investments',
  '501C': 'stocks',
  '501D': 'bonds',
  '501E': 'moneymarket',
};

interface Topic {
  id: string;
  title: string;
  subtitle?: string;
  data: string;
  onEdit: () => void;
}

export default function FinancialPlansReviewPage() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const { categoryName } = useParams();
  const [topics, setTopics] = useState<Topic[]>([]);
  const [error, setError] = useState<string | null>(null);

  const userInputs = useAppSelector(selectUserInputs);
  const allQuestions = useAppSelector(selectQuestions);
  const isLoading = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in FinancialPlansReviewPage component');
            setError('Unable to load user data');
          }
        } catch (error) {
          console.error('Error fetching owner ID in FinancialPlansReviewPage component:', error);
          setError('Failed to load user data');
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Handle navigation to edit a specific question
  const handleEditQuestion = (questionId: string, subcategoryId: string) => {
    const route = subcategoryRoutes[subcategoryId];
    if (route) {
      const basePath = `/category/${categoryName || 'financialplans'}/${route}`;
      navigate(`${basePath}?questionId=${questionId}`);
    }
  };

  // Process user inputs to create topics for review
  useEffect(() => {
    if (!user?.id) {
      return;
    }

    if (!isLoading) {
      const allTopics: Topic[] = [];

      if (userInputs.length > 0) {
        // Process each user input
        userInputs.forEach((userInput) => {
          userInput.answersBySection.forEach((section) => {
            section.answers.forEach((answer) => {
              // Skip empty answers
              if (!answer.answer || answer.answer.trim() === '') {
                return;
              }

              let displayData = answer.answer;
              let shouldDisplay = true;

              // Handle contacts data specially - following ImportantContacts pattern
              try {
                const parsed = JSON.parse(answer.answer);
                if (Array.isArray(parsed)) {
                  // Filter out empty contacts
                  const validContacts = parsed.filter((c: any) =>
                    c && (c.name?.trim() || c.phone?.trim() || c.info?.trim())
                  );
                  if (validContacts.length > 0) {
                    displayData = validContacts.map(contact => {
                      const phoneNumber = contact.phone || contact.info || '';
                      return `${contact.name || ''}${phoneNumber ? ` (${phoneNumber})` : ''}`;
                    }).filter(Boolean).join(', ');
                  } else {
                    shouldDisplay = false; // Don't show empty contact arrays
                  }
                } else if (parsed && typeof parsed === 'object' && (parsed.name || parsed.phone || parsed.info)) {
                  // Single contact object
                  const phoneNumber = parsed.phone || parsed.info || '';
                  displayData = `${parsed.name || ''}${phoneNumber ? ` (${phoneNumber})` : ''}`;
                } else {
                  shouldDisplay = false; // Don't show invalid contact data
                }
              } catch {
                // If parsing fails, check if it's just "[]" string or empty
                if (answer.answer.trim() === '[]' || answer.answer.trim() === '') {
                  shouldDisplay = false;
                }
              }

              // Only add to topics if we should display it
              if (shouldDisplay) {
                allTopics.push({
                  id: answer.originalQuestionId,
                  title: answer.question,
                  subtitle: `Category: ${getSubcategoryName(section.originalSectionId)}`,
                  data: displayData,
                  onEdit: () => handleEditQuestion(answer.originalQuestionId, section.originalSectionId)
                });
              }
            });
          });
        });
      }

      // If we didn't find any answers, use the questions as a template
      if (allTopics.length === 0) {
        const allQuestionsFlat = allQuestions['501'] || [];
        allQuestionsFlat.forEach((q) => {
          const subcategoryId = q.sectionId;
          if (subcategoryId) {
            allTopics.push({
              id: q.id,
              title: q.text,
              subtitle: `Category: ${getSubcategoryName(subcategoryId)}`,
              data: "No answer provided",
              onEdit: () => handleEditQuestion(q.id, subcategoryId)
            });
          }
        });
        if (allTopics.length === 0) {
          categoryTabsConfig.financialplans.forEach((section) => {
            const subcategoryId = getSubcategoryIdFromLabel(section.label);
            if (subcategoryId) {
              allTopics.push({
                id: subcategoryId,
                title: section.label,
                subtitle: 'No answers provided yet',
                data: 'Click edit to add your information',
                onEdit: () => navigate(section.path)
              });
            }
          });
        }
      }

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, allQuestions, navigate, categoryName]);

  // Helper function to get subcategory name from ID
  const getSubcategoryName = (subcategoryId: string): string => {
    switch (subcategoryId) {
      case '501A': return 'Financial Planner';
      case '501B': return 'Investments';
      case '501C': return 'Stocks';
      case '501D': return 'Bonds';
      case '501E': return 'Money Market';
      default: return 'Unknown';
    }
  };

  // Helper function to get subcategory ID from label
  const getSubcategoryIdFromLabel = (label: string): string | null => {
    switch (label) {
      case 'Financial Planner': return '501A';
      case 'Investments': return '501B';
      case 'Stocks': return '501C';
      case 'Bonds': return '501D';
      case 'Money Market': return '501E';
      default: return null;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Financial Plans Review" showAvatar={true} />
        <div className="container mx-auto px-4 py-6 text-center">
          Loading your financial plans information...
        </div>
      </div>
    );
  }

  if (error || reduxError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Financial Plans Review" showAvatar={true} />
        <div className="container mx-auto px-4 py-6">
          <Alert variant="destructive">
            <AlertDescription>{error || reduxError}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <CategoryReviewPage
      categoryTitle="Financial Plans"
      infoTitle="How to edit your information"
      infoDescription="Review the details about your financial plans. Tap Edit on any item to update it."
      topics={topics}
      onPrint={() => window.print()}
      afterTopics={
        <Button 
          onClick={() => navigate('/dashboard')}
          className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
        >
          Continue to Dashboard
        </Button>
      }
    />
  );
}
