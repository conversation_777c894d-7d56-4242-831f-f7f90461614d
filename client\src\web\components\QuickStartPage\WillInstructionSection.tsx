import React, { useEffect, useState } from 'react';
import { Formik, Form, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { Button } from '@/components/ui/button';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  UserInput,
  selectQuestionsBySubcategoryId
} from '@/store/slices/willInstructionsSlice';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import {
  Question,
  QuestionItem,
  buildValidationSchema,
  generateInitialValues,
  handleDependentAnswers
} from '@/web/components/Category/WillInstructions/FormFields';
import ScrollToQuestion from '@/web/components/Category/WillInstructions/ScrollToQuestion';
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';

const WillInstructionSection: React.FC = () => {
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingLocationInputId, setExistingLocationInputId] = useState<string | null>(null);
  const [existingLegalInputId, setExistingLegalInputId] = useState<string | null>(null);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  // Get questions from both 105A (will location) and 105B (lawyer info) sections
  const locationQuestions = useAppSelector(selectQuestionsBySubcategoryId('105A'));
  const legalQuestions = useAppSelector(selectQuestionsBySubcategoryId('105B'));
  const allQuestions = [...locationQuestions, ...legalQuestions];

  const loading = useAppSelector((state: any) => state.willInstructions.loading);
  const userInputs = useAppSelector((state: any) => state.willInstructions.userInputs);

  // Fetch user inputs on mount
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (ownerId) dispatch(fetchUserInputs(ownerId));
      }
    };
    fetchData();
  }, [dispatch, user]);

  // Process user inputs to get saved answers
  useEffect(() => {
    if (!loading) {
      let combinedAnswers = {};
      let locationInputId: string | null = null;
      let legalInputId: string | null = null;
      
      if (userInputs.length > 0) {
        // Get saved answers for Location (105A)
        const locationInput = userInputs.find((input: UserInput) => input.originalSubCategoryId === '105A');
        if (locationInput) {
          combinedAnswers = { ...combinedAnswers, ...convertUserInputToFormValues(locationInput) };
          locationInputId = locationInput._id || null;
        }
        
        // Get saved answers for Legal (105B)
        const legalInput = userInputs.find((input: UserInput) => input.originalSubCategoryId === '105B');
        if (legalInput) {
          combinedAnswers = { ...combinedAnswers, ...convertUserInputToFormValues(legalInput) };
          legalInputId = legalInput._id || null;
        }
      }
      
      setSavedAnswers(combinedAnswers);
      setExistingLocationInputId(locationInputId);
      setExistingLegalInputId(legalInputId);
      setIsDataLoaded(true);
    }
  }, [userInputs, loading]);

  // Handle form submission
  const handleSubmit = async (
    values: Record<string, string>,
    { setSubmitting }: FormikHelpers<Record<string, string>>
  ) => {
    try {
      if (!user || !user.id) throw new Error('You must be logged in to save answers');
      const ownerId = await getCachedOwnerIdFromUser(user);

      // Split answers by section
      const locationSectionIds = ['105A'];
      const legalSectionIds = ['105B'];
      const answersBySection: Record<string, Array<any>> = {};
      
      allQuestions.forEach((q) => {
        const question = q as import('@/web/components/Category/WillInstructions/FormFields').Question;
        if (!answersBySection[question.sectionId]) {
          answersBySection[question.sectionId] = [];
        }
        const answer = values[question.id];
        if (answer) {
          answersBySection[question.sectionId].push({
            index: answersBySection[question.sectionId].length,
            originalQuestionId: question.id,
            question: question.text,
            type: question.type,
            answer,
          });
        }
      });

      // Prepare data for Location (105A)
      const locationAnswersBySection = Object.entries(answersBySection)
        .filter(([sectionId]) => locationSectionIds.includes(sectionId))
        .map(([sectionId, answers]) => ({
          originalSectionId: sectionId,
          isCompleted: true,
          answers: answers as any[],
        }));

      // Prepare data for Legal (105B)
      const legalAnswersBySection = Object.entries(answersBySection)
        .filter(([sectionId]) => legalSectionIds.includes(sectionId))
        .map(([sectionId, answers]) => ({
          originalSectionId: sectionId,
          isCompleted: true,
          answers: answers as any[],
        }));

      // Save/update Location input (105A)
      if (locationAnswersBySection.length > 0) {
        if (existingLocationInputId) {
          await dispatch(
            updateUserInput({
              id: existingLocationInputId,
              userData: {
                userId: user.id,
                categoryId: generateObjectId(),
                originalCategoryId: '3',
                subCategoryId: generateObjectId(),
                originalSubCategoryId: '105A',
                answersBySection: locationAnswersBySection,
              } as UserInput,
            })
          ).unwrap();
        } else {
          const userData: Omit<UserInput, '_id'> = {
            userId: user.id,
            categoryId: generateObjectId(),
            originalCategoryId: '3',
            subCategoryId: generateObjectId(),
            originalSubCategoryId: '105A',
            answersBySection: locationAnswersBySection,
          };
          await dispatch(saveUserInput(userData)).unwrap();
        }
      }

      // Save/update Legal input (105B)
      if (legalAnswersBySection.length > 0) {
        if (existingLegalInputId) {
          await dispatch(
            updateUserInput({
              id: existingLegalInputId,
              userData: {
                userId: user.id,
                categoryId: generateObjectId(),
                originalCategoryId: '3',
                subCategoryId: generateObjectId(),
                originalSubCategoryId: '105B',
                answersBySection: legalAnswersBySection,
              } as UserInput,
            })
          ).unwrap();
        } else {
          const userData: Omit<UserInput, '_id'> = {
            userId: user.id,
            categoryId: generateObjectId(),
            originalCategoryId: '3',
            subCategoryId: generateObjectId(),
            originalSubCategoryId: '105B',
            answersBySection: legalAnswersBySection,
          };
          await dispatch(saveUserInput(userData)).unwrap();
        }
      }

      // Force re-fetch to update Redux state everywhere
      if (ownerId) {
        await dispatch(fetchUserInputs(ownerId));
      }

      setSubmitting(false);
    } catch (error) {
      setSubmitting(false);
    }
  };

  // Show loading until both questions and user data are loaded
  if (allQuestions.length === 0 || loading || !isDataLoaded) {
    return <div className="flex justify-center items-center h-40">Loading...</div>;
  }

  // Cast allQuestions to the correct type for FormFields helpers
  const typedQuestions = allQuestions as import('@/web/components/Category/WillInstructions/FormFields').Question[];

  const validationSchema = Yup.object(buildValidationSchema(typedQuestions, Yup));
  const baseInitialValues = generateInitialValues(typedQuestions);
  const initialValues = { ...baseInitialValues, ...savedAnswers };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
      {/* <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">Will & Testament Information</h3>
        <p className="text-sm text-gray-600">
          This section includes both Will Location details and Lawyer/Attorney information to ensure your will is properly documented and accessible.
        </p>
      </div> */}
      
      <Formik
        key={`${isDataLoaded}-${JSON.stringify(savedAnswers)}`}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize={true}
      >
        {({ values, isSubmitting, isValid, dirty, setValues }) => {
          const handleFieldChange = (fieldId: string, value: string) => {
            const newValues = { ...values, [fieldId]: value };
            const updatedValues = handleDependentAnswers(newValues, typedQuestions);
            setValues(updatedValues);
          };

          return (
            <Form>
              <ScrollToQuestion questions={typedQuestions}>
                {(refs) => (
                  <>
                    {[...typedQuestions]
                      .sort((a, b) => a.order - b.order)
                      .map((question) => (
                        <div
                          key={question.id}
                          id={`question-${question.id}`}
                          ref={(el: HTMLDivElement | null) => {
                            refs[question.id] = el;
                          }}
                        >
                          <QuestionItem
                            question={question}
                            formValues={values}
                            onChange={(value: string) => handleFieldChange(question.id, value)}
                          />
                        </div>
                      ))}
                  </>
                )}
              </ScrollToQuestion>
              <div className="mt-8 flex justify-end">
                <Button
                  type="submit"
                  disabled={isSubmitting || !isValid || !dirty}
                  className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                >
                  Save & Continue
                </Button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default WillInstructionSection; 