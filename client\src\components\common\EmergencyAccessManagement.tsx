import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Clock, AlertTriangle, CheckCircle, XCircle, User } from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';

interface EmergencyAccessManagementProps {
  className?: string;
}

export const EmergencyAccessManagement: React.FC<EmergencyAccessManagementProps> = ({ className }) => {
  const { 
    userAccess, 
    keyHolders, 
    handleGrantEmergencyAccess,
    handleDenyEmergencyAccess,
    loading 
  } = useAccessControl();

  if (!userAccess || !userAccess.isOwner || userAccess.subscriptionType !== 'spare_key') {
    return null;
  }

  const pendingRequests = keyHolders.filter(kh => 
    kh.emergencyAccessRequested && 
    !kh.emergencyAccessGranted && 
    !kh.emergencyAccessDenied
  );

  const grantedRequests = keyHolders.filter(kh => kh.emergencyAccessGranted);
  const deniedRequests = keyHolders.filter(kh => kh.emergencyAccessDenied);

  const formatTimeElapsed = (requestedAt: string): string => {
    const requested = new Date(requestedAt).getTime();
    const now = Date.now();
    const elapsed = now - requested;
    const hours = Math.floor(elapsed / (1000 * 60 * 60));
    const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m ago`;
  };

  const getTimeRemaining = (requestedAt: string): number => {
    const requested = new Date(requestedAt).getTime();
    const now = Date.now();
    const elapsed = now - requested;
    // const waitTime = 24 * 60 * 60 * 1000; // 24 hours
    const waitTime = 24 * 60 * 60 * 1000; // 24 hours in ms
    return Math.max(0, waitTime - elapsed);
  };

  const renderKeyHolder = (keyHolder: any) => {
    const timeRemaining = getTimeRemaining(keyHolder.emergencyAccessRequestedAt);
    const isAutoGranted = timeRemaining === 0 && !keyHolder.emergencyAccessGranted && !keyHolder.emergencyAccessDenied;

    return (
      <div key={keyHolder._id} className="border rounded-lg p-4 space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-gray-500" />
            <span className="font-medium">
              {keyHolder.firstName} {keyHolder.lastName}
            </span>
            <span className="text-sm text-gray-500">({keyHolder.email})</span>
            {keyHolder.role && (
              <span className="ml-2 px-2 py-0.5 rounded bg-blue-100 text-blue-800 text-xs font-semibold">
                {keyHolder.role}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {keyHolder.emergencyAccessGranted && (
              <Badge variant="default" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Granted
              </Badge>
            )}
            {keyHolder.emergencyAccessDenied && (
              <Badge variant="destructive">
                <XCircle className="h-3 w-3 mr-1" />
                Denied
              </Badge>
            )}
            {keyHolder.emergencyAccessRequested && !keyHolder.emergencyAccessGranted && !keyHolder.emergencyAccessDenied && (
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                <Clock className="h-3 w-3 mr-1" />
                Pending
              </Badge>
            )}
          </div>
        </div>

        {keyHolder.emergencyAccessRequested && (
          <div className="text-sm text-gray-600">
            Requested: {formatTimeElapsed(keyHolder.emergencyAccessRequestedAt)}
            {timeRemaining > 0 && !keyHolder.emergencyAccessGranted && (
              <div className="mt-1">
                Auto-grant in: {Math.floor(timeRemaining / (1000 * 60 * 60))} hr {Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60))} min
              </div>
            )}
          </div>
        )}

        {keyHolder.emergencyAccessRequested && 
         !keyHolder.emergencyAccessGranted && 
         !keyHolder.emergencyAccessDenied && (
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={() => handleGrantEmergencyAccess(keyHolder._id)}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700"
            >
              Grant Access
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={() => handleDenyEmergencyAccess(keyHolder._id)}
              disabled={loading}
            >
              Deny Access
            </Button>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          Emergency Access Management
        </CardTitle>
        <CardDescription>
          Manage emergency access requests from your key holders.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {pendingRequests.length === 0 && grantedRequests.length === 0 && deniedRequests.length === 0 && (
          <Alert>
            <AlertDescription>
              No emergency access requests at this time.
            </AlertDescription>
          </Alert>
        )}

        {pendingRequests.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-medium text-lg">Pending Requests ({pendingRequests.length})</h3>
            <div className="space-y-3">
              {pendingRequests.map(renderKeyHolder)}
            </div>
          </div>
        )}

        {grantedRequests.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-medium text-lg">Granted Access ({grantedRequests.length})</h3>
            <div className="space-y-3">
              {grantedRequests.map(renderKeyHolder)}
            </div>
          </div>
        )}

        {deniedRequests.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-medium text-lg">Denied Requests ({deniedRequests.length})</h3>
            <div className="space-y-3">
              {deniedRequests.map(renderKeyHolder)}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 