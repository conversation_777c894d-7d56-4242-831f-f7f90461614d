import defaultAvatar from '@/assets/global/defaultAvatar/defaultImage.jpg';

export const getAvatarUrl = (userImage?: string): string => {
  if (!userImage || userImage.trim() === '') {
    return defaultAvatar;
  }
  
  // If it's already a full URL, return as is
  if (userImage.startsWith('http')) {
    return userImage;
  }
  
  // Validate that the image path doesn't contain invalid characters
  const cleanImagePath = userImage.replace(/[^a-zA-Z0-9._-]/g, '');
  
  // Construct the full URL for uploaded images
  const fullUrl = `${import.meta.env.VITE_API_URL}/uploads/${cleanImagePath}`;
  
  return fullUrl;
};

export const createUserInfo = (user: any) => {
  return {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: getAvatarUrl(user?.image)
  };
}; 