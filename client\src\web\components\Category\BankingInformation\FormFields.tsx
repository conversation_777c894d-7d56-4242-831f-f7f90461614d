import { useField } from 'formik';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import * as Yup from 'yup';

export interface BaseQuestion {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  isAnswered?: boolean;
  answer?: any;
  dependsOn?: {
    questionId: string;
    value: string;
  };
}

export interface TextQuestion extends BaseQuestion {
  type: 'text';
  validationRules?: {
    minLength?: number;
    maxLength?: number;
  };
  placeholder?: string;
}

export interface PasswordQuestion extends BaseQuestion {
  type: 'password';
  placeholder?: string;
}

export interface BooleanQuestion extends BaseQuestion {
  type: 'boolean';
}

export interface DropdownQuestion extends BaseQuestion {
  type: 'dropdown';
  options: string[];
}

export interface TextareaQuestion extends BaseQuestion {
  type: 'textarea';
  placeholder?: string;
}

export type Question = TextQuestion | PasswordQuestion | BooleanQuestion | DropdownQuestion | TextareaQuestion;

// Custom form field components for Formik
export const TextField = ({ question }: { question: TextQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    helpers.setValue(value);
    helpers.setTouched(true);
  };

  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Input
        id={question.id}
        type="text"
        placeholder={question.placeholder}
        {...field}
        onChange={handleChange}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const PasswordField = ({ question }: { question: PasswordQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    helpers.setValue(value);
    helpers.setTouched(true);
  };

  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Input
        id={question.id}
        type="password"
        placeholder={question.placeholder}
        {...field}
        onChange={handleChange}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const TextareaField = ({ question }: { question: TextareaQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    helpers.setValue(value);
    helpers.setTouched(true);
  };

  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Textarea
        id={question.id}
        placeholder={question.placeholder}
        {...field}
        onChange={handleChange}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
        rows={4}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const BooleanField = ({ question, onChange }: { question: BooleanQuestion; onChange: (value: string) => void }) => {
  const [field, meta, helpers] = useField(question.id);

  const handleValueChange = (value: string) => {
    helpers.setValue(value);
    helpers.setTouched(true);
    onChange(value);
  };

  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium">
        {question.text}
      </Label>
      <RadioGroup
        value={field.value || ''}
        onValueChange={handleValueChange}
        className="flex flex-row space-x-6"
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="Yes" id={`${question.id}-yes`} />
          <Label htmlFor={`${question.id}-yes`}>Yes</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="No" id={`${question.id}-no`} />
          <Label htmlFor={`${question.id}-no`}>No</Label>
        </div>
      </RadioGroup>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const DropdownField = ({ question, onChange }: { question: DropdownQuestion; onChange: (value: string) => void }) => {
  const [field, meta, helpers] = useField(question.id);

  const handleValueChange = (value: string) => {
    helpers.setValue(value);
    helpers.setTouched(true);
    onChange(value);
  };

  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium">
        {question.text}
      </Label>
      <Select value={field.value || ''} onValueChange={handleValueChange}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select an option..." />
        </SelectTrigger>
        <SelectContent>
          {question.options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

// Question item component that renders the appropriate field type
export const QuestionItem = ({ question, formValues, onChange }: {
  question: Question;
  formValues: Record<string, any>;
  onChange: (value: string) => void;
}) => {
  const renderField = () => {
    switch (question.type) {
      case 'text':
        return <TextField question={question as TextQuestion} />;
      case 'password':
        return <PasswordField question={question as PasswordQuestion} />;
      case 'boolean':
        return <BooleanField question={question as BooleanQuestion} onChange={onChange} />;
      case 'dropdown':
        return <DropdownField question={question as DropdownQuestion} onChange={onChange} />;
      case 'textarea':
        return <TextareaField question={question as TextareaQuestion} />;
      default:
        return <TextField question={question as TextQuestion} />;
    }
  };

  return renderField();
};

// Helper functions
export const buildValidationSchema = (questions: Question[]) => {
  const schema: Record<string, any> = {};

  questions.forEach((question) => {
    // For banking information, all fields are optional
    schema[question.id] = Yup.string().nullable();
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: Question[], existingValues?: Record<string, any>) => {
  const initialValues: Record<string, any> = {};

  questions.forEach((question) => {
    initialValues[question.id] = existingValues?.[question.id] || '';
  });

  return initialValues;
};

export const handleDependentAnswers = (questionId: string, value: string, questions: Question[]) => {
  // Find dependent questions and reset their values if needed
  const dependentQuestions = questions.filter(q =>
    q.dependsOn?.questionId === questionId && q.dependsOn?.value !== value
  );

  const resetValues: Record<string, any> = {};
  dependentQuestions.forEach(q => {
    resetValues[q.id] = '';
  });

  return resetValues;
};

export const isQuestionVisible = (question: Question, formValues: Record<string, any>) => {
  if (!question.dependsOn) return true;
  return formValues[question.dependsOn.questionId] === question.dependsOn.value;
};
