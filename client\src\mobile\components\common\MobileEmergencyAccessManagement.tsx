import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Clock, AlertTriangle, CheckCircle, XCircle, User, ChevronDown, ChevronUp } from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';

interface MobileEmergencyAccessManagementProps {
  className?: string;
}

export const MobileEmergencyAccessManagement: React.FC<MobileEmergencyAccessManagementProps> = ({ className }) => {
  const { 
    userAccess, 
    keyHolders, 
    handleGrantEmergencyAccess,
    handleDenyEmergencyAccess,
    loading 
  } = useAccessControl();

  const [showEmergency, setShowEmergency] = useState(false);

  if (!userAccess || !userAccess.isOwner || userAccess.subscriptionType !== 'spare_key') {
    return null;
  }

  const pendingRequests = keyHolders.filter(kh => 
    kh.emergencyAccessRequested && 
    !kh.emergencyAccessGranted && 
    !kh.emergencyAccessDenied
  );

  const grantedRequests = keyHolders.filter(kh => kh.emergencyAccessGranted);
  const deniedRequests = keyHolders.filter(kh => kh.emergencyAccessDenied);

  const formatTimeElapsed = (requestedAt: string): string => {
    const requested = new Date(requestedAt).getTime();
    const now = Date.now();
    const elapsed = now - requested;
    const hours = Math.floor(elapsed / (1000 * 60 * 60));
    const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m ago`;
  };

  const getTimeRemaining = (requestedAt: string): number => {
    const requested = new Date(requestedAt).getTime();
    const now = Date.now();
    const elapsed = now - requested;
    const waitTime = 24 * 60 * 60 * 1000; // 24 hours in ms
    return Math.max(0, waitTime - elapsed);
  };

  const renderKeyHolder = (keyHolder: any, isPending = false) => {
    const timeRemaining = getTimeRemaining(keyHolder.emergencyAccessRequestedAt);
    const isAutoGranted = timeRemaining === 0 && keyHolder.emergencyAccessRequested && !keyHolder.emergencyAccessGranted && !keyHolder.emergencyAccessDenied;

    const pendingHighlight = keyHolder.emergencyAccessRequested && !keyHolder.emergencyAccessGranted && !keyHolder.emergencyAccessDenied;

    return (
      <div key={keyHolder._id} className={`border rounded-lg p-4 mb-3 bg-white ${pendingHighlight ? 'border-yellow-300 bg-yellow-50' : ''}`}>
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
              <User className="h-5 w-5 text-gray-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">
                {keyHolder.firstName} {keyHolder.lastName}
              </h4>
              <p className="text-sm text-gray-600">{keyHolder.email}</p>
            </div>
          </div>
          <div className="text-right">
            {keyHolder.emergencyAccessGranted && (
              <Badge className="bg-green-100 text-green-800">Granted</Badge>
            )}
            {keyHolder.emergencyAccessDenied && (
              <Badge className="bg-red-100 text-red-800">Denied</Badge>
            )}
            {keyHolder.emergencyAccessRequested && 
             !keyHolder.emergencyAccessGranted && 
             !keyHolder.emergencyAccessDenied && (
              <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
            )}
          </div>
        </div>

        {keyHolder.emergencyAccessRequested && (
          <div className="text-sm text-gray-600 mb-3">
            <div>Requested: {formatTimeElapsed(keyHolder.emergencyAccessRequestedAt)}</div>
            {timeRemaining > 0 && !keyHolder.emergencyAccessGranted && (
              <div className="mt-1">
                Auto-grant in: {Math.floor(timeRemaining / (1000 * 60 * 60))} hr {Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60))} min
              </div>
            )}
          </div>
        )}

        {keyHolder.emergencyAccessRequested && 
         !keyHolder.emergencyAccessGranted && 
         !keyHolder.emergencyAccessDenied && (
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={() => handleGrantEmergencyAccess(keyHolder._id)}
              disabled={loading}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              Grant Access
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={() => handleDenyEmergencyAccess(keyHolder._id)}
              disabled={loading}
              className="flex-1"
            >
              Deny Access
            </Button>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`${className} w-full max-w-md mx-auto mb-6`}>
      <button
        className="flex items-center justify-between w-full p-4 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors"
        onClick={() => setShowEmergency((prev) => !prev)}
        aria-expanded={showEmergency}
        aria-controls="emergency-access-section"
      >
        <div className="flex items-center gap-3">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          <div className="text-left">
            <h3 className="font-semibold text-gray-900 flex items-center gap-2">
              Emergency Access Management
              {pendingRequests.length > 0 && (
                <span className="inline-block w-2 h-2 rounded-full bg-yellow-400 animate-pulse" />
              )}
            </h3>
            <p className="text-sm text-gray-600">
              {pendingRequests.length} pending request{pendingRequests.length !== 1 ? 's' : ''}
            </p>
          </div>
        </div>
        {showEmergency ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
      </button>

      <div
        id="emergency-access-section"
        style={{
          maxHeight: showEmergency ? 1000 : 0,
          overflow: 'hidden',
          transition: 'max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        }}
        aria-hidden={!showEmergency}
      >
        <div className="mt-4 space-y-4">
          {pendingRequests.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Pending Requests ({pendingRequests.length})</h4>
              {pendingRequests.map((keyHolder: any) => renderKeyHolder(keyHolder, true))}
            </div>
          )}

          {grantedRequests.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Granted Access ({grantedRequests.length})</h4>
              {grantedRequests.map((keyHolder: any) => renderKeyHolder(keyHolder))}
            </div>
          )}

          {deniedRequests.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Denied Requests ({deniedRequests.length})</h4>
              {deniedRequests.map((keyHolder: any) => renderKeyHolder(keyHolder))}
            </div>
          )}

          {keyHolders.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <User className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p>No key holders found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 