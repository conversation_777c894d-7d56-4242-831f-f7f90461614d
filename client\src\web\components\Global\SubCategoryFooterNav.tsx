import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useRestrictedNavigation } from '@/hooks/useRestrictedNavigation';
import { UpgradeDialog } from '@/components/common/UpgradeDialog';
import { getCategoryIdFromPath } from '@/utils/footerUtils';
import { useAccessControl } from '@/hooks/useAccessControl';
import { Lock } from 'lucide-react';

interface SubCategoryFooterNavProps {
  leftLabel: string;
  leftTo: string;
  rightLabel: string;
  rightTo: string;
  className?: string;
}

const SubCategoryFooterNav: React.FC<SubCategoryFooterNavProps> = ({
  leftLabel,
  leftTo,
  rightLabel,
  rightTo,
  className,
}) => {
  const { 
    handleNavigation, 
    handleDisabledClick,
    handleUpgradeClick, 
    upgradeDialogOpen, 
    setUpgradeDialogOpen, 
    userAccess 
  } = useRestrictedNavigation();
  
  const { canAccessCategory } = useAccessControl();

  // Check if navigation targets are accessible
  const isLeftAccessible = getCategoryIdFromPath(leftTo) ? canAccessCategory(getCategoryIdFromPath(leftTo)!) : true;
  const isRightAccessible = getCategoryIdFromPath(rightTo) ? canAccessCategory(getCategoryIdFromPath(rightTo)!) : true;

  return (
    <>
      <div className={`flex gap-4 mt-8 ${className || ''}`}>
        <Link to={leftTo} className="flex-1" onClick={(e) => !isLeftAccessible ? handleDisabledClick(e, leftTo) : handleNavigation(e, leftTo)}>
          <Button 
            variant="outline" 
            className={`w-full transition-all ${
              !isLeftAccessible 
                ? 'opacity-50 cursor-pointer bg-gray-50 text-gray-400 border-gray-200 hover:bg-gray-100' 
                : ''
            }`}
            disabled={!isLeftAccessible}
          >
            <div className="flex items-center gap-2">
              ← {leftLabel}
              {!isLeftAccessible && <Lock className="w-3 h-3" />}
            </div>
          </Button>
        </Link>
        <Link to={rightTo} className="flex-1" onClick={(e) => !isRightAccessible ? handleDisabledClick(e, rightTo) : handleNavigation(e, rightTo)}>
          <Button 
            className={`w-full transition-all ${
              !isRightAccessible 
                ? 'opacity-50 cursor-pointer bg-gray-400 text-gray-200 hover:bg-gray-500' 
                : 'bg-[#2BCFD5] hover:bg-[#19bbb5] text-white'
            }`}
            disabled={!isRightAccessible}
          >
            <div className="flex items-center gap-2">
              {rightLabel} →
              {!isRightAccessible && <Lock className="w-3 h-3" />}
            </div>
          </Button>
        </Link>
      </div>

      {/* Upgrade Dialog */}
      <UpgradeDialog
        open={upgradeDialogOpen}
        onOpenChange={setUpgradeDialogOpen}
        onUpgradeClick={handleUpgradeClick}
        subscriptionType={userAccess?.subscriptionType}
      />
    </>
  );
};

export default SubCategoryFooterNav; 