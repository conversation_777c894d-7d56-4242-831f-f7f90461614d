import { RequestHand<PERSON>, Router } from 'express';
import {
    createQuestion,
    getQuestions,
    getQuestionById,
    updateQuestion,
    deleteQuestion,
    saveAnswers,
    getQuestionsByCategory,
    getQuestionsBySubCategory
} from '../controller/questionController';
import { combinedAuth } from '../middleware/authMiddleware';

const router = Router();

router.post('/create', createQuestion as RequestHandler);
router.get('/', getQuestions as RequestHandler);
router.get('/:id', getQuestionById as RequestHandler);
router.put('/:id', updateQuestion as RequestHandler);
router.delete('/:id', deleteQuestion as RequestHandler);

// New routes for category-based questions and saving answers
router.get('/category/:categoryId', getQuestionsByCategory as RequestHandler);
router.get('/subcategory/:subCategoryId', getQuestionsBySubCategory as RequestHandler);
router.post('/answers', combinedAuth, saveAnswers as RequestHandler);

export default router;