import { useState, useEffect } from 'react';
import CategoryReviewPage from '@/mobile/components/category/CategoryReviewPage';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchUserInputs, selectUserInputs, selectLoading, selectError } from '@/store/slices/importantContactsSlice';
import { Button } from '@/components/ui/button';

// Map subcategory IDs to their routes
const subcategoryRoutes: Record<string, string> = {
  '207A': 'friendsfamily',
  '207B': 'work',
  '207C': 'religiousaffiliation',
  '207D': 'clubs',
};

interface ReviewItem {
  id: string;
  title: string;
  subtitle?: string;
  data: string;
  onEdit: () => void;
}

const ContactReviewPage = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [topics, setTopics] = useState<ReviewItem[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Get data from Redux store
  const userInputs = useAppSelector(selectUserInputs);
  const isLoading = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Handle navigation to edit a specific question
  const handleEditQuestion = (questionId: string, subcategoryId: string) => {
    const route = subcategoryRoutes[subcategoryId];
    if (route) {
      navigate(`/category/importantcontacts/${route}?questionId=${questionId}`);
    }
  };

  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      const topicsMap = new Map<string, ReviewItem>();

      userInputs.forEach((userInput) => {
        const subcategoryId = userInput.originalSubCategoryId;

        userInput.answersBySection?.forEach((section) => {
          section.answers?.forEach((answer) => {
            const questionId = answer.originalQuestionId;
            const existingTopic = topicsMap.get(questionId);

            try {
              const answerData = JSON.parse(answer.answer);
              
              const formatContact = (contact: any) => {
                if (typeof contact === 'object' && contact !== null) {
                  return `${contact.name || ''} (${contact.phone || contact.info || ''})`;
                }
                return '';
              };

              const formattedData = Array.isArray(answerData)
                ? answerData.map(formatContact).join(', ')
                : formatContact(answerData);

              if (existingTopic) {
                existingTopic.data += `, ${formattedData}`;
              } else {
                topicsMap.set(questionId, {
                  id: questionId,
                  title: answer.question,
                  subtitle: `Category: ${subcategoryRoutes[subcategoryId]?.charAt(0).toUpperCase() + subcategoryRoutes[subcategoryId]?.slice(1)}`,
                  data: formattedData,
                  onEdit: () => handleEditQuestion(questionId, subcategoryId),
                });
              }
            } catch (e) {
              const formattedData = answer.answer;
              if (existingTopic) {
                existingTopic.data += `, ${formattedData}`;
              } else {
                topicsMap.set(questionId, {
                  id: questionId,
                  title: answer.question,
                  subtitle: `Category: ${subcategoryRoutes[subcategoryId]?.charAt(0).toUpperCase() + subcategoryRoutes[subcategoryId]?.slice(1)}`,
                  data: formattedData,
                  onEdit: () => handleEditQuestion(questionId, subcategoryId),
                });
              }
            }
          });
        });
      });

      setTopics(Array.from(topicsMap.values()));
    }
  }, [userInputs, isLoading, navigate]);

  if (isLoading) {
    return (
      <>
        <GradiantHeader title="Important Contacts" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  if (error || reduxError) {
    return (
      <>
        <GradiantHeader title="Important Contacts" showAvatar={true} />
        <div className="p-4">
          <Alert variant="destructive">
            <AlertDescription>{error || reduxError}</AlertDescription>
          </Alert>
        </div>
      </>
    );
  }

  if (!user || !user.id) {
    return (
      <>
        <GradiantHeader title="Important Contacts" showAvatar={true} />
        <div className="p-4">
          <Alert variant="destructive">
            <AlertDescription>You must be logged in to view your answers</AlertDescription>
          </Alert>
        </div>
      </>
    );
  }

  if (userInputs.length === 0 && !isLoading) {
    return (
      <>
        <GradiantHeader title="Important Contacts" showAvatar={true} />
        <div className="p-4">
          <Alert>
            <AlertDescription>No important contacts answers found. Please complete some questions first.</AlertDescription>
          </Alert>
        </div>
      </>
    );
  }

  return (
    <>
      <CategoryReviewPage
        categoryTitle="Important Contacts"
        infoTitle="How to edit your information"
        infoDescription="Review the details about your important contacts. Tap Edit on any item to update it."
        topics={topics}
        onPrint={() => window.print()}
        afterTopics={
          <Button 
            onClick={() => navigate('/category/socialmedia')}
            className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
          >
            Continue to Social Media
          </Button>
        }
      />
    </>
  );
};

export default ContactReviewPage;
