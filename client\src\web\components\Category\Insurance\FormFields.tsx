import { useField } from 'formik';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export interface BaseQuestion {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  isAnswered?: boolean;
  answer?: any;
  dependsOn?: {
    questionId: string;
    value: string;
  };
  options?: string[];
}

export interface DisplayQuestion extends BaseQuestion {
  type: 'display';
}

export interface TextQuestion extends BaseQuestion {
  type: 'text';
  validationRules?: {
    minLength?: number;
    maxLength?: number;
  };
  placeholder?: string;
}

export interface BooleanQuestion extends BaseQuestion {
  type: 'boolean';
}

export interface DropdownQuestion extends BaseQuestion {
  type: 'dropdown';
  options: string[];
}

export interface TextareaQuestion extends BaseQuestion {
  type: 'textarea';
  placeholder?: string;
}

export type Question = DisplayQuestion | TextQuestion | BooleanQuestion | DropdownQuestion | TextareaQuestion;

// Custom form field components for Formik
export const TextareaField = ({ question }: { question: TextQuestion | TextareaQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Textarea
        id={question.id}
        placeholder={question.placeholder}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const TextField = ({ question }: { question: TextQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Input
        id={question.id}
        type={question.id.includes('password') || question.id.includes('Password') ? 'password' : 'text'}
        placeholder={question.placeholder}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const BooleanField = ({ question }: { question: BooleanQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  
  return (
    <div className="mb-6">
      <div className="flex flex-col">
        <Label className="font-medium mb-2" htmlFor={question.id}>
          {question.text}
        </Label>
        <div className="flex gap-2">
          <Button
            type="button"
            onClick={() => helpers.setValue('yes')}
            className={field.value === 'yes' 
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5]' 
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          >
            Yes
          </Button>
          <Button
            type="button"
            onClick={() => helpers.setValue('no')}
            className={field.value === 'no' 
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5]' 
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          >
            No
          </Button>
        </div>
        {meta.touched && meta.error ? (
          <div className="text-red-500 text-sm mt-1">{meta.error}</div>
        ) : null}
      </div>
    </div>
  );
};

export const DropdownField = ({ question }: { question: DropdownQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium">
        {question.text}
      </Label>
      <Select value={field.value || ''} onValueChange={(value) => helpers.setValue(value)}>
        <SelectTrigger className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}>
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          {question.options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const DisplayField = ({ question }: { question: DisplayQuestion }) => {
  return (
    <div className="mb-6">
      <div className="text-lg font-semibold text-[#1F4168] border-b pb-2">
        {question.text}
      </div>
    </div>
  );
};

// Main component for rendering questions
export const QuestionItem = ({ 
  question, 
  values 
}: { 
  question: Question;
  values: Record<string, any>;
}) => {
  // Check if this question should be shown based on dependencies
  const shouldShow = !question.dependsOn || 
    (values[question.dependsOn.questionId]?.toString().toLowerCase() === question.dependsOn.value.toLowerCase());

  // If question depends on something and condition is not met, render ghosted version
  const isGhosted = question.dependsOn && 
    values[question.dependsOn.questionId]?.toString().toLowerCase() !== question.dependsOn.value.toLowerCase();

  if (!shouldShow && isGhosted) {
    return (
      <div className="mb-6 opacity-50 pointer-events-none">
        {renderQuestion(question)}
      </div>
    );
  } else if (!shouldShow) {
    return null;
  }

  return renderQuestion(question);
};

const renderQuestion = (question: Question) => {
  switch (question.type) {
    case 'display':
      return <DisplayField question={question as DisplayQuestion} />;
    case 'text':
      return <TextField question={question as TextQuestion} />;
    case 'textarea':
      return <TextareaField question={question} />;
    case 'boolean':
      return <BooleanField question={question as BooleanQuestion} />;
    case 'dropdown':
      return <DropdownField question={question as DropdownQuestion} />;
    default:
      return null;
  }
};

// Utility functions for handling forms
export const buildValidationSchema = (questions: Question[], Yup: any) => {
  const schemaShape: Record<string, any> = {};
  
  questions.forEach(question => {
    // Skip validation for display type questions
    if (question.type === 'display') return;

    let fieldSchema;

    switch (question.type) {
      case 'text':
        fieldSchema = Yup.string();
        if ((question as TextQuestion).validationRules?.minLength) {
          fieldSchema = fieldSchema.min(
            (question as TextQuestion).validationRules!.minLength!,
            `Must be at least ${(question as TextQuestion).validationRules!.minLength!} characters`
          );
        }
        if ((question as TextQuestion).validationRules?.maxLength) {
          fieldSchema = fieldSchema.max(
            (question as TextQuestion).validationRules!.maxLength!,
            `Must be at most ${(question as TextQuestion).validationRules!.maxLength!} characters`
          );
        }
        break;

      case 'boolean':
        fieldSchema = Yup.string();
        break;

      case 'dropdown':
        fieldSchema = Yup.string();
        break;

      default:
        fieldSchema = Yup.string();
    }

    schemaShape[question.id] = fieldSchema;
  });
  
  return Yup.object().shape(schemaShape);
};

export const generateInitialValues = (questions: Question[]) => {
  const initialValues: Record<string, any> = {};
  
  questions.forEach(question => {
    if (question.type !== 'display') {
      initialValues[question.id] = '';
    }
  });
  
  return initialValues;
};

export const handleDependentAnswers = (
  values: Record<string, any>,
  questions: Question[],
  setValues: (values: Record<string, any>) => void
) => {
  const newValues = { ...values };
  let hasChanges = false;

  questions.forEach(question => {
    if (question.dependsOn) {
      const dependentValue = values[question.dependsOn.questionId];
      const shouldShow = dependentValue?.toString().toLowerCase() === question.dependsOn.value.toLowerCase();
      
      if (!shouldShow && values[question.id]) {
        newValues[question.id] = '';
        hasChanges = true;
      }
    }
  });

  if (hasChanges) {
    setValues(newValues);
  }
};

export const calculateProgress = (questions: Question[], values: Record<string, any>) => {
  let totalQuestions = 0;
  let answeredQuestions = 0;

  questions.forEach(question => {
    if (question.type === 'display') return;

    // Check if question should be counted based on dependencies
    if (question.dependsOn) {
      const dependentAnswer = values[question.dependsOn.questionId];
      if (dependentAnswer?.toString().toLowerCase() === question.dependsOn.value.toLowerCase()) {
        totalQuestions++;
        if (values[question.id] && values[question.id].toString().trim() !== '') {
          answeredQuestions++;
        }
      }
    } else {
      totalQuestions++;
      if (values[question.id] && values[question.id].toString().trim() !== '') {
        answeredQuestions++;
      }
    }
  });

  const completionPercentage = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;
  return { totalQuestions, answeredQuestions, completionPercentage };
};
