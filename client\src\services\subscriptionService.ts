import api from './api';
import { PricingPlan, Subscription } from '@/types/subscription';

class SubscriptionService {
  async getPricingPlans(): Promise<PricingPlan[]> {
    const response = await api.get<PricingPlan[]>('/v1/api/pricing-plans');
    return response.data;
  }

  async getCurrentSubscription(): Promise<Subscription> {
    const response = await api.get<Subscription>('/v1/api/subscriptions/current');
    return response.data;
  }

  async subscribeToPlan(planId: string, ownerId: string): Promise<Subscription> {
    const response = await api.post<Subscription>('/v1/api/subscriptions', { planId, ownerId });
    return response.data;
  }

  async cancelSubscription(): Promise<Subscription> {
    const response = await api.post<Subscription>('/v1/api/subscriptions/cancel');
    return response.data;
  }

  async updateSubscription(planId: string): Promise<Subscription> {
    const response = await api.put<Subscription>('/v1/api/subscriptions', { planId });
    return response.data;
  }

  async getOwnerSubscription(ownerId: string): Promise<any> {
    const response: any = await api.get(`/v1/api/subscriptions/owner/${ownerId}`);
    return response.data.data.subscription;
  }
}

export default new SubscriptionService(); 