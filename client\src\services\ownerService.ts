import api from './api';
import { Owner } from '@/types/owner';

// Cache for owner data to prevent duplicate API calls
const ownerCache = new Map<string, { data: Owner, timestamp: number }>();
const ownerPromiseCache = new Map<string, Promise<Owner>>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

class OwnerService {
  async getOwnerByUserId(userId: string): Promise<Owner> {
    const cacheKey = `user_${userId}`;
    const now = Date.now();

    // Check cache first
    const cached = ownerCache.get(cacheKey);
    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      return cached.data;
    }

    // Check if there's already a promise in flight
    if (ownerPromiseCache.has(cacheKey)) {
      return ownerPromiseCache.get(cacheKey)!;
    }

    // Create and cache the promise
    const promise = (async (): Promise<Owner> => {
      try {
        const response: any = await api.get(`/v1/api/owners/user/${userId}`);
        const owner = response.data.data.owner;
        // Cache the result
        ownerCache.set(cacheKey, { data: owner, timestamp: now });
        // Clean up promise cache
        ownerPromiseCache.delete(cacheKey);
        return owner;
      } catch (error) {
        // Clean up promise cache on error
        ownerPromiseCache.delete(cacheKey);
        throw error;
      }
    })();

    ownerPromiseCache.set(cacheKey, promise);
    return promise;
  }

  async getOwnerById(ownerId: string): Promise<Owner> {
    const cacheKey = `owner_${ownerId}`;
    const now = Date.now();

    // Check cache first
    const cached = ownerCache.get(cacheKey);
    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      console.log(`[OwnerService] Cache hit for getOwnerById(${ownerId})`);
      return cached.data;
    }

    // Check if there's already a promise in flight
    if (ownerPromiseCache.has(cacheKey)) {
      console.log(`[OwnerService] Promise in flight for getOwnerById(${ownerId}), waiting...`);
      return ownerPromiseCache.get(cacheKey)!;
    }

    console.log(`[OwnerService] Making API call for getOwnerById(${ownerId})`);

    // Create and cache the promise
    const promise = (async (): Promise<Owner> => {
      try {
        const response: any = await api.get(`/v1/api/owners/${ownerId}`);
        const owner = response.data.data.owner;
        // Cache the result
        ownerCache.set(cacheKey, { data: owner, timestamp: now });
        // Clean up promise cache
        ownerPromiseCache.delete(cacheKey);
        return owner;
      } catch (error) {
        // Clean up promise cache on error
        ownerPromiseCache.delete(cacheKey);
        throw error;
      }
    })();

    ownerPromiseCache.set(cacheKey, promise);
    return promise;
  }

  async getOwnerByEmail(email: string): Promise<Owner> {
    const cacheKey = `email_${email}`;
    const now = Date.now();

    // Check cache first
    const cached = ownerCache.get(cacheKey);
    if (cached && (now - cached.timestamp) < CACHE_DURATION) {
      return cached.data;
    }

    // Check if there's already a promise in flight
    if (ownerPromiseCache.has(cacheKey)) {
      return ownerPromiseCache.get(cacheKey)!;
    }

    // Create and cache the promise
    const promise = (async (): Promise<Owner> => {
      try {
        const response: any = await api.get(`/v1/api/owners/email/${email}`);
        const owner = response.data.data.owner;
        // Cache the result
        ownerCache.set(cacheKey, { data: owner, timestamp: now });
        // Clean up promise cache
        ownerPromiseCache.delete(cacheKey);
        return owner;
      } catch (error) {
        // Clean up promise cache on error
        ownerPromiseCache.delete(cacheKey);
        throw error;
      }
    })();

    ownerPromiseCache.set(cacheKey, promise);
    return promise;
  }

  /**
   * Clear cache for all owner data
   */
  clearCache(): void {
    ownerCache.clear();
    ownerPromiseCache.clear();
  }

  /**
   * Clear cache for a specific owner
   */
  clearOwnerCache(ownerId: string): void {
    ownerCache.delete(`owner_${ownerId}`);
    ownerPromiseCache.delete(`owner_${ownerId}`);
  }

  /**
   * Clear cache for a specific user
   */
  clearUserCache(userId: string): void {
    ownerCache.delete(`user_${userId}`);
    ownerPromiseCache.delete(`user_${userId}`);
  }
}

export default new OwnerService(); 