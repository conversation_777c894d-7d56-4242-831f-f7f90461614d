import mongoose from 'mongoose';

export interface IPricingPlan extends mongoose.Document {
  type: 'temporary_key' | 'spare_key' | 'all_access_key';
  price: number;
  displayPrice: string;
  tagline: string;
  features: string[];
  duration: number; // Duration in months, -1 for infinite
  categorylimit: number; // Number of categories allowed, -1 for unlimited
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
  priceIdStripe:string
}
