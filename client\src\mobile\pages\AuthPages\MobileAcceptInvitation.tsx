import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner'; // Use the same toast as web
import { Loader2 } from 'lucide-react';
import { acceptInvite } from '@/services/inviteService';

export default function MobileAcceptInvitation() {
  const { token } = useParams();
  const navigate = useNavigate();
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAccept = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await acceptInvite(token!, password);
      toast.success('Invitation accepted! You can now log in.');
      navigate('/auth/login');
    } catch (err: any) {
      toast.error(err?.response?.data?.message || 'Failed to accept invitation.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-2">
      <Card className="w-full max-w-sm mx-auto shadow-lg">
        <CardHeader>
          <CardTitle className="text-center text-lg">Accept Invitation</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleAccept} className="space-y-4">
            <div className="text-center text-sm text-gray-600 mb-2">
              Set your password to activate your account.
            </div>
            <Input
              type="password"
              placeholder="Enter a password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              required
              minLength={6}
              className="w-full"
              disabled={loading}
            />
            <Button type="submit" className="w-full bg-[#1F4168] text-white" disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 animate-spin mx-auto" /> : 'Accept Invitation'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
} 