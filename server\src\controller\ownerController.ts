import { Request, Response } from 'express';
import Owner from '../models/Owner';
import User from '../models/User';
import { ICreateOwnerData, IUpdateOwnerData } from '../types/Owner';
import { AuthRequest } from '../middleware/authMiddleware';
import mongoose from 'mongoose';

// Get all owners
export const getAllOwners = async (req: Request, res: Response): Promise<void> => {
  try {
    const owners = await Owner.find()
      .populate('user', 'username email roleId')
      .sort({ createdAt: -1 });
    
    res.status(200).json({
      status: 'success',
      results: owners.length,
      data: { owners }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error fetching owners',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get owner by ID
export const getOwnerById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const owner = await Owner.findById(id).populate('user', 'username email roleId');
    
    if (!owner) {
      res.status(404).json({
        status: 'fail',
        message: 'Owner not found'
      });
      return;
    }
    
    res.status(200).json({
      status: 'success',
      data: { owner }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error fetching owner',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get owner by user ID
export const getOwnerByUserId = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    
    const owner = await Owner.findByUserId(new mongoose.Types.ObjectId(userId));
    
    if (!owner) {
      res.status(404).json({
        status: 'fail',
        message: 'Owner not found for this user'
      });
      return;
    }
    
    res.status(200).json({
      status: 'success',
      data: { owner }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error fetching owner by user ID',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get current user's owner profile
export const getMyOwnerProfile = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id;
    
    if (!userId) {
      res.status(401).json({
        status: 'fail',
        message: 'User not authenticated'
      });
      return;
    }
    
    const owner = await Owner.findByUserId(userId);
    
    if (!owner) {
      res.status(404).json({
        status: 'fail',
        message: 'Owner profile not found'
      });
      return;
    }
    
    res.status(200).json({
      status: 'success',
      data: { owner }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error fetching owner profile',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update owner
export const updateOwner = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData: IUpdateOwnerData = req.body;
    
    const owner = await Owner.findById(id);
    
    if (!owner) {
      res.status(404).json({
        status: 'fail',
        message: 'Owner not found'
      });
      return;
    }

    // Update only provided fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof IUpdateOwnerData] !== undefined) {
        (owner as any)[key] = updateData[key as keyof IUpdateOwnerData];
      }
    });

    await owner.save();
    
    res.status(200).json({
      status: 'success',
      message: 'Owner updated successfully',
      data: { owner }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error updating owner',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update current user's owner profile
export const updateMyOwnerProfile = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id;
    const updateData: IUpdateOwnerData = req.body;
    
    if (!userId) {
      res.status(401).json({
        status: 'fail',
        message: 'User not authenticated'
      });
      return;
    }
    
    const owner = await Owner.findOne({ userId });
    
    if (!owner) {
      res.status(404).json({
        status: 'fail',
        message: 'Owner profile not found'
      });
      return;
    }

    // Update only provided fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof IUpdateOwnerData] !== undefined) {
        (owner as any)[key] = updateData[key as keyof IUpdateOwnerData];
      }
    });

    await owner.save();
    
    res.status(200).json({
      status: 'success',
      message: 'Owner profile updated successfully',
      data: { owner }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error updating owner profile',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete owner (soft delete by removing the relationship)
export const deleteOwner = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const owner = await Owner.findById(id);

    if (!owner) {
      res.status(404).json({
        status: 'fail',
        message: 'Owner not found'
      });
      return;
    }

    // Remove owner reference from user
    await User.findByIdAndUpdate(
      owner.userId,
      { $unset: { ownerId: 1 } }
    );

    // Delete the owner record
    await Owner.findByIdAndDelete(id);

    res.status(200).json({
      status: 'success',
      message: 'Owner deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error deleting owner',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get owner by email
export const getOwnerByEmail = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email } = req.params;
    
    const owner = await Owner.findByEmail(email);
    
    if (!owner) {
      res.status(404).json({
        status: 'fail',
        message: 'Owner not found with this email'
      });
      return;
    }
    
    res.status(200).json({
      status: 'success',
      data: { owner }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error fetching owner by email',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
