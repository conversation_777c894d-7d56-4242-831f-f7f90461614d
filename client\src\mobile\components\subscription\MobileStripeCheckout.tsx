import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CreditCard, CheckCircle, AlertCircle, ArrowLeft } from 'lucide-react';
import stripeService from '@/services/stripeService';
import { useToast } from '@/hooks/use-toast';
import { PricingPlan } from '@/types/subscription';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import GradiantHeader from '../header/gradiantHeader';

interface MobileStripeCheckoutProps {
  plan: PricingPlan;
  onSuccess?: () => void;
  onCancel?: () => void;
  className?: string;
}

export default function MobileStripeCheckout({ 
  plan, 
  onSuccess, 
  onCancel, 
  className = "" 
}: MobileStripeCheckoutProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  const isFreePlan = plan.price === 0;

  const handleSubscribe = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get ownerId for the current user
      if (!user) {
        setError('User not found. Please log in again.');
        toast({
          title: "User Not Found",
          description: "Please log in again.",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }
      const ownerId = await getCachedOwnerIdFromUser(user);
      if (!ownerId) {
        setError('Owner ID not found. Please log in again or contact support.');
        toast({
          title: "Owner Not Found",
          description: "Could not determine your owner ID. Please log in again or contact support.",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      // Create Stripe checkout session (or free plan subscription)
      const session = await stripeService.createCheckoutSession(plan.type, ownerId);
      
      if (session.url) {
        if (isFreePlan) {
          // For free plans, redirect directly to success page
          window.location.href = session.url;
        } else {
          // For paid plans, redirect to Stripe checkout
          window.location.href = session.url;
        }
      } else {
        throw new Error('No checkout URL received');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start checkout process';
      setError(errorMessage);
      toast({
        title: "Checkout Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <div className={`min-h-screen bg-gray-100 flex items-center justify-center p-4 ${className}`}>
      <Card className="w-full max-w-md rounded-2xl shadow-lg overflow-hidden">
        {/* Header as part of the card */}
        <GradiantHeader title="Complete Your Subscription" />

        <div className="px-6 py-6">
          <CardHeader className="text-center pb-4">
            <CardTitle className="flex items-center justify-center gap-2 text-lg">
              {isFreePlan ? (
                <CheckCircle className="h-6 w-6 text-green-500" />
              ) : (
                <CreditCard className="h-6 w-6 text-[#2BCFD5]" />
              )}
              {isFreePlan ? 'Free Plan' : 'Secure Payment'}
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Plan Summary */}
            <div className="bg-[#F0F9FF] rounded-xl p-4 border border-[#D1E9F7]">
              <h3 className="font-semibold text-lg mb-2 text-[#1F4168]">
                {plan.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </h3>
              <div className="text-2xl font-bold text-[#2BCFD5] mb-1">{plan.displayPrice}</div>
              <p className="text-sm text-gray-600">{plan.tagline}</p>
            </div>

            {/* Features Preview */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm text-gray-700">What's included:</h4>
              <ul className="space-y-2">
                {plan.features.slice(0, 3).map((feature, index) => (
                  <li key={index} className="flex items-start gap-3 text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                    <span>{feature}</span>
                  </li>
                ))}
                {plan.features.length > 3 && (
                  <li className="text-sm text-gray-500 pl-7">
                    +{plan.features.length - 3} more features
                  </li>
                )}
              </ul>
            </div>

            {/* Error Display */}
            {error && (
              <div className="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3 pt-4">
              <Button
                onClick={handleSubscribe}
                disabled={isLoading}
                className={`w-full py-3 text-base font-semibold rounded-lg ${
                  isFreePlan 
                    ? 'bg-green-600 hover:bg-green-700' 
                    : 'bg-[#2BCFD5] hover:bg-[#1F4168]'
                }`}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    {isFreePlan ? (
                      <CheckCircle className="mr-2 h-5 w-5" />
                    ) : (
                      <CreditCard className="mr-2 h-5 w-5" />
                    )}
                    {isFreePlan ? 'Get Free Plan' : `Subscribe Now - ${plan.displayPrice}`}
                  </>
                )}
              </Button>
              
              <Button
                onClick={handleCancel}
                variant="outline"
                className="w-full py-3 text-base"
                disabled={isLoading}
              >
                Cancel
              </Button>
            </div>

            {/* Security Notice */}
            {!isFreePlan && (
              <div className="text-xs text-gray-500 text-center bg-gray-50 p-3 rounded-lg">
                🔒 Your payment is secured by Stripe. We never store your payment information.
              </div>
            )}
            
            {/* Free Plan Notice */}
            {isFreePlan && (
              <div className="text-xs text-green-600 text-center bg-green-50 p-3 rounded-lg">
                ✓ No payment required. Start using HeirKey immediately!
              </div>
            )}
          </CardContent>
        </div>
      </Card>
    </div>
  );
} 