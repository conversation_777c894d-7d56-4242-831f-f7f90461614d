import express from 'express';
import {
  createSubscription,
  getAllSubscriptions,
  getSubscriptionById,
  getOwnerSubscription,
  changePlan,
  cancelSubscription,
  getExpiredSubscriptions,
  renewSubscription,
  syncUserSubscriptionTypes
} from '../controller/subscribedPlanController';
import { 
  validateSubscribedPlan, 
  validateChangePlan 
} from '../validation/subscribedPlanValidation';

const router = express.Router();

// Get all subscriptions (with optional filters)
router.get('/', getAllSubscriptions);

// Get expired subscriptions
router.get('/expired', getExpiredSubscriptions);

// Sync user subscription types with their actual subscriptions
router.post('/sync-user-subscription-types', syncUserSubscriptionTypes);

// Get subscription by ID
router.get('/:id', getSubscriptionById);

// Get owner's current subscription
router.get('/owner/:ownerId', getOwnerSubscription);

// Create new subscription
router.post('/', validateSubscribedPlan, createSubscription);

// Change/upgrade plan for an owner
router.put('/owner/:ownerId/change-plan', validateChangePlan, changePlan);

// Renew subscription (extend current plan)
router.put('/:id/renew', renewSubscription);

// Cancel subscription
router.delete('/:id', cancelSubscription);

export default router;
