import { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Formik, Form } from "formik";
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import financialPlansData from '@/data/financialPlans.json';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { Question, QuestionItem, buildValidationSchema, generateInitialValues, calculateProgress } from '@/mobile/components/dashboard/FinancialPlans/FormFields';
import ScrollToQuestion from '@/mobile/components/dashboard/FinancialPlans/ScrollToQuestion';
import { useAuth } from '@/contexts/AuthContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  selectUserInputsBySubcategoryId,
  selectLoading,
  selectError
} from '@/store/slices/financialPlansSlice';
import { convertUserInputToFormValues, generateObjectId, normalizeContactData } from '@/services/userInputService';
import * as Yup from 'yup';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

const subcategoryId = "501C";

export default function StocksPage() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [formError, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get questions for this section
  const questions = (financialPlansData["501"] || []).filter(q => q.sectionId === subcategoryId) as Question[];
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId(subcategoryId)(state));
  const isLoadingRedux = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Find the userInput for this subcategory (following ImportantContacts pattern)
  const userInput = useMemo(
    () => userInputs.find((input: UserInput) => input.originalSubCategoryId === subcategoryId),
    [userInputs, subcategoryId]
  );

  // Generate form configuration
  const validationSchema = buildValidationSchema(questions, Yup);
  const baseInitialValues = generateInitialValues(questions);

  // Cross-platform data reading logic (following ImportantContacts pattern)
  const initialValues = (() => {
    if (userInput) {
      const processedValues = { ...baseInitialValues };

      // Process each question to handle cross-platform data
      questions.forEach(question => {
        if (question.type === 'contacts') {
          // Handle contacts with cross-platform logic
          let contacts: any[] = [];
          const section = userInput.answersBySection.find((s: any) => s.originalSectionId === subcategoryId);
          if (section) {
            const contactAnswers = section.answers.filter((a: any) => a.originalQuestionId === question.id);
            if (contactAnswers.length === 1) {
              // Web style: one answer, which is an array
              try {
                const parsed = JSON.parse(contactAnswers[0].answer);
                if (Array.isArray(parsed)) {
                  contacts = parsed.map(normalizeContactData);
                } else if (parsed && typeof parsed === 'object') {
                  contacts = [normalizeContactData(parsed)];
                }
              } catch {}
            } else if (contactAnswers.length > 1) {
              // Mobile style: multiple answers, each a contact
              contacts = contactAnswers.map((a: any) => {
                try {
                  return normalizeContactData(JSON.parse(a.answer));
                } catch {
                  return null;
                }
              }).filter(Boolean);
            }
          }
          processedValues[question.id] = contacts.length > 0 ? contacts : [{ name: "", phone: "" }];
        } else {
          // Handle regular fields
          const formValues = convertUserInputToFormValues(userInput);
          if (formValues[question.id] !== undefined) {
            processedValues[question.id] = formValues[question.id];
          }
        }
      });

      return processedValues;

    }
    return baseInitialValues;
  })();

  // Get existing input ID directly from userInput (following ImportantContacts pattern)
  const existingInputId = userInput?._id || null;

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  if (isLoading) {
    return (
      <>
        <GradiantHeader title="Stocks" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <GradiantHeader title="Stocks" showAvatar={true} />
      <div className="container mx-auto px-4 py-4 max-w-md">
        {/* Tabs */}
        <div className="bg-gray-50 rounded-xl shadow-sm border border-gray-100 flex flex-row overflow-x-auto">
          {categoryTabsConfig.financialplans.map(tab => {
            const isActive = tab.path === "/category/financialplans/stocks";
            return (
              <button
                key={tab.label}
                type="button"
                className={
                  "flex-1 py-2 px-2 rounded-lg font-medium text-xs whitespace-nowrap " +
                  (isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500")
                }
                style={{ flexShrink: 0, minWidth: 'fit-content' }}
                disabled={isActive}
                onClick={() => {
                  if (!isActive) navigate(tab.path);
                }}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Error Alert */}
        {(formError || reduxError) && (
          <Alert variant="destructive" className="mt-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        <div className="flex bg-white rounded-xl shadow-sm border border-gray-100 items-center justify-between mt-4 py-4 p-3">
          <div>
            <span className="text-lg font-bold text-gray-500">Financial Plans:</span>
            <span className="text-[#2BCFD5] font-semibold ml-1">Stocks</span>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mt-4">
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                if (!user?.id) {
                  setError('You must be logged in to save answers');
                  return;
                }

                // Use web-style saving for better cross-platform compatibility
                const answersBySection = questions.reduce((sections: Record<string, Array<{
                  index: number;
                  originalQuestionId: string;
                  question: string;
                  type: string;
                  answer: any;
                }>>, question) => {
                  if (!sections[question.sectionId]) {
                    sections[question.sectionId] = [];
                  }
                  let answer = values[question.id];

                  // Special handling for contact arrays (web-style)
                  if (question.type === 'contacts' && Array.isArray(answer)) {
                    // Filter out empty contacts and stringify for storage
                    answer = answer.filter((c: any) => c && (c.name?.trim() || c.phone?.trim()));
                    answer = JSON.stringify(answer);
                  }

                  if (answer !== undefined && answer !== '' && !(Array.isArray(answer) && answer.length === 0)) {
                    sections[question.sectionId].push({
                      index: sections[question.sectionId].length,
                      originalQuestionId: question.id,
                      question: question.text,
                      type: question.type === 'contacts' ? 'text' : question.type,
                      answer
                    });
                  }
                  return sections;
                }, {});

                const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
                  originalSectionId: sectionId,
                  isCompleted: true,
                  answers: answers as Array<{
                    index: number;
                    originalQuestionId: string;
                    question: string;
                    type: string;
                    answer: any;
                  }>
                }));

                const hasAnswers = formattedAnswersBySection.some(section => section.answers.length > 0);
                if (!hasAnswers) {
                  setSubmitting(false);
                  return;
                }

                const userData = {
                  userId: user.id,
                  categoryId: generateObjectId(),
                  originalCategoryId: '9', // Use '9' like web version
                  subCategoryId: generateObjectId(),
                  originalSubCategoryId: subcategoryId,
                  answersBySection: formattedAnswersBySection
                };

                if (existingInputId) {
                  await dispatch(updateUserInput({ id: existingInputId, userData })).unwrap();
                } else {
                  await dispatch(saveUserInput(userData)).unwrap();
                }

                navigate('/category/financialplans/bonds');
              } catch (error) {
                setError('Failed to save your answers. At least one question must be answered.');
              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({ values, isSubmitting }) => {
              const progress = calculateProgress(questions, values);
              
              return (
                <Form>
                  <div className="bg-gray-50 p-5 rounded-xl shadow-sm border">
                    <div className="flex items-center justify-between">
                      <p className="text-lg font-semibold">
                        Financial Plans: <span className="text-[#2BCFD5]">Stocks</span>
                      </p>
                      <CircularProgress
                        value={progress.answeredQuestions}
                        max={progress.totalQuestions}
                        size={40}
                        stroke={3}
                        color="#2BCFD5"
                      />
                    </div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-xl shadow-sm border mt-4">
                    <ScrollToQuestion questions={questions}>
                      {(refs) => (
                        <>
                          {questions.map(question => (
                            <div key={question.id} ref={el => { refs[question.id] = el; }}>
                              <QuestionItem question={question} values={values} />
                            </div>
                          ))}
                        </>
                      )}
                    </ScrollToQuestion>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-[#2BCFD5] text-white py-2 rounded-lg font-semibold mt-4"
                    >
                      {isSubmitting ? 'Saving...' : 'Save & Continue'}
                    </button>
                  </div>
                </Form>
              );
            }}
          </Formik>
        </div>
      </div>
      <Footer />
    </div>
  );
}
