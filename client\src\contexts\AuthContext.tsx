import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import authService, { User, LoginCredentials, RegisterCredentials, AuthResponse } from '../services/authService';
import { useAppDispatch } from '../store';
import { clearAccessControlOnLogout } from '../store/slices/accessControlSlice';

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  clearError: () => void;
  setUser: (user: User | null) => void;
  setToken: (token: string | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const [user, setUserState] = useState<User | null>(null);
  const [token, setTokenState] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize auth state from localStorage
  useEffect(() => {
    console.log('AuthContext: Initializing from localStorage');
    const storedToken = localStorage.getItem('token');
    const storedUser = localStorage.getItem('user');

    console.log('AuthContext: storedToken exists:', !!storedToken);
    console.log('AuthContext: storedUser exists:', !!storedUser);

    if (storedToken && storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        console.log('AuthContext: Setting initial user and token from localStorage');
        setToken(storedToken);
        setUser(parsedUser);
      } catch (err) {
        console.error('AuthContext: Error parsing stored user:', err);
        // Clear invalid data
        localStorage.removeItem('user');
        localStorage.removeItem('token');
      }
    }

    setIsLoading(false);
  }, []);

  // Login function
  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.login(credentials);

      // Check if email verification is required
      if (response.requiresVerification) {
        // Don't store token/user, but throw a special error for frontend handling
        const verificationError = new Error(response.message || 'Email verification required');
        (verificationError as any).response = { data: response };
        throw verificationError;
      }

      // Store token and user in localStorage for successful login
      if (response.token) {
        localStorage.setItem('token', response.token);
        setToken(response.token);
      }
      if (response.user) {
        localStorage.setItem('user', JSON.stringify(response.user));
        setUser(response.user);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || err.message || 'Login failed. Please try again.';
      setError(errorMessage);

      // Re-throw the error with full response data for frontend handling
      const error = new Error(errorMessage);
      (error as any).response = err.response;
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (credentials: RegisterCredentials) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await authService.register(credentials);

      // Check if email verification is required
      if (response.requiresVerification) {
        // Don't store token/user yet, just return the response
        return response;
      }

      // Legacy flow: Store token and user in localStorage (for existing users)
      if (response.token) {
        localStorage.setItem('token', response.token);
        setToken(response.token);
      }
      if (response.user) {
        localStorage.setItem('user', JSON.stringify(response.user));
        setUser(response.user);
      }

      return response;
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Registration failed. Please try again.';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    setIsLoading(true);

    try {
      await authService.logout();
    } catch (err) {
      console.error('Logout error:', err);
    } finally {
      // Clear state and localStorage
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      setToken(null);
      setUser(null);
      
      // Clear access control state
      dispatch(clearAccessControlOnLogout());
      
      setIsLoading(false);
    }
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  // Set user function
  const setUser = (newUser: User | null) => {
    setError(null);
    if (newUser) {
      localStorage.setItem('user', JSON.stringify(newUser));
    }
    setUserState(newUser);
  };

  // Set token function
  const setToken = (newToken: string | null) => {
    setError(null);
    if (newToken) {
      localStorage.setItem('token', newToken);
    }
    setTokenState(newToken);
  };

  // Compute isAuthenticated value
  const isAuthenticated = !!token;

  // Log authentication state changes
  useEffect(() => {
    console.log('AuthContext: Authentication state changed:', {
      isAuthenticated,
      hasToken: !!token,
      hasUser: !!user
    });
  }, [isAuthenticated, token, user]);

  const value = {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    clearError,
    setUser,
    setToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
