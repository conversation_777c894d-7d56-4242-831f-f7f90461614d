import { useState, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import stripeService from '@/services/stripeService';
import { PricingPlan } from '@/types/subscription';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

interface UseStripeCheckoutReturn {
  isProcessing: boolean;
  error: string | null;
  initiateCheckout: (plan: PricingPlan) => Promise<void>;
  clearError: () => void;
}

export function useStripeCheckout(): UseStripeCheckoutReturn {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  const initiateCheckout = useCallback(async (plan: PricingPlan) => {
    setIsProcessing(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('User not found. Please log in again.');
      }
      const ownerId = await getCachedOwnerIdFromUser(user);
      if (!ownerId) {
        throw new Error('Owner ID not found. Please log in again or contact support.');
      }
      // Create Stripe checkout session
      const session = await stripeService.createCheckoutSession(plan.type, ownerId);
      
      if (session.url) {
        // Redirect to Stripe checkout
        window.location.href = session.url;
      } else {
        throw new Error('No checkout URL received from Stripe');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start checkout process';
      setError(errorMessage);
      
      toast({
        title: "Checkout Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  }, [toast, user]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isProcessing,
    error,
    initiateCheckout,
    clearError,
  };
} 