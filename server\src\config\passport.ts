import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import User from '../models/User';

// Set up Passport with GoogleStrategy for authentication
passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID || '',
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  callbackURL: `${process.env.BACKEND_URL}/v1/auth/google/callback`,
  passReqToCallback: true,
  proxy: true // Enable proxy support for production
},
async (req: any, accessToken: string, refreshToken: string, profile: any, done: any) => {
  try {
    const email = profile.emails?.[0].value;
    if (!email) {
      return done(null, false, { message: 'Email not provided from Google' });
    }

    // Get the state from the request query parameters
    const state = req.query.state || 'login';

    // Check if user exists
    const existingUser = await User.findOne({ 
      $or: [
        { googleId: profile.id },
        { email: email }
      ]
    });

    if (state === 'login') {
      // Login flow
      if (!existingUser) {
        return done(null, false, { 
          message: 'No account found with this email. Please sign up first.',
          redirectTo: 'register',
          state: 'login'
        });
      }
      
      // Update Google ID if not set
      if (!existingUser.googleId) {
        existingUser.googleId = profile.id;
        existingUser.image = profile.photos?.[0].value || existingUser.image;
        existingUser.externalUser = true;
        await existingUser.save();
      }
      
      return done(null, existingUser);
    } else {
      // Signup flow
      if (existingUser) {
        return done(null, false, { 
          message: 'User already exists. Please log in.',
          redirectTo: 'login'
        });
      }

      // Create new user
      const newUser = await new User({
        googleId: profile.id,
        email: email,
        username: profile.displayName,
        firstName: profile.name?.givenName || '',
        lastName: profile.name?.familyName || '',
        image: profile.photos?.[0].value || '',
        externalUser: true
      }).save();

      return done(null, newUser);
    }
  } catch (err) {
    return done(err, false);
  }
}));

// Serialize user into session
passport.serializeUser((user: any, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id: any, done) => {
  try {
    const user = await User.findById(id);
    done(null, user);
  } catch (err) {
    done(err, null);
  }
});
