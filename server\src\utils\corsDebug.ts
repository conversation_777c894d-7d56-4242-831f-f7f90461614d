import express from 'express';

// Debug middleware to log CORS requests
export const corsDebugMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.log('=== CORS Debug Info ===');
  console.log('Request URL:', req.url);
  console.log('Request Method:', req.method);
  console.log('Request Origin:', req.headers.origin);
  console.log('Request Headers:', req.headers);
  console.log('=======================');
  
  // Add CORS headers for debugging
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, stripe-signature');
  res.header('Access-Control-Allow-Credentials', 'true');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS preflight request');
    res.status(200).end();
    return;
  }
  
  next();
};

// Test endpoint to verify CORS is working
export const testCorsEndpoint = (req: express.Request, res: express.Response) => {
  console.log('CORS test endpoint hit');
  res.json({
    message: 'CORS is working!',
    timestamp: new Date().toISOString(),
    origin: req.headers.origin,
    method: req.method
  });
}; 