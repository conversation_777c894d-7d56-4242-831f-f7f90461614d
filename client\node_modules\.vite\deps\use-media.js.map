{"version": 3, "sources": ["../../use-media/lib/utilities/camelToHyphen.js", "../../use-media/lib/utilities/queryObjectToString.js", "../../use-media/lib/utilities/noop.js", "../../use-media/lib/utilities/index.js", "../../use-media/lib/useMedia.js", "../../use-media/lib/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction camelToHyphen(camelString) {\n    return camelString\n        .replace(/[A-Z]/g, function (string) { return \"-\" + string.toLowerCase(); })\n        .toLowerCase();\n}\nexports.default = camelToHyphen;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar camelToHyphen_1 = require(\"./camelToHyphen\");\nvar QUERY_COMBINATOR = ' and ';\nfunction queryObjectToString(query) {\n    if (typeof query === 'string') {\n        return query;\n    }\n    return Object.entries(query)\n        .map(function (_a) {\n        var feature = _a[0], value = _a[1];\n        var convertedFeature = camelToHyphen_1.default(feature);\n        var convertedValue = value;\n        if (typeof convertedValue === 'boolean') {\n            return convertedValue ? convertedFeature : \"not \" + convertedFeature;\n        }\n        if (typeof convertedValue === 'number' &&\n            /[height|width]$/.test(convertedFeature)) {\n            convertedValue = convertedValue + \"px\";\n        }\n        return \"(\" + convertedFeature + \": \" + convertedValue + \")\";\n    })\n        .join(QUERY_COMBINATOR);\n}\nexports.default = queryObjectToString;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction noop() { }\nexports.default = noop;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.noop = exports.queryObjectToString = exports.camelToHyphen = void 0;\nvar camelToHyphen_1 = require(\"./camelToHyphen\");\nObject.defineProperty(exports, \"camelToHyphen\", { enumerable: true, get: function () { return camelToHyphen_1.default; } });\nvar queryObjectToString_1 = require(\"./queryObjectToString\");\nObject.defineProperty(exports, \"queryObjectToString\", { enumerable: true, get: function () { return queryObjectToString_1.default; } });\nvar noop_1 = require(\"./noop\");\nObject.defineProperty(exports, \"noop\", { enumerable: true, get: function () { return noop_1.default; } });\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useMediaLayout = exports.useMedia = exports.mockMediaQueryList = void 0;\nvar react_1 = require(\"react\");\nvar utilities_1 = require(\"./utilities\");\nexports.mockMediaQueryList = {\n    media: '',\n    matches: false,\n    onchange: utilities_1.noop,\n    addListener: utilities_1.noop,\n    removeListener: utilities_1.noop,\n    addEventListener: utilities_1.noop,\n    removeEventListener: utilities_1.noop,\n    dispatchEvent: function (_) { return true; },\n};\nvar createUseMedia = function (effect) { return function (rawQuery, defaultState) {\n    if (defaultState === void 0) { defaultState = false; }\n    var _a = react_1.useState(defaultState), state = _a[0], setState = _a[1];\n    var query = utilities_1.queryObjectToString(rawQuery);\n    effect(function () {\n        var mounted = true;\n        var mediaQueryList = typeof window === 'undefined'\n            ? exports.mockMediaQueryList\n            : window.matchMedia(query);\n        var onChange = function () {\n            if (!mounted) {\n                return;\n            }\n            setState(Boolean(mediaQueryList.matches));\n        };\n        mediaQueryList.addListener(onChange);\n        setState(mediaQueryList.matches);\n        return function () {\n            mounted = false;\n            mediaQueryList.removeListener(onChange);\n        };\n    }, [query]);\n    return state;\n}; };\nexports.useMedia = createUseMedia(react_1.useEffect);\nexports.useMediaLayout = createUseMedia(react_1.useLayoutEffect);\nexports.default = exports.useMedia;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.useMediaLayout = exports.useMedia = exports.default = void 0;\nvar useMedia_1 = require(\"./useMedia\");\nObject.defineProperty(exports, \"default\", { enumerable: true, get: function () { return useMedia_1.default; } });\nObject.defineProperty(exports, \"useMedia\", { enumerable: true, get: function () { return useMedia_1.useMedia; } });\nObject.defineProperty(exports, \"useMediaLayout\", { enumerable: true, get: function () { return useMedia_1.useMediaLayout; } });\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,aAAS,cAAc,aAAa;AAChC,aAAO,YACF,QAAQ,UAAU,SAAU,QAAQ;AAAE,eAAO,MAAM,OAAO,YAAY;AAAA,MAAG,CAAC,EAC1E,YAAY;AAAA,IACrB;AACA,YAAQ,UAAU;AAAA;AAAA;;;ACPlB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,QAAI,kBAAkB;AACtB,QAAI,mBAAmB;AACvB,aAAS,oBAAoB,OAAO;AAChC,UAAI,OAAO,UAAU,UAAU;AAC3B,eAAO;AAAA,MACX;AACA,aAAO,OAAO,QAAQ,KAAK,EACtB,IAAI,SAAU,IAAI;AACnB,YAAI,UAAU,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC;AACjC,YAAI,mBAAmB,gBAAgB,QAAQ,OAAO;AACtD,YAAI,iBAAiB;AACrB,YAAI,OAAO,mBAAmB,WAAW;AACrC,iBAAO,iBAAiB,mBAAmB,SAAS;AAAA,QACxD;AACA,YAAI,OAAO,mBAAmB,YAC1B,kBAAkB,KAAK,gBAAgB,GAAG;AAC1C,2BAAiB,iBAAiB;AAAA,QACtC;AACA,eAAO,MAAM,mBAAmB,OAAO,iBAAiB;AAAA,MAC5D,CAAC,EACI,KAAK,gBAAgB;AAAA,IAC9B;AACA,YAAQ,UAAU;AAAA;AAAA;;;ACxBlB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,aAAS,OAAO;AAAA,IAAE;AAClB,YAAQ,UAAU;AAAA;AAAA;;;ACHlB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,OAAO,QAAQ,sBAAsB,QAAQ,gBAAgB;AACrE,QAAI,kBAAkB;AACtB,WAAO,eAAe,SAAS,iBAAiB,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,gBAAgB;AAAA,IAAS,EAAE,CAAC;AAC1H,QAAI,wBAAwB;AAC5B,WAAO,eAAe,SAAS,uBAAuB,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,sBAAsB;AAAA,IAAS,EAAE,CAAC;AACtI,QAAI,SAAS;AACb,WAAO,eAAe,SAAS,QAAQ,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,OAAO;AAAA,IAAS,EAAE,CAAC;AAAA;AAAA;;;ACRxG;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB,QAAQ,WAAW,QAAQ,qBAAqB;AACzE,QAAI,UAAU;AACd,QAAI,cAAc;AAClB,YAAQ,qBAAqB;AAAA,MACzB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,UAAU,YAAY;AAAA,MACtB,aAAa,YAAY;AAAA,MACzB,gBAAgB,YAAY;AAAA,MAC5B,kBAAkB,YAAY;AAAA,MAC9B,qBAAqB,YAAY;AAAA,MACjC,eAAe,SAAU,GAAG;AAAE,eAAO;AAAA,MAAM;AAAA,IAC/C;AACA,QAAI,iBAAiB,SAAU,QAAQ;AAAE,aAAO,SAAU,UAAU,cAAc;AAC9E,YAAI,iBAAiB,QAAQ;AAAE,yBAAe;AAAA,QAAO;AACrD,YAAI,KAAK,QAAQ,SAAS,YAAY,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACvE,YAAI,QAAQ,YAAY,oBAAoB,QAAQ;AACpD,eAAO,WAAY;AACf,cAAI,UAAU;AACd,cAAI,iBAAiB,OAAO,WAAW,cACjC,QAAQ,qBACR,OAAO,WAAW,KAAK;AAC7B,cAAI,WAAW,WAAY;AACvB,gBAAI,CAAC,SAAS;AACV;AAAA,YACJ;AACA,qBAAS,QAAQ,eAAe,OAAO,CAAC;AAAA,UAC5C;AACA,yBAAe,YAAY,QAAQ;AACnC,mBAAS,eAAe,OAAO;AAC/B,iBAAO,WAAY;AACf,sBAAU;AACV,2BAAe,eAAe,QAAQ;AAAA,UAC1C;AAAA,QACJ,GAAG,CAAC,KAAK,CAAC;AACV,eAAO;AAAA,MACX;AAAA,IAAG;AACH,YAAQ,WAAW,eAAe,QAAQ,SAAS;AACnD,YAAQ,iBAAiB,eAAe,QAAQ,eAAe;AAC/D,YAAQ,UAAU,QAAQ;AAAA;AAAA;;;ACzC1B;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,iBAAiB,QAAQ,WAAW,QAAQ,UAAU;AAC9D,QAAI,aAAa;AACjB,WAAO,eAAe,SAAS,WAAW,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,WAAW;AAAA,IAAS,EAAE,CAAC;AAC/G,WAAO,eAAe,SAAS,YAAY,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,WAAW;AAAA,IAAU,EAAE,CAAC;AACjH,WAAO,eAAe,SAAS,kBAAkB,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,WAAW;AAAA,IAAgB,EAAE,CAAC;AAAA;AAAA;", "names": []}