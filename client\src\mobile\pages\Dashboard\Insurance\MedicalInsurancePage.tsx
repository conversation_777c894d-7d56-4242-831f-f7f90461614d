import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik, Form, FormikHelpers, Field, ErrorMessage } from "formik";
import * as Yup from 'yup';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import insuranceData from '@/data/insurance.json';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  updateUserInput,
  selectError,
} from '@/store/slices/insuranceSlice';
import { QuestionItem, buildValidationSchema, generateInitialValues, handleDependentAnswers, Question } from '@/mobile/components/dashboard/Insurance/FormFields';
import ScrollToQuestion from '@/mobile/components/dashboard/Insurance/ScrollToQuestion';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Get questions for Medical Insurance (section 401B)
const questions = (insuranceData["401"] || []).filter((q: any) => q.sectionId === '401B') as Question[];

export default function MedicalInsurancePage() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [formError, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get data from Redux store
  const userInputs = useAppSelector((state: any) => state.insurance.userInputs);
  const isLoadingRedux = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');
  const fromReview = queryParams.get('fromReview');

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (userInputs && userInputs.length > 0) {
      // Find the input for this category and check for Medical Insurance section
      const userInput = userInputs.find((input: any) =>
        input.originalCategoryId === '8' && input.originalSubCategoryId === '401' &&
        input.answersBySection.some((section: any) => section.originalSectionId === '401B')
      );

      if (userInput && userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);

        // Convert user inputs to form values
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      }
    }
  }, [userInputs, existingInputId]);

  // Handle form submission
  const handleSubmit = async (values: Record<string, any>, { setSubmitting }: FormikHelpers<Record<string, any>>) => {
    try {
      if (!user || !user.id) {
        throw new Error('You must be logged in to save answers');
      }

      // Group answers by section and filter out empty answers
      const answersBySection = questions.reduce((sections: Record<string, any[]>, question: Question) => {
        if (!sections[question.sectionId]) {
          sections[question.sectionId] = [];
        }
        const answer = values[question.id];
        if (answer !== undefined && answer !== '') {
          // Handle type conversion for server validation
          const type = question.type === 'dropdown' ? 'choice' : question.type;
          sections[question.sectionId].push({
            index: sections[question.sectionId].length,
            originalQuestionId: question.id,
            question: question.text,
            type: type,
            answer
          });
        }
        return sections;
      }, {});

      const newAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
        originalSectionId: sectionId,
        isCompleted: true,
        answers: answers as Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>
      }));

      const hasAnswers = newAnswersBySection.some(section => section.answers.length > 0);
      if (!hasAnswers) {
        setSubmitting(false);
        return;
      }

      // Get existing user input to merge with new answers (web version approach)
      const existingUserInput = userInputs.find((input: any) =>
        input.originalCategoryId === '8' && input.originalSubCategoryId === '401'
      );

      let mergedAnswersBySection = [...newAnswersBySection];

      // If there's existing data, merge it with new answers
      if (existingUserInput && existingUserInput.answersBySection) {
        const existingSections = existingUserInput.answersBySection;

        // Create a map of new sections for easy lookup
        const newSectionsMap = new Map(newAnswersBySection.map(section => [section.originalSectionId, section]));

        // Start with existing sections
        mergedAnswersBySection = existingSections.map((existingSection: any) => {
          // If we have new answers for this section, use the new ones
          if (newSectionsMap.has(existingSection.originalSectionId)) {
            return newSectionsMap.get(existingSection.originalSectionId);
          }
          // Otherwise keep the existing section
          return existingSection;
        });

        // Add any completely new sections that weren't in existing data
        newAnswersBySection.forEach(newSection => {
          const existingSection = existingSections.find((section: any) => section.originalSectionId === newSection.originalSectionId);
          if (!existingSection) {
            mergedAnswersBySection.push(newSection);
          }
        });
      }

      if (existingInputId) {
        await dispatch(updateUserInput({
          id: existingInputId,
          userData: {
            userId: user.id,
            categoryId: generateObjectId(),
            originalCategoryId: '8',
            subCategoryId: generateObjectId(),
            originalSubCategoryId: '401',
            answersBySection: mergedAnswersBySection
          } as UserInput
        })).unwrap();
      } else {
        const userData: Omit<UserInput, '_id'> = {
          userId: user.id,
          categoryId: generateObjectId(),
          originalCategoryId: '8',
          subCategoryId: generateObjectId(),
          originalSubCategoryId: '401',
          answersBySection: mergedAnswersBySection
        };
        await dispatch(saveUserInput(userData)).unwrap();
      }
      setSubmitting(false);
      
      // Navigate based on where user came from
      if (fromReview) {
        navigate('/category/insurance/review');
      } else {
        navigate('/category/insurance/life');
      }
    } catch (error: any) {
      setSubmitting(false);
      setError(error.message || 'Failed to save answers. Please try again.');
    }
  };

  // Generate initial values and validation schema
  const initialValues = { ...generateInitialValues(questions), ...savedAnswers };
  const validationSchema = buildValidationSchema(questions, Yup);

  if (isLoading || isLoadingRedux) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Medical Insurance" showAvatar={true} />
        <div className="container mx-auto px-4 py-6 text-center">
          Loading your medical insurance information...
        </div>
      </div>
    );
  }

  if (formError || reduxError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Medical Insurance" showAvatar={true} />
        <div className="container mx-auto px-4 py-6">
          <Alert className="mb-4">
            <AlertDescription>
              {formError || reduxError}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Medical Insurance" showAvatar={true} />
        <div className="container mx-auto px-4 py-6 text-center">
          No questions available for Medical Insurance.
        </div>
      </div>
    );
  }

  return (
    <>
      <GradiantHeader title="Insurance" showAvatar={true} />
      <div className="p-4">
        {/* Tab Bar */}
        <div className="flex flex-row flex-nowrap gap-3 mb-4 bg-gray-50 rounded-lg p-1 overflow-x-auto scrollbar-hide">
          {categoryTabsConfig.insurance.map(tab => {
            const isActive = tab.label === "Medical Insurance";
            return (
              <button
                key={tab.label}
                className={`flex-1 py-1 px-1 rounded-md font-medium whitespace-nowrap ${
                  isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500"
                }`}
                disabled={isActive}
                onClick={() => !isActive && navigate(tab.path)}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Error message */}
        {(formError || reduxError) && (
          <Alert className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        {/* Form */}
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ values, setValues, isSubmitting, isValid, dirty }) => {
            // Handle dependent answers
            useEffect(() => {
              handleDependentAnswers(values, questions, setValues);
            }, [values, setValues]);

            return (
              <Form>
                <div className="bg-gray-50 p-5 rounded-xl shadow-sm border mb-4">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-semibold">
                      Insurance: <span className="text-[#2BCFD5]">Medical Insurance</span>
                    </p>
                    <CircularProgress
                      value={2}
                      max={3}
                      size={40}
                      stroke={3}
                      color="#2BCFD5"
                    />
                  </div>
                </div>

                <ScrollToQuestion questions={questions}>
                  {(refs) => (
                    <div className="space-y-4 mt-8 bg-gray-50 p-5 rounded-xl shadow-sm border">
                      {[...questions]
                        .sort((a, b) => a.order - b.order)
                        .map((question: Question) => (
                          <div
                            key={question.id}
                            id={`question-${question.id}`}
                            ref={(el) => {
                              if (el) refs[question.id] = el;
                            }}
                          >
                            <label className="block font-medium text-gray-700 mb-2">
                              {question.text}
                            </label>
                            {question.type === "boolean" ? (
                              <div className="flex space-x-4">
                                {["yes", "no"].map(option => (
                                  <label
                                    key={option}
                                    className={
                                      "flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer " +
                                      (values[question.id] === option
                                        ? "bg-[#2BCFD5] text-white border-[#2BCFD5]"
                                        : "bg-gray-50 hover:bg-[#25b6bb] hover:text-white")
                                    }
                                    style={{ transition: "all 0.2s" }}
                                  >
                                    <Field type="radio" name={question.id} value={option} className="hidden" />
                                    {option.charAt(0).toUpperCase() + option.slice(1)}
                                  </label>
                                ))}
                              </div>
                            ) : (question.type === "choice" || question.type === "dropdown") && Array.isArray(question.options) ? (
                              <Field as="select" name={question.id} className="w-full border rounded-lg px-3 py-2">
                                <option value="">Select...</option>
                                {question.options.map((option: string) => (
                                  <option key={option} value={option}>{option}</option>
                                ))}
                              </Field>
                            ) : (
                              <Field
                                name={question.id}
                                type="text"
                                className="w-full border rounded-lg px-3 py-2"
                                placeholder={question.placeholder || "Enter your answer"}
                              />
                            )}
                            <ErrorMessage name={question.id} component="div" className="text-red-500 text-sm mt-1" />
                          </div>
                        ))}
                    </div>
                  )}
                </ScrollToQuestion>

                <div className="mt-6 flex justify-between items-center">
                  <button
                    type="button"
                    onClick={() => navigate('/category/insurance/home')}
                    className="text-[#2BCFD5] underline"
                  >
                    ← Back
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                  >
                    {isSubmitting ? 'Saving...' : 'Next →'}
                  </button>
                </div>
              </Form>
            );
          }}
        </Formik>

      </div>
      <Footer />
    </>
  );
}
