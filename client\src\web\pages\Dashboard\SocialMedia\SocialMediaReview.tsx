import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CategoryReviewPage from '@/web/components/Category/CategoryReviewPage';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { useAuth } from '@/contexts/AuthContext';
import socialMediaData from '@/data/socialMedia.json';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  selectUserInputs,
  selectLoading,
  selectError,
  UserInput
} from '@/store/slices/socialMediaSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';
// Map section IDs to their routes
const sectionRoutes: Record<string, string> = {
  '206A': '/category/socialmedia/email',
  '206B': '/category/socialmedia/facebook',
  '206C': '/category/socialmedia/instagram',
  '206D': '/category/socialmedia/otheraccounts',
  '206E': '/category/socialmedia/cellphone',
};

// Map question IDs to their section IDs
const questionToSectionMap: Record<string, string> = {};
Object.entries(socialMediaData).forEach(([categoryId, questions]) => {
  questions.forEach((question: any) => {
    if (question.sectionId) {
      questionToSectionMap[question.id] = question.sectionId;
    }
  });
});

export default function SocialMediaReview() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);

  const userInputs = useAppSelector(selectUserInputs);
  const isLoading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);

  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: createUserInfo(user).avatar
  };

  useEffect(() => {
    const fetchData = async () => {
          if (user?.id) {
            try {
              const ownerId = await getCachedOwnerIdFromUser(user);
              if (ownerId) {
                dispatch(fetchUserInputs(ownerId));
              } else {
                console.error('No owner ID found for user in HomeInstructionsReview component');
              }
            } catch (error) {
              console.error('Error fetching owner ID in HomeInstructionsReview component:', error);
            }
          }
        };
    
        fetchData();
  }, [dispatch, user]);

  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      userInputs.forEach((userInput: UserInput) => {
        userInput.answersBySection.forEach((section) => {
          // Special handling for Other Social Media Accounts (section 206D)
          if (section.originalSectionId === '206D') {
            // Group all s13/s14/s15 sets
            const accountAnswers = section.answers.filter(a =>
              ['s13', 's14', 's15'].includes(a.originalQuestionId)
            );
            for (let i = 0; i < accountAnswers.length; i += 3) {
              const service = accountAnswers[i]?.answer;
              const login = accountAnswers[i + 1]?.answer;
              const password = accountAnswers[i + 2]?.answer;
              if (service || login || password) {
                allTopics.push({
                  id: `other-social-${i / 3}`,
                  title: `Other Social Media Account #${i / 3 + 1}`,
                  subtitle: service ? `Service: ${service}` : undefined,
                  data: `Login: ${login || ''} | Password: ${password || ''}`,
                  onEdit: () => {
                    const route = sectionRoutes['206D'];
                    if (route) navigate(route);
                  }
                });
              }
            }
            // Also show any other non-account answers in this section
            section.answers.forEach((answer) => {
              if (!['s13', 's14', 's15'].includes(answer.originalQuestionId)) {
                const questionId = answer.originalQuestionId;
                const allQuestions = socialMediaData['206'];
                const questionData = allQuestions?.find((q: any) => q.id === questionId);
                const sectionKey = questionToSectionMap[questionId];
                if (questionData) {
                  allTopics.push({
                    id: questionId,
                    title: questionData.text,
                    subtitle: `Section: ${section.originalSectionId}`,
                    data: answer.answer,
                    onEdit: () => {
                      const route = sectionRoutes[sectionKey];
                      if (route) {
                        navigate(`${route}?questionId=${questionId}`);
                      }
                    }
                  });
                }
              }
            });
          } else {
            // Default logic for all other sections
            section.answers.forEach((answer) => {
              const questionId = answer.originalQuestionId;
              const allQuestions = socialMediaData['206'];
              const questionData = allQuestions?.find((q: any) => q.id === questionId);
              const sectionKey = questionToSectionMap[questionId];
              if (questionData) {
                allTopics.push({
                  id: questionId,
                  title: questionData.text,
                  subtitle: `Section: ${section.originalSectionId}`,
                  data: answer.answer,
                  onEdit: () => {
                    const route = sectionRoutes[sectionKey];
                    if (route) {
                      navigate(`${route}?questionId=${questionId}`);
                    }
                  }
                });
              }
            });
          }
        });
      });

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, navigate]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">Loading your answers...</div>;
  }

  if (error) {
    return <div className="flex justify-center items-center h-screen text-red-500">{error}</div>;
  }

  if (!user?.id) {
    return <div className="flex justify-center items-center h-screen text-red-500">You must be logged in to view your answers</div>;
  }

  if (userInputs.length === 0 && !isLoading) {
    return <div className="flex justify-center items-center h-screen">No social media answers found. Please complete some questions first.</div>;
  }

  return (
    <div className="flex flex-col items-center">
    <CategoryReviewPage
      categoryTitle="Social Media and Phone"
      infoTitle="How to edit your information"
      infoDescription="Now, you are about to enter details about your social media and phone accounts, preferences, and essential information to be passed on to your family members. Each section has several questions. Fill out as much as you can/like. You can always come back to fill out more information later."
      topics={topics}
      user={userInfo}
      onPrint={() => window.print()}
      afterTopics={
        <button
          onClick={() => navigate('/dashboard')}
          className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
        >
          Continue to Dashboard
        </button>
      }
    />
    </div>
  );
}
