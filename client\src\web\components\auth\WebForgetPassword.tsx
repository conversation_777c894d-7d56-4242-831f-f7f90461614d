import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import Header from '../Layout/AppHeader';
import WebLayout from '../Layout/WebLayout';
import authService from '@/services/authService';

const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

export default function WebForgetPassword() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    mode: 'onChange',
  });

  const onSubmit = async (data: ForgotPasswordFormValues) => {
    setIsLoading(true);
    setError('');
    setMessage('');

    try {
      const response = await authService.forgotPassword(data.email);
      setMessage(response.message);

      // Show success message for a few seconds, then navigate
      setTimeout(() => {
        navigate('/auth/login', {
          state: {
            message: 'Password reset link sent! Check your email and follow the instructions.'
          }
        });
      }, 3000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <WebLayout subHeaderTitle="Forgot Password">
      <div className="flex flex-col md:flex-row">
        <Header />
        <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
          <div className="bg-white border border-gray-200 rounded-xl shadow-md w-full max-w-md py-20 px-6 space-y-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-[#1F2668]">
                Forgot your password?
              </h2>
              <p className="mt-2 text-lg text-gray-600">
                Enter your email and we'll send you a reset link.
              </p>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="mt-8 space-y-6">
              {/* Success Message */}
              {message && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm text-green-700">{message}</p>
                  <p className="text-xs text-green-600 mt-1">Redirecting to login page...</p>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              )}

              <div>
                <Input
                  type="email"
                  placeholder="Enter your email"
                  {...register('email')}
                  className={`h-12 text-lg ${errors.email ? 'border-red-500' : ''}`}
                  disabled={isLoading}
                />
                {errors.email && (
                  <p className="mt-2 text-sm text-red-500">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full h-12 text-lg bg-[#2BCFD5] hover:bg-[#22BBCC] text-white transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={!isValid || isLoading}
              >
                {isLoading ? 'Sending...' : 'Send Reset Link'}
              </Button>
            </form>
          </div>
        </div>
      </div>
    </WebLayout>
  );
} 