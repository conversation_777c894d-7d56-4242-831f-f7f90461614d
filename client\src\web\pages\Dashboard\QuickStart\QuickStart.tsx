import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { useAuth } from '@/contexts/AuthContext';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { Avatar } from '@radix-ui/react-avatar';
import { createUserInfo, getAvatarUrl } from '@/utils/avatarUtils';

const QuickStart: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const userInfo = createUserInfo(user);

  const handleGetStarted = () => {
    navigate('/category/quickstart/essential');
  };

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      <div className="bg-gradient-to-r from-[#1F4168] to-[#2BCFD5] text-white py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-1">Quick Start</h1>
              <p className="text-sm opacity-80">Get started quickly with essential information</p>
              <button
                onClick={() => navigate('/dashboard')}
                className="flex items-center text-sm hover:underline text-[#2BCFD5] font-semibold text-md mt-1 mb-1"
              >
                <span className="mr-1">&larr;</span> Back to Home
              </button>
            </div>
            <div className="flex items-center">
              <div className="text-right mr-4">
                <div className="font-semibold">{userInfo.name}</div>
                <div className="text-sm opacity-80">{userInfo.email}</div>
              </div>
              <Avatar className="rounded-full w-14 h-14 bg-white overflow-hidden">
                <img
                  src={getAvatarUrl(userInfo.avatar)}
                  alt={userInfo.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = avatar;
                  }}
                />
              </Avatar>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
              <h2 className="text-2xl font-bold mb-4 text-gray-800">Welcome to Quick Start</h2>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">How Quick Start Works</h3>
                <p className="text-blue-700 text-sm">
                  Quick Start helps you fill out essential information that will automatically populate 
                  answers in other categories. This saves you time and ensures consistency across your profile.
                </p>
              </div>

              <div className="space-y-4 mb-6">
                <div className="flex items-start space-x-3">
                  <div className="bg-green-100 rounded-full p-2">
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Essential Information</h4>
                    <p className="text-gray-600 text-sm">Provide basic details like contact information, address, and preferences</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-blue-100 rounded-full p-2">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Auto-Population</h4>
                    <p className="text-gray-600 text-sm">Your answers will automatically fill in relevant fields across all categories</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-purple-100 rounded-full p-2">
                    <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">Time Saving</h4>
                    <p className="text-gray-600 text-sm">Complete your profile faster by avoiding duplicate data entry</p>
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                <Button 
                  onClick={handleGetStarted}
                  className="bg-[#2BCFD5] hover:bg-[#19bbb5] text-white px-6 py-3"
                >
                  Get Started
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => navigate('/dashboard')}
                  className="px-6 py-3"
                >
                  Back to Home
                </Button>
              </div>
            </div>
          </div>

          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
    
  );
};

export default QuickStart; 