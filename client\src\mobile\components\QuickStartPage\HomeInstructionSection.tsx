import React, { useEffect, useState } from 'react';
import { Formik, Form, FormikHelpers, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  UserInput
} from '@/store/slices/homeInstructionsSlice';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import {
  Question,
  buildValidationSchema,
  generateInitialValues
} from '@/mobile/components/dashboard/HomeInstructions/FormFields';
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';
import { castToQuestionType } from '@/mobile/utils/questionUtils';
import QuickStartCardWrapper from './QuickStartCardWrapper';

const HomeInstructionSection: React.FC = () => {
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  // Get questions for Home Location (subcategory '105')
  const questions = useAppSelector((state: any) => state.homeInstructions.questions['105'] || []);
  const loading = useAppSelector((state: any) => state.homeInstructions.loading);
  const userInputs = useAppSelector((state: any) => state.homeInstructions.userInputs);

  // Cast questions to the correct type
  const typedQuestions = useState(() => castToQuestionType(questions))[0];

  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (ownerId) dispatch(fetchUserInputs(ownerId));
      }
    };
    fetchData();
  }, [dispatch, user]);

  useEffect(() => {
    if (!loading) {
      if (userInputs.length > 0) {
        const userInput = userInputs.find((input: UserInput) => input.originalSubCategoryId === '105');
        if (userInput) {
          setSavedAnswers(convertUserInputToFormValues(userInput));
          setExistingInputId(userInput._id || null);
        }
      }
      setIsDataLoaded(true);
    }
  }, [userInputs, loading]);

  const handleSubmit = async (
    values: Record<string, string>,
    { setSubmitting }: FormikHelpers<Record<string, string>>
  ) => {
    try {
      if (!user || !user.id) throw new Error('You must be logged in to save answers');
      const ownerId = await getCachedOwnerIdFromUser(user);
      const answersBySection = typedQuestions.reduce(
        (sections: Record<string, Array<any>>, question: Question) => {
          if (!sections[question.sectionId]) {
            sections[question.sectionId] = [];
          }
          const answer = values[question.id];
          if (answer) {
            sections[question.sectionId].push({
              index: sections[question.sectionId].length,
              originalQuestionId: question.id,
              question: question.text,
              type: question.type,
              answer,
            });
          }
          return sections;
        },
        {}
      );
      const formattedAnswersBySection = Object.entries(answersBySection).map(
        ([sectionId, answers]) => ({
          originalSectionId: sectionId,
          isCompleted: true,
          answers: answers as any[],
        })
      );
      if (existingInputId) {
        await dispatch(
          updateUserInput({
            id: existingInputId,
            userData: {
              userId: user.id,
              categoryId: generateObjectId(),
              originalCategoryId: '1',
              subCategoryId: generateObjectId(),
              originalSubCategoryId: '105',
              answersBySection: formattedAnswersBySection,
            } as UserInput,
          })
        ).unwrap();
      } else {
        const userData: Omit<UserInput, '_id'> = {
          userId: user.id,
          categoryId: generateObjectId(),
          originalCategoryId: '1',
          subCategoryId: generateObjectId(),
          originalSubCategoryId: '105',
          answersBySection: formattedAnswersBySection,
        };
        await dispatch(saveUserInput(userData)).unwrap();
      }
      if (ownerId) {
        await dispatch(fetchUserInputs(ownerId));
      }
      setSubmitting(false);
    } catch (error) {
      setSubmitting(false);
    }
  };

  if (typedQuestions.length === 0 || loading || !isDataLoaded) {
    return <div className="flex justify-center items-center h-40">Loading...</div>;
  }

  const validationSchema = buildValidationSchema(typedQuestions, Yup);
  const baseInitialValues = generateInitialValues(typedQuestions);
  const initialValues = { ...baseInitialValues, ...savedAnswers };

  return (
    <QuickStartCardWrapper>
      <Formik
        key={`${isDataLoaded}-${JSON.stringify(savedAnswers)}`}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize={true}
      >
        {({ values, isSubmitting, isValid, dirty }) => (
          <Form>
            <div className="mb-4">
            </div>
            <div>
              {[...typedQuestions]
                .sort((a: Question, b: Question) => a.order - b.order)
                .map((question: Question) => (
                  <div key={question.id} id={`question-${question.id}`}>
                    {question.type === 'display' ? (
                      <div className="text-lg font-medium text-gray-700 mb-4">
                        {question.text}
                      </div>
                    ) : (
                      <>
                        <label className="block font-medium text-gray-700 mb-2">
                          {question.text}
                        </label>
                        <Field
                          name={question.id}
                          type="text"
                          className="w-full border rounded-lg px-3 py-2 mb-2"
                          placeholder={question.placeholder || question.text}
                        />
                        <ErrorMessage name={question.id} component="div" className="text-red-500 text-sm mt-1" />
                      </>
                    )}
                  </div>
                ))}
              <button
                type="submit"
                disabled={isSubmitting || !isValid || !dirty}
                className="w-full bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold mt-6 hover:bg-[#25b6bb]"
              >
                Save & Continue
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </QuickStartCardWrapper>
  );
};

export default HomeInstructionSection; 