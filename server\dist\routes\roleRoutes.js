"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const roleController_1 = require("../controller/roleController");
const roleValidation_1 = require("../validation/roleValidation");
const authMiddleware_1 = require("../middleware/authMiddleware");
const router = express_1.default.Router();
// Public routes (no authentication required)
router.get('/initialize-defaults', roleController_1.initializeDefaultRoles);
router.get('/all', roleController_1.getAllRoles);
router.get('/name/:name', roleController_1.getRoleByName);
// Protected routes (authentication required)
router.use(authMiddleware_1.combinedAuth); // Apply authentication to all routes below
router.post('/', roleValidation_1.validateCreateRole, roleController_1.createRole);
router.get('/:id', roleController_1.getRoleById);
router.put('/:id', roleValidation_1.validateUpdateRole, roleController_1.updateRole);
router.delete('/:id', roleController_1.deleteRole);
// User role management routes
router.post('/assign', roleController_1.assignRoleToUser);
router.get('/:roleId/users', roleController_1.getUsersByRole);
exports.default = router;
//# sourceMappingURL=roleRoutes.js.map