import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId,
  updateUserInput,
} from '@/store/slices/subscriptionCategorySlice';
import {
  QuestionItem,
  buildValidationSchema,
  generateInitialValues,
} from '@/web/components/Category/Subscription/FormFields';
import ScrollToQuestion from '@/web/components/Category/Subscription/ScrollToQuestion';
import GoodToKnowBox from '@/web/components/Global/GoodToKnowBox';
import SubCategoryFooterNav from '@/web/components/Global/SubCategoryFooterNav';
import SubCategoryHeader from '@/web/components/Global/SubCategoryHeader';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import SubCategoryTitle from '@/web/components/Global/SubCategoryTitle';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { Form, Formik, FormikHelpers, FieldArray, Field } from 'formik';
import { useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import * as Yup from 'yup';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';
import { Plus, Trash2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const OTHER_SERVICES_SECTION_ID = '1104';
const DROPDOWN_QUESTION_ID = 's10';

interface ServiceEntry {
  serviceName: string;
  customServiceName?: string;
  displayName?: string;
  accounts: AccountEntry[];
}

interface AccountEntry {
  username: string;
  password: string;
}

interface FormValues {
  services: ServiceEntry[];
  [key: string]: any;
}

const OtherServices = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const [selectedServices, setSelectedServices] = useState<ServiceEntry[]>([]);
  const [duplicateServiceError, setDuplicateServiceError] = useState<string>('');
  const [highlightedServiceIndex, setHighlightedServiceIndex] = useState<number | null>(null);

  const storeQuestions = useAppSelector(selectQuestionsBySubcategoryId(OTHER_SERVICES_SECTION_ID));
  const loading = useAppSelector(selectLoading);
  const userInputs = useAppSelector(selectUserInputsBySubcategoryId('1104A'));

  const userInput = useMemo(
    () => userInputs.find((input: UserInput) => input.originalSubCategoryId === '1104A'),
    [userInputs]
  );

  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');
  const serviceIndex = queryParams.get('serviceIndex'); // New parameter for service index

  // Get dropdown options from the question
  const dropdownQuestion = storeQuestions.find(q => q.id === DROPDOWN_QUESTION_ID);
  const dropdownOptions = dropdownQuestion?.options || [];

  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in Other Services component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in Other Services component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Load saved services from user input
  useEffect(() => {
    if (userInput && userInput.answersBySection) {
      const services: ServiceEntry[] = [];
      
      userInput.answersBySection.forEach(section => {
        section.answers.forEach(answer => {
          if (answer.originalQuestionId === DROPDOWN_QUESTION_ID) {
            // Parse the service data
            try {
              const serviceData = JSON.parse(answer.answer);
              console.log('Parsed service data:', serviceData);
              if (Array.isArray(serviceData)) {
                // Convert old format to new format if needed
                serviceData.forEach((item: any) => {
                  if (typeof item === 'object' && item.serviceName) {
                    // New format with accounts array
                    const serviceEntry: ServiceEntry = {
                      serviceName: item.serviceName,
                      accounts: item.accounts || [{ username: item.username || '', password: item.password || '' }]
                    };
                    
                    // Handle custom service name for "Other" services
                    if (item.serviceName === 'Other' && item.customServiceName) {
                      serviceEntry.customServiceName = item.customServiceName;
                      serviceEntry.displayName = item.customServiceName;
                    } else if (item.displayName) {
                      serviceEntry.displayName = item.displayName;
                    } else {
                      serviceEntry.displayName = item.serviceName;
                    }
                    
                    console.log('Created service entry:', serviceEntry);
                    services.push(serviceEntry);
                  } else if (typeof item === 'string') {
                    // Old format - convert to new format
                    const oldServiceEntry: ServiceEntry = {
                      serviceName: item,
                      displayName: item,
                      accounts: [{ username: '', password: '' }]
                    };
                    console.log('Created old format service entry:', oldServiceEntry);
                    services.push(oldServiceEntry);
                  }
                });
              }
            } catch (e) {
              console.error('Error parsing service data:', e);
            }
          }
        });
      });
      
      console.log('Setting selected services:', services);
      setSelectedServices(services.length > 0 ? services : []);
    }
  }, [userInput]);

  // Update form when selectedServices changes
  useEffect(() => {
    console.log('Selected services updated:', selectedServices);
  }, [selectedServices]);

  useEffect(() => {
    if (!loading && targetQuestionId) {
      setTimeout(() => {
        // Always scroll to the dropdown question when navigating from review page
        const element = document.getElementById(`question-${DROPDOWN_QUESTION_ID}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.classList.add('bg-yellow-100');
          setTimeout(() => {
            element.classList.remove('bg-yellow-100');
          }, 2000);
        }
      }, 500);
    }
  }, [loading, targetQuestionId]);

  // Handle service highlighting when navigating from review page
  useEffect(() => {
    if (!loading && serviceIndex !== null && selectedServices.length > 0) {
      const index = parseInt(serviceIndex);
      if (index >= 0 && index < selectedServices.length) {
        setHighlightedServiceIndex(index);
        
        // Scroll to the specific service after a delay
        setTimeout(() => {
          const serviceElement = document.getElementById(`service-${index}`);
          if (serviceElement) {
            serviceElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            serviceElement.classList.add('bg-blue-50', 'border-blue-300');
            setTimeout(() => {
              serviceElement.classList.remove('bg-blue-50', 'border-blue-300');
              setHighlightedServiceIndex(null);
            }, 3000);
          }
        }, 1000);
      }
    }
  }, [loading, serviceIndex, selectedServices]);

  const handleSubmit = async (values: FormValues, { setSubmitting }: FormikHelpers<FormValues>) => {
    try {
      if (!user || !user.id) {
        throw new Error('You must be logged in to save answers');
      }

      console.log('Form values:', values);
      console.log('Selected services:', selectedServices);

      // Filter out empty services
      const validServices = selectedServices.filter(service => 
        service.serviceName && (service.accounts.length > 0)
      ).map(service => {
        // For "Other" services, use the custom name if available
        if (service.serviceName === 'Other' && service.customServiceName) {
          return {
            ...service,
            displayName: service.customServiceName
          };
        }
        return service;
      });

      const answersBySection = storeQuestions
        .reduce((sections: Record<string, Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>>, question) => {
          if (!sections[question.sectionId]) {
            sections[question.sectionId] = [];
          }
          
          if (question.id === DROPDOWN_QUESTION_ID) {
            // Save the services array as JSON
            sections[question.sectionId].push({
              index: sections[question.sectionId].length,
              originalQuestionId: question.id,
              question: question.text,
              type: 'choice',
              answer: JSON.stringify(validServices)
            });
          } else {
            // Handle other questions normally
            const answer = values[question.id];
            if (answer !== undefined && answer !== null && answer !== '') {
              let answerType = question.type;
              if (question.type === 'password') {
                answerType = 'text';
              } else if (question.type === 'dropdown') {
                answerType = 'choice';
              }
              
              sections[question.sectionId].push({
                index: sections[question.sectionId].length,
                originalQuestionId: question.id,
                question: question.text,
                type: answerType,
                answer: answer.toString()
              });
            }
          }
          return sections;
        }, {});

      console.log('Answers by section:', answersBySection);

      const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
        originalSectionId: sectionId,
        isCompleted: true,
        answers: answers as Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>
      }));

      console.log('Formatted answers by section:', formattedAnswersBySection);

      const hasAnswers = formattedAnswersBySection.some(section => section.answers.length > 0);
      if (!hasAnswers) {
        console.log('No answers to save');
        setSubmitting(false);
        return;
      }

      const userData = {
        userId: user.id,
        categoryId: generateObjectId(),
        originalCategoryId: '11',
        subCategoryId: generateObjectId(),
        originalSubCategoryId: '1104A',
        answersBySection: formattedAnswersBySection
      };

      if (userInput?._id) {
        console.log('Updating existing input:', userInput._id);
        await dispatch(updateUserInput({
          id: userInput._id,
          userData
        })).unwrap();
      } else {
        console.log('Saving new input');
        await dispatch(saveUserInput(userData)).unwrap();
      }
      setSubmitting(false);
      navigate('/category/subscription/review');
    } catch (error: any) {
      console.error('Error saving answers:', error);
      setSubmitting(false);
    }
  };



  const handleServiceDelete = async (idx: number) => {
    try {
      if (!user || !user.id) {
        throw new Error('You must be logged in to save changes');
      }

      // Remove from local state first
      const newServices = selectedServices.filter((_: ServiceEntry, index: number) => index !== idx);
      setSelectedServices(newServices);

      // Filter out empty services
      const validServices = newServices.filter(service => 
        service.serviceName && (service.accounts.length > 0)
      ).map(service => {
        // For "Other" services, use the custom name if available
        if (service.serviceName === 'Other' && service.customServiceName) {
          return {
            ...service,
            displayName: service.customServiceName
          };
        }
        return service;
      });

      const answersBySection = storeQuestions
        .reduce((sections: Record<string, Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>>, question) => {
          if (!sections[question.sectionId]) {
            sections[question.sectionId] = [];
          }
          
          if (question.id === DROPDOWN_QUESTION_ID) {
            // Save the updated services array as JSON
            sections[question.sectionId].push({
              index: sections[question.sectionId].length,
              originalQuestionId: question.id,
              question: question.text,
              type: 'choice',
              answer: JSON.stringify(validServices)
            });
          }
          return sections;
        }, {});

      const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
        originalSectionId: sectionId,
        isCompleted: true,
        answers: answers as Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>
      }));

      // Save to database
      const userData = {
        userId: user.id,
        categoryId: generateObjectId(),
        originalCategoryId: '11',
        subCategoryId: generateObjectId(),
        originalSubCategoryId: '1104A',
        answersBySection: formattedAnswersBySection
      };

      if (userInput?._id) {
        console.log('Updating existing input after service deletion:', userInput._id);
        await dispatch(updateUserInput({
          id: userInput._id,
          userData
        })).unwrap();
      } else {
        console.log('Saving new input after service deletion');
        await dispatch(saveUserInput(userData)).unwrap();
      }
    } catch (error: any) {
      console.error('Error saving after service deletion:', error);
      // Revert the local state if save fails
      setSelectedServices(selectedServices);
    }
  };

  const checkDuplicateService = (serviceName: string, customServiceName?: string): boolean => {
    return selectedServices.some(service => {
      if (serviceName === 'Other') {
        // For "Other" services, only check if the exact same custom name exists
        return service.serviceName === 'Other' && 
               service.customServiceName && 
               customServiceName && 
               service.customServiceName.toLowerCase() === customServiceName.toLowerCase();
      }
      // For standard services, check if the service name already exists
      return service.serviceName === serviceName;
    });
  };

  const getServiceDisplayName = (service: ServiceEntry): string => {
    if (service.serviceName === 'Other' && service.customServiceName) {
      return service.customServiceName;
    }
    return service.serviceName;
  };

  if (storeQuestions.length === 0 || loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  // Create validation schema for services
  const validationSchema = Yup.object().shape({
    services: Yup.array().of(
      Yup.object().shape({
        serviceName: Yup.string(),
        accounts: Yup.array().of(
          Yup.object().shape({
            username: Yup.string(),
            password: Yup.string()
          })
        )
      })
    )
  });

  const initialValues = {
    services: selectedServices.length > 0 ? selectedServices : []
  };

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      <SubCategoryHeader
        title="Subscription"
        backTo="/dashboard"
        user={{
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
          email: user?.email || '<EMAIL>',
          avatar: createUserInfo(user).avatar
        }}
      />
      <SubCategoryTabs tabs={categoryTabsConfig.subscription} />
      <div className="container mx-auto px-6">
        <SubCategoryTitle
          mainCategory="Subscription"
          category="Other Services"
          description="These files contain questions to help you record your subscription details so they're easy to find later."
        />
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
              >
                {({ values, isSubmitting, isValid, dirty, setFieldValue }) => {
                  console.log('Formik values:', values);
                  console.log('Selected services in form:', selectedServices);
                  
                  return (
                    <Form>
                      <ScrollToQuestion questions={storeQuestions}>
                        {(refs) => (
                          <>
                            {[...storeQuestions]
                              .sort((a, b) => a.order - b.order)
                              .filter((question) => question.id === DROPDOWN_QUESTION_ID) // Only show the dropdown question
                              .map((question) => (
                                <div
                                  key={question.id}
                                  id={`question-${question.id}`}
                                  ref={(el) => {
                                    if (el) refs[question.id] = el;
                                  }}
                                  className="p-4 border rounded-lg mb-4"
                                >
                                  {question.id === DROPDOWN_QUESTION_ID ? (
                                    <div>
                                      <Label className="text-sm font-medium text-gray-700 mb-2">
                                        {question.text}
                                      </Label>
                                      <FieldArray name="services">
                                        {({ push, remove, form }) => {
                                          const services = Array.isArray(form.values.services) 
                                            ? form.values.services 
                                            : [];
                                          
                                          return (
                                            <div>
                                              {services.length === 0 && (
                                                <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                                                  <div className="text-gray-500 mb-4">
                                                    <Plus size={48} className="mx-auto mb-2 text-gray-400" />
                                                    <p className="text-lg font-medium">No subscription services added yet</p>
                                                    <p className="text-sm">Click the button below to add your first subscription service</p>
                                                  </div>
                                                  <button
                                                    type="button"
                                                    className="flex items-center gap-2 mx-auto bg-[#2BCFD5] hover:bg-[#19bbb5] text-white px-6 py-3 rounded-lg font-medium"
                                                    onClick={() => {
                                                      const newService = { serviceName: '', accounts: [{ username: '', password: '' }] };
                                                      push(newService);
                                                      setSelectedServices([...services, newService]);
                                                    }}
                                                  >
                                                    <Plus size={20} /> Add Your First Service
                                                  </button>
                                                </div>
                                              )}
                                              {services.map((service: ServiceEntry, idx: number) => (
                                                <div 
                                                  key={idx} 
                                                  id={`service-${idx}`}
                                                  className="border rounded-lg p-4 mb-4 bg-gray-50"
                                                >
                                                  <div className="flex items-center justify-between mb-3">
                                                    <h4 className="font-medium text-gray-700">Service {idx + 1}</h4>
                                                    <button
                                                      type="button"
                                                      className="text-red-500 hover:text-red-700"
                                                      onClick={() => {
                                                        handleServiceDelete(idx);
                                                      }}
                                                      aria-label="Delete service"
                                                    >
                                                      <Trash2 size={18} />
                                                    </button>
                                                  </div>
                                                  <div className="grid grid-cols-1 gap-3">
                                                    <div>
                                                      <Label className="text-sm font-medium text-gray-700">
                                                        Service Name
                                                      </Label>
                                                      <Select
                                                        value={service.serviceName}
                                                        onValueChange={(value) => {
                                                          // Check if this service already exists (only for standard services)
                                                          if (value !== 'Other') {
                                                            const isDuplicate = checkDuplicateService(value);
                                                            if (isDuplicate) {
                                                              setDuplicateServiceError(`${value} is already added. You can add accounts to that service.`);
                                                              setTimeout(() => setDuplicateServiceError(''), 3000);
                                                              return;
                                                            }
                                                          }
                                                          
                                                          const newServices = [...services];
                                                          newServices[idx] = { 
                                                            ...service, 
                                                            serviceName: value,
                                                            customServiceName: value === 'Other' ? '' : undefined,
                                                            displayName: value === 'Other' ? '' : value
                                                          };
                                                          setFieldValue('services', newServices);
                                                          setSelectedServices(newServices);
                                                          setDuplicateServiceError(''); // Clear any previous error
                                                        }}
                                                      >
                                                        <SelectTrigger className="w-full">
                                                          <SelectValue placeholder="Select a service" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                          {dropdownOptions.map((option: string) => (
                                                            <SelectItem key={option} value={option}>
                                                              {option}
                                                            </SelectItem>
                                                          ))}
                                                        </SelectContent>
                                                      </Select>
                                                    </div>
                                                    
                                                    {/* Custom service name field for "Other" selection */}
                                                    {service.serviceName === 'Other' && (
                                                      <div>
                                                        <Label className="text-sm font-medium text-gray-700">
                                                          What is your other subscription account?
                                                        </Label>
                                                        <Input
                                                          value={service.customServiceName || ''}
                                                          onChange={(e) => {
                                                            const customName = e.target.value;
                                                            
                                                            // Only check for duplicates if a custom name is actually entered
                                                            if (customName.trim()) {
                                                              const isDuplicate = checkDuplicateService('Other', customName);
                                                              if (isDuplicate) {
                                                                setDuplicateServiceError(`${customName} is already added. You can add accounts to that service.`);
                                                                setTimeout(() => setDuplicateServiceError(''), 3000);
                                                                return;
                                                              }
                                                            }
                                                            
                                                            const newServices = [...services];
                                                            newServices[idx] = { 
                                                              ...service, 
                                                              customServiceName: customName,
                                                              displayName: customName || 'Other'
                                                            };
                                                            setFieldValue('services', newServices);
                                                            setSelectedServices(newServices);
                                                            setDuplicateServiceError(''); // Clear any previous error
                                                          }}
                                                          placeholder="Enter your custom service name"
                                                          className="w-full"
                                                        />
                                                      </div>
                                                    )}
                                                    
                                                    {/* Error message for duplicate services */}
                                                    {duplicateServiceError && (
                                                      <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                                                        <p className="text-sm text-red-700">
                                                          ⚠️ {duplicateServiceError}
                                                        </p>
                                                      </div>
                                                    )}
                                                    <FieldArray name={`services.${idx}.accounts`}>
                                                      {({ push: pushAccount, remove: removeAccount, form: accountForm }) => (
                                                        <div>
                                                          <div className="flex items-center justify-between mb-2">
                                                            <Label className="text-sm font-medium text-gray-700">
                                                              Accounts
                                                            </Label>
                                                            <button
                                                              type="button"
                                                              className="flex items-center gap-1 text-[#2BCFD5] hover:text-[#19bbb5] text-sm"
                                                              onClick={() => {
                                                                const newAccount = { username: '', password: '' };
                                                                pushAccount(newAccount);
                                                                const newServices = [...services];
                                                                const newAccounts = [...service.accounts];
                                                                newAccounts.push(newAccount);
                                                                newServices[idx] = { ...service, accounts: newAccounts };
                                                                setFieldValue('services', newServices);
                                                                setSelectedServices(newServices);
                                                              }}
                                                            >
                                                              <Plus size={16} /> Add Account
                                                            </button>
                                                          </div>
                                                          {service.accounts.length === 0 && (
                                                            <div className="text-gray-400 mb-2">No accounts added for this service.</div>
                                                          )}
                                                          {service.accounts.map((account: AccountEntry, accountIdx: number) => (
                                                            <div key={accountIdx} className="border rounded-lg p-3 mb-3 bg-white">
                                                              <div className="flex items-center justify-between mb-2">
                                                                <h5 className="font-medium text-gray-600 text-sm">Account {accountIdx + 1}</h5>
                                                                <button
                                                                  type="button"
                                                                  className="text-red-500 hover:text-red-700"
                                                                  onClick={() => removeAccount(accountIdx)}
                                                                  aria-label="Delete account"
                                                                >
                                                                  <Trash2 size={16} />
                                                                </button>
                                                              </div>
                                                              <div className="grid grid-cols-1 gap-2">
                                                                <div>
                                                                  <Label className="text-xs font-medium text-gray-600">
                                                                    Username
                                                                  </Label>
                                                                  <Input
                                                                    value={account.username}
                                                                    onChange={(e) => {
                                                                      const newServices = [...services];
                                                                      const newAccounts = [...service.accounts];
                                                                      newAccounts[accountIdx] = { ...account, username: e.target.value };
                                                                      newServices[idx] = { ...service, accounts: newAccounts };
                                                                      setFieldValue('services', newServices);
                                                                      setSelectedServices(newServices);
                                                                    }}
                                                                    placeholder="Enter username"
                                                                    className="w-full text-sm"
                                                                  />
                                                                </div>
                                                                <div>
                                                                  <Label className="text-xs font-medium text-gray-600">
                                                                    Password
                                                                  </Label>
                                                                  <Input
                                                                    type="password"
                                                                    value={account.password}
                                                                    onChange={(e) => {
                                                                      const newServices = [...services];
                                                                      const newAccounts = [...service.accounts];
                                                                      newAccounts[accountIdx] = { ...account, password: e.target.value };
                                                                      newServices[idx] = { ...service, accounts: newAccounts };
                                                                      setFieldValue('services', newServices);
                                                                      setSelectedServices(newServices);
                                                                    }}
                                                                    placeholder="Enter password"
                                                                    className="w-full text-sm"
                                                                  />
                                                                </div>
                                                              </div>
                                                            </div>
                                                          ))}
                                                        </div>
                                                      )}
                                                    </FieldArray>
                                                  </div>
                                                </div>
                                              ))}
                                              <button
                                                type="button"
                                                className="flex items-center gap-1 text-[#2BCFD5] hover:text-[#19bbb5] font-medium mt-2"
                                                onClick={() => {
                                                  const newService = { serviceName: '', accounts: [{ username: '', password: '' }] };
                                                  push(newService);
                                                  setSelectedServices([...services, newService]);
                                                }}
                                              >
                                                <Plus size={18} /> Add Service
                                              </button>
                                              
                                              {/* Helpful message about multiple "Other" services */}
                                              {services.length > 0 && (
                                                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                                  <p className="text-sm text-blue-700">
                                                    💡 <strong>Tip:</strong> You can add multiple "Other" services. Each service can have its own custom name and multiple accounts.
                                                  </p>
                                                </div>
                                              )}
                                            </div>
                                          );
                                        }}
                                      </FieldArray>
                                    </div>
                                  ) : null}
                                </div>
                              ))}
                          </>
                        )}
                      </ScrollToQuestion>
                      
                      {/* Save Button - Always Visible */}
                      <div className="mt-8 flex justify-between items-center">
                        <div className="text-sm text-gray-600">
                          {selectedServices.length > 0 && (
                            <span>
                              {selectedServices.length} service{selectedServices.length !== 1 ? 's' : ''} added
                              {selectedServices.reduce((total, service) => total + service.accounts.length, 0) > 0 && (
                                <span className="ml-2">
                                  • {selectedServices.reduce((total, service) => total + service.accounts.length, 0)} account{selectedServices.reduce((total, service) => total + service.accounts.length, 0) !== 1 ? 's' : ''}
                                </span>
                              )}
                            </span>
                          )}
                        </div>
                        <div className="flex gap-3">
                          <Button
                            type="submit"
                            disabled={isSubmitting}
                            className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                          >
                            {isSubmitting ? 'Saving...' : 'Save & Review'}
                          </Button>
                        </div>
                      </div>
                      <GoodToKnowBox
                        title="Editing my Answers"
                        description="Each topic below is a part of your subscription services, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you."
                      />
                      <SubCategoryFooterNav
                        leftLabel="Amazon Prime"
                        leftTo="/category/subscription/amazonprime"
                        rightLabel="Review"
                        rightTo="/category/subscription/review"
                      />
                    </Form>
                  );
                }}
              </Formik>
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default OtherServices;
