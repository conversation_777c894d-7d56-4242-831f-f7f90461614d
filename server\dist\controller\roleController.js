"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUsersByRole = exports.assignRoleToUser = exports.initializeDefaultRoles = exports.deleteRole = exports.updateRole = exports.getRoleByName = exports.getRoleById = exports.getAllRoles = exports.createRole = void 0;
const Role_1 = __importDefault(require("../models/Role"));
// Create a new role
const createRole = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, description, permissions, isActive } = req.body;
        // Check if role already exists
        const existingRole = yield Role_1.default.findOne({ name });
        if (existingRole) {
            res.status(400).json({
                status: 'fail',
                message: `Role with name '${name}' already exists`
            });
            return;
        }
        const role = new Role_1.default({
            name,
            description,
            permissions,
            isActive: isActive !== undefined ? isActive : true
        });
        yield role.save();
        res.status(201).json({
            status: 'success',
            message: 'Role created successfully',
            data: { role }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error creating role',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.createRole = createRole;
// Get all roles
const getAllRoles = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { isActive } = req.query;
        const filter = {};
        if (isActive !== undefined) {
            filter.isActive = isActive === 'true';
        }
        const roles = yield Role_1.default.find(filter).sort({ createdAt: -1 });
        res.status(200).json({
            status: 'success',
            results: roles.length,
            data: { roles }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error fetching roles',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getAllRoles = getAllRoles;
// Get role by ID
const getRoleById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const role = yield Role_1.default.findById(id);
        if (!role) {
            res.status(404).json({
                status: 'fail',
                message: 'Role not found'
            });
            return;
        }
        res.status(200).json({
            status: 'success',
            data: { role }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error fetching role',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getRoleById = getRoleById;
// Get role by name
const getRoleByName = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name } = req.params;
        const role = yield Role_1.default.findOne({ name });
        if (!role) {
            res.status(404).json({
                status: 'fail',
                message: 'Role not found'
            });
            return;
        }
        res.status(200).json({
            status: 'success',
            data: { role }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error fetching role',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getRoleByName = getRoleByName;
// Update role
const updateRole = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const { description, permissions, isActive } = req.body;
        const role = yield Role_1.default.findById(id);
        if (!role) {
            res.status(404).json({
                status: 'fail',
                message: 'Role not found'
            });
            return;
        }
        // Update only provided fields
        if (description !== undefined)
            role.description = description;
        if (permissions !== undefined)
            role.permissions = permissions;
        if (isActive !== undefined)
            role.isActive = isActive;
        yield role.save();
        res.status(200).json({
            status: 'success',
            message: 'Role updated successfully',
            data: { role }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error updating role',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.updateRole = updateRole;
// Delete role (soft delete by setting isActive to false)
const deleteRole = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const role = yield Role_1.default.findById(id);
        if (!role) {
            res.status(404).json({
                status: 'fail',
                message: 'Role not found'
            });
            return;
        }
        role.isActive = false;
        yield role.save();
        res.status(200).json({
            status: 'success',
            message: 'Role deleted successfully'
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error deleting role',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.deleteRole = deleteRole;
// Initialize default roles
const initializeDefaultRoles = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const defaultRoles = Role_1.default.getDefaultRoles();
        const createdRoles = [];
        for (const roleData of defaultRoles) {
            const existingRole = yield Role_1.default.findOne({ name: roleData.name });
            if (!existingRole) {
                const role = new Role_1.default(roleData);
                yield role.save();
                createdRoles.push(role);
            }
        }
        res.status(200).json({
            status: 'success',
            message: `Default roles initialized. Created ${createdRoles.length} new roles.`,
            data: { createdRoles }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error initializing default roles',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.initializeDefaultRoles = initializeDefaultRoles;
// Assign role to user
const assignRoleToUser = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId, roleId } = req.body;
        if (!userId || !roleId) {
            res.status(400).json({
                status: 'fail',
                message: 'User ID and Role ID are required'
            });
            return;
        }
        // Check if role exists
        const role = yield Role_1.default.findById(roleId);
        if (!role) {
            res.status(404).json({
                status: 'fail',
                message: 'Role not found'
            });
            return;
        }
        // Check if user exists and update their role
        const User = require('../models/User').default;
        const user = yield User.findById(userId);
        if (!user) {
            res.status(404).json({
                status: 'fail',
                message: 'User not found'
            });
            return;
        }
        user.roleId = roleId;
        yield user.save();
        res.status(200).json({
            status: 'success',
            message: 'Role assigned to user successfully',
            data: {
                userId: user._id,
                roleId: role._id,
                roleName: role.name
            }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error assigning role to user',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.assignRoleToUser = assignRoleToUser;
// Get users by role
const getUsersByRole = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { roleId } = req.params;
        // Check if role exists
        const role = yield Role_1.default.findById(roleId);
        if (!role) {
            res.status(404).json({
                status: 'fail',
                message: 'Role not found'
            });
            return;
        }
        // Get users with this role
        const User = require('../models/User').default;
        const users = yield User.find({ roleId }).populate('roleId', 'name description permissions');
        res.status(200).json({
            status: 'success',
            results: users.length,
            data: {
                role: role.name,
                users
            }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error fetching users by role',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getUsersByRole = getUsersByRole;
//# sourceMappingURL=roleController.js.map