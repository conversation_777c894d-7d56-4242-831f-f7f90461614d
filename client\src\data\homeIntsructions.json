{"101": [{"id": "q1", "text": "Do you have pets?", "type": "boolean", "required": false, "sectionId": "101A", "order": 1}, {"id": "q2", "text": "How Many?", "type": "choice", "required": false, "sectionId": "101A", "order": 2, "options": ["1", "2", "3", "4", "5", "More than 5"], "dependsOn": {"questionId": "q1", "value": "yes"}}, {"id": "q3", "text": "What is your pet's name?", "type": "text", "required": false, "sectionId": "101B", "validationRules": {"minLength": 1}, "order": 3, "dependsOn": {"questionId": "q1", "value": "yes"}}, {"id": "q4", "text": "Do you have instructions for your pets?", "type": "text", "required": false, "sectionId": "101B", "validationRules": {"maxLength": 275}, "order": 4, "dependsOn": {"questionId": "q1", "value": "yes"}}, {"id": "q5", "text": "Who is the pet's veterinarian?", "type": "text", "required": false, "sectionId": "101C", "order": 5, "dependsOn": {"questionId": "q1", "value": "yes"}}, {"id": "q6", "text": "What is their contact information?", "placeholder": "Phone Number", "type": "number", "required": false, "sectionId": "101C", "order": 6, "dependsOn": {"questionId": "q1", "value": "yes"}}, {"id": "q7", "text": "Enter their address", "placeholder": "Address", "type": "text", "required": false, "sectionId": "101C", "order": 7, "dependsOn": {"questionId": "q1", "value": "yes"}}], "102": [{"id": "t1", "text": "What day of the week is your trash collected?", "type": "choice", "required": false, "sectionId": "102A", "options": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "order": 1}], "103": [{"id": "o1", "text": "Do you have any other specific home instructions about your home?", "type": "text", "required": false, "sectionId": "103A", "validationRules": {"maxLength": 275}, "order": 1}], "104": [{"id": "s1", "text": "Do you have a home security system?", "type": "boolean", "required": false, "sectionId": "104A", "order": 1}, {"id": "s2", "text": "Which company provides your security system?", "type": "choice", "required": false, "sectionId": "104B", "options": ["ADT", "SimpliSafe", "<PERSON><PERSON><PERSON>", "Frontpoint", "Ring Alarm", "Other"], "order": 2, "dependsOn": {"questionId": "s1", "value": "yes"}}, {"id": "s3", "text": "If Other, please specify:", "type": "text", "required": false, "sectionId": "104B", "order": 3, "dependsOn": {"questionId": "s2", "value": "Other"}}, {"id": "s4", "text": "What is your account number?", "type": "text", "required": false, "sectionId": "104B", "order": 4, "dependsOn": {"questionId": "s1", "value": "yes"}}, {"id": "s5", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "104B", "order": 5, "dependsOn": {"questionId": "s1", "value": "yes"}}, {"id": "s6", "text": "What is your username?", "type": "text", "required": false, "sectionId": "104B", "order": 6, "dependsOn": {"questionId": "s5", "value": "yes"}}, {"id": "s7", "text": "What is your password?", "type": "text", "required": false, "sectionId": "104B", "order": 7, "dependsOn": {"questionId": "s5", "value": "yes"}}, {"id": "s8", "text": "What is your security code?", "type": "text", "required": false, "sectionId": "104B", "order": 8, "dependsOn": {"questionId": "s1", "value": "yes"}}], "105": [{"id": "l1", "text": "What is the address of the property you will be adding information about?", "type": "display", "required": false, "sectionId": "105A", "order": 1}, {"id": "l2", "text": "Street", "type": "text", "required": false, "sectionId": "105B", "order": 2}, {"id": "l3", "text": "City", "type": "text", "required": false, "sectionId": "105B", "order": 3}, {"id": "l4", "text": "State", "type": "text", "required": false, "sectionId": "105B", "order": 4}, {"id": "l5", "text": "ZIP Code", "type": "text", "required": false, "sectionId": "105B", "order": 5}]}