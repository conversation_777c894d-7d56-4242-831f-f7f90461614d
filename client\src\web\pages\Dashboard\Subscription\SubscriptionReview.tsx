import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CategoryReviewPage from '@/web/components/Category/CategoryReviewPage';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { useAuth } from '@/contexts/AuthContext';
import subscriptionData from '@/data/subscription.json';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  selectUserInputs,
  selectLoading,
  selectError,
  UserInput
} from '@/store/slices/subscriptionCategorySlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';

// Map section IDs to their routes
const sectionRoutes: Record<string, string> = {
  '1101A': '/category/subscription/netflix',
  '1102A': '/category/subscription/appletv',
  '1103A': '/category/subscription/amazonprime',
  '1104A': '/category/subscription/otherservices',
};

// Map question IDs to their section IDs
const questionToSectionMap: Record<string, string> = {};
Object.entries(subscriptionData).forEach(([categoryId, questions]) => {
  questions.forEach((question: any) => {
    if (question.sectionId) {
      questionToSectionMap[question.id] = question.sectionId;
    }
  });
});

const SubscriptionReview = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);

  const userInputs = useAppSelector(selectUserInputs);
  const isLoading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);

  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: createUserInfo(user).avatar
  };

  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in SubscriptionReview component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in SubscriptionReview component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      userInputs.forEach((userInput: UserInput) => {
        userInput.answersBySection.forEach((section) => {
          // Special handling for Other Services (section 1104A)
          if (section.originalSectionId === '1104A') {
            // Find the main dropdown selection
            const dropdownAnswer = section.answers.find(a => a.originalQuestionId === 's10');
            
            if (dropdownAnswer?.answer) {
              try {
                // Parse the JSON structure for services
                const servicesData = JSON.parse(dropdownAnswer.answer);
                
                if (Array.isArray(servicesData)) {
                  // Handle new structure with multiple services and accounts
                  servicesData.forEach((service: any, index: number) => {
                    if (service.serviceName && service.accounts && Array.isArray(service.accounts)) {
                      const serviceName = service.serviceName;
                      const accounts = service.accounts.filter((account: any) => 
                        account.username || account.password
                      );
                      
                      if (accounts.length > 0) {
                        // Format accounts for display in the requested format
                        const displayName = service.displayName || service.serviceName;
                        let accountsDisplay = `service name: ${displayName}\n`;
                        accounts.forEach((account: any, accountIndex: number) => {
                          accountsDisplay += `Account ${accountIndex + 1}\n`;
                          const parts = [];
                          if (account.username) {
                            parts.push(`username: ${account.username}`);
                          }
                          if (account.password) {
                            parts.push(`password: ${account.password}`);
                          }
                          accountsDisplay += parts.join('  |  ') + '\n';
                        });
                        
                        allTopics.push({
                          id: `service-${displayName}-${index}`,
                          title: displayName,
                          subtitle: `${accounts.length} account${accounts.length !== 1 ? 's' : ''}`,
                          data: accountsDisplay.trim(),
                          onEdit: () => {
                            const route = sectionRoutes['1104A'];
                            if (route) {
                              // Pass the service index for highlighting
                              navigate(`${route}?serviceIndex=${index}`);
                            }
                          }
                        });
                      } else {
                        // Service with no valid accounts
                        const displayName = service.displayName || service.serviceName;
                        allTopics.push({
                          id: `service-${displayName}-${index}`,
                          title: displayName,
                          subtitle: 'Subscription Service',
                          data: 'No credentials provided',
                          onEdit: () => {
                            const route = sectionRoutes['1104A'];
                            if (route) {
                              // Pass the service index for highlighting
                              navigate(`${route}?serviceIndex=${index}`);
                            }
                          }
                        });
                      }
                    }
                  });
                } else {
                  // Handle old single service format
                  const selectedService = dropdownAnswer.answer;
                  
                  if (selectedService === 'Other') {
                    // Handle "Other" service with custom name
                    const serviceNameAnswer = section.answers.find(a => a.originalQuestionId === 's11');
                    const serviceName = serviceNameAnswer?.answer || 'Custom Service';
                    
                    allTopics.push({
                      id: 'other-service-custom',
                      title: serviceName,
                      subtitle: 'Custom Service',
                      data: 'Service added manually',
                      onEdit: () => {
                        const route = sectionRoutes['1104A'];
                        if (route) {
                          // For old format, we don't have a specific index, so just navigate
                          navigate(route);
                        }
                      }
                    });
                  } else {
                    // Handle specific service selection (old format)
                    const usernameAnswer = section.answers.find(a => 
                      a.originalQuestionId === 's12' || // Disney+
                      a.originalQuestionId === 's14' || // Hulu
                      a.originalQuestionId === 's16' || // Max
                      a.originalQuestionId === 's18' || // Peacock
                      a.originalQuestionId === 's20' || // Paramount+
                      a.originalQuestionId === 's22' || // Apple TV+
                      a.originalQuestionId === 's24' || // YouTube TV
                      a.originalQuestionId === 's26'    // Sling TV
                    );
                    
                    const passwordAnswer = section.answers.find(a => 
                      a.originalQuestionId === 's13' || // Disney+
                      a.originalQuestionId === 's15' || // Hulu
                      a.originalQuestionId === 's17' || // Max
                      a.originalQuestionId === 's19' || // Peacock
                      a.originalQuestionId === 's21' || // Paramount+
                      a.originalQuestionId === 's23' || // Apple TV+
                      a.originalQuestionId === 's25' || // YouTube TV
                      a.originalQuestionId === 's27'    // Sling TV
                    );
                    
                    const credentials = [];
                    if (usernameAnswer?.answer) {
                      credentials.push(`Username: ${usernameAnswer.answer}`);
                    }
                    if (passwordAnswer?.answer) {
                      credentials.push(`Password: ${passwordAnswer.answer}`);
                    }
                    
                    allTopics.push({
                      id: `service-${selectedService}`,
                      title: selectedService,
                      subtitle: 'Subscription Service',
                      data: credentials.length > 0 ? credentials.join(' | ') : 'No credentials provided',
                      onEdit: () => {
                        const route = sectionRoutes['1104A'];
                        if (route) {
                          // For old format, we don't have a specific index, so just navigate
                          navigate(route);
                        }
                      }
                    });
                  }
                }
              } catch (error) {
                console.error('Error parsing service data:', error);
                // Fallback to old format
                const selectedService = dropdownAnswer.answer;
                allTopics.push({
                  id: `service-${selectedService}`,
                  title: selectedService,
                  subtitle: 'Subscription Service',
                  data: 'Error parsing service data',
                  onEdit: () => {
                    const route = sectionRoutes['1104A'];
                    if (route) navigate(route);
                  }
                });
              }
            }
            
            // Also show any other answers in this section that aren't part of the main service selection
            section.answers.forEach((answer) => {
              if (!['s10', 's11', 's12', 's13', 's14', 's15', 's16', 's17', 's18', 's19', 's20', 's21', 's22', 's23', 's24', 's25', 's26', 's27'].includes(answer.originalQuestionId)) {
                const questionId = answer.originalQuestionId;
                const allQuestions = subscriptionData['1104'];
                const questionData = allQuestions?.find((q: any) => q.id === questionId);
                if (questionData) {
                  allTopics.push({
                    id: questionId,
                    title: questionData.text,
                    subtitle: `Section: ${section.originalSectionId}`,
                    data: answer.answer,
                    onEdit: () => {
                      const route = sectionRoutes['1104A'];
                      if (route) {
                        navigate(`${route}?questionId=${questionId}`);
                      }
                    }
                  });
                }
              }
            });
          } else {
            // Default logic for all other sections
            section.answers.forEach((answer) => {
              const questionId = answer.originalQuestionId;
              const sectionKey = questionToSectionMap[questionId];
              
              // Find the question data from the appropriate section
              let questionData = null;
              let allQuestions = null;
              
              // Map section IDs to their data keys
              const sectionDataMap: Record<string, string> = {
                '1101A': '1101', // Netflix
                '1102A': '1102', // Apple TV
                '1103A': '1103', // Amazon Prime
                '1104A': '1104', // Other Services
              };
              
              const dataKey = sectionDataMap[section.originalSectionId];
              if (dataKey && dataKey in subscriptionData) {
                allQuestions = subscriptionData[dataKey as keyof typeof subscriptionData];
                questionData = allQuestions?.find((q: any) => q.id === questionId);
              }
              
              if (questionData) {
                allTopics.push({
                  id: questionId,
                  title: questionData.text,
                  subtitle: `Section: ${section.originalSectionId}`,
                  data: answer.answer,
                  onEdit: () => {
                    const route = sectionRoutes[sectionKey];
                    if (route) {
                      navigate(`${route}?questionId=${questionId}`);
                    }
                  }
                });
              }
            });
          }
        });
      });

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, navigate]);

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">Loading your answers...</div>;
  }

  if (error) {
    return <div className="flex justify-center items-center h-screen text-red-500">{error}</div>;
  }

  if (!user?.id) {
    return <div className="flex justify-center items-center h-screen text-red-500">You must be logged in to view your answers</div>;
  }

  if (userInputs.length === 0 && !isLoading) {
    return <div className="flex justify-center items-center h-screen">No subscription answers found. Please complete some questions first.</div>;
  }

  return (
    <div className="flex flex-col items-center">
      <CategoryReviewPage
        categoryTitle="Subscription"
        infoTitle="Your Subscription Information"
        infoDescription="Review all your subscription service information below. You can edit any section by clicking the edit button."
        topics={topics}
        user={userInfo}
        onPrint={() => window.print()}
        afterTopics={
          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">Next Steps</h3>
            <p className="text-blue-700 text-sm">
              Your subscription information has been saved. You can always come back to update this information
              or add new subscription services as needed.
            </p>
            <button
              onClick={() => navigate('/dashboard')}
              className="mt-4 px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md"
            >
              Continue to Dashboard
            </button>
          </div>
        }
      />
    </div>
  );
};

export default SubscriptionReview;
