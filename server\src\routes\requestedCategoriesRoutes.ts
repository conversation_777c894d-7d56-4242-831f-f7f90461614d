import express from 'express';
import { 
    requestCategories, 
    getMyRequests, 
    getOwnerRequests, 
    approveRequest,
    approveRequestFromDashboard
} from '../controller/requestedCategoriesController';
import { 
    requestCategoriesValidation, 
    approveRequestValidation,
    approveRequestFromDashboardValidation
} from '../validation/requestedCategoriesValidation';
import { combinedAuth } from '../middleware/authMiddleware';

const router = express.Router();

// Protected routes - Nominee/Family can request categories
router.post('/request', combinedAuth, requestCategoriesValidation, requestCategories);

// Protected routes - Users can view their own requests
router.get('/my-requests', combinedAuth, getMyRequests);

// Protected routes - Owner can view requests
router.get('/owner-requests', combinedAuth, getOwnerRequests);

// Protected routes - Owner can approve/reject requests from dashboard
router.post('/approve-from-dashboard', combinedAuth, approveRequestFromDashboardValidation, approveRequestFromDashboard);

// Public route - Approve/reject request (no auth required as it uses token)
router.post('/approve', approveRequestValidation, approveRequest);

export default router;
