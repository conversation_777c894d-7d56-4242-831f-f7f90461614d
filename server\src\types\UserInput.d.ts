import { Types } from 'mongoose';

// Interface for a single answer
export interface IAnswer {
  index?: number;
  questionId: Types.ObjectId;
  originalQuestionId?: string; // Our manual question ID (q1, q2, etc.)
  type?: string;
  answer: string;
  is_encrypted?: boolean;
  question?: string;
}

// Interface for answers by section
export interface IAnswerBySection {
  sectionId: Types.ObjectId;
  originalSectionId?: string; // Our manual section ID (101A, 101B, etc.)
  isCompleted: boolean;
  answers: IAnswer[];
}

// Main document interface
export interface IUserInput extends Document {
  sectionId?: Types.ObjectId;
  originalSectionId?: string;
  isCompleted?: boolean;
  userId: Types.ObjectId;
  ownerId?: Types.ObjectId; // Made optional for backward compatibility
  categoryId?: Types.ObjectId;
  originalCategoryId?: string; // Our manual category ID (1, 2, etc.)
  subCategoryId?: Types.ObjectId;
  originalSubCategoryId?: string; // Our manual subcategory ID (101, 102, etc.)
  answers?: IAnswer[];
  answersBySection?: IAnswerBySection[];
}