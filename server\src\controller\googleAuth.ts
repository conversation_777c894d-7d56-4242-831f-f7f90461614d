import { Request, Response } from 'express';
import passport from "passport";
import User from '../models/User';
import jwt from 'jsonwebtoken';

interface IUser {
    _id: string;
    email: string;
    googleId: string;
    username: string;
    image?: string;
    externalUser: boolean;
    firstName?: string;
    lastName?: string;
}

// Google authentication route
export const googleAuth = passport.authenticate('google', {
    scope: ['profile', 'email']
});

// Handle the callback after Google has authenticated the user
export const googleAuthCallback = (req: Request, res: Response) => {
    if (!req.user) {
        const authInfo = req.authInfo as { message: string; redirectTo?: string; state?: string } | undefined;
        const error = authInfo?.message || 'Authentication failed';
        const redirectTo = authInfo?.redirectTo || 'login';
        const state = authInfo?.state || 'login';

        if (state === 'login' && redirectTo === 'register') {
            return res.redirect(
                `${process.env.FRONTEND_URL}/auth/google/callback?success=false&error=${encodeURIComponent(error)}&redirectTo=register`
            );
        }

        return res.redirect(
            `${process.env.FRONTEND_URL}/auth/google/callback?success=false&error=${encodeURIComponent(error)}&redirectTo=${redirectTo}`
        );
    }

    // Successful authentication
    const user = req.user as IUser;

    // Create token with both id and userId for compatibility
    const token = jwt.sign(
        {
            id: user._id,
            userId: user._id,  // Include both formats for compatibility
            email: user.email
        },
        process.env.JWT_SECRET || 'default',
        { expiresIn: '24h' }
    );

    console.log('Creating token for user:', user._id);

    // Prepare user data for frontend
    const userData = {
        id: user._id,
        _id: user._id,  // Include both formats for compatibility
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        image: user.image,
        externalUser: true,
        isNewUser: !user.firstName || !user.lastName
    };

    // Redirect to frontend with token and user data
    // Make sure to properly encode the token and user data
    const encodedToken = encodeURIComponent(token);
    const encodedUserData = encodeURIComponent(JSON.stringify(userData));

    console.log('Google Auth Callback: Redirecting to frontend with token and user data');
    console.log('Token length:', token.length);
    console.log('User data:', userData);

    const redirectUrl = `${process.env.FRONTEND_URL}/auth/google/callback?success=true&token=${encodedToken}&user=${encodedUserData}`;
    res.redirect(redirectUrl);
};

// Error handling for authentication failure
export const googleAuthFailure = (req: Request, res: Response) => {
    const error = 'Authentication failed. Please try again.';
    res.redirect(`${process.env.FRONTEND_URL}/auth/google/callback?success=false&error=${encodeURIComponent(error)}`);
};

// Profile details handler
export const profileDetails = (req: Request, res: Response): void => {
    try {
        const user = req.user;

        if (!user) {
            res.status(401).json({ message: 'Unauthorized: User not found in request' });
            return;
        }

        res.status(200).json({ user: user, message: 'Profile retrieved successfully' });
    } catch (error) {
        console.error('Error in profileDetails:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};
