import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import quickStartService, { QuickStartQuestion, QuickStartAnswer } from '@/services/quickStartService';

// Types
interface QuickStartState {
  questions: QuickStartQuestion[];
  answers: Record<string, string>;
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
  lastSyncResult: any;
  progress: {
    answered: number;
    total: number;
  };
}

// Initial state
const initialState: QuickStartState = {
  questions: [],
  answers: {},
  isLoading: false,
  isSaving: false,
  error: null,
  lastSyncResult: null,
  progress: {
    answered: 0,
    total: 0
  }
};

// Async thunks
export const loadQuickStartData = createAsyncThunk(
  'quickStart/loadData',
  async (userId: string, { getState, rejectWithValue }) => {
    const state = getState() as { quickStart: QuickStartState };

    // Prevent duplicate calls if already loading or if data is already loaded
    if (state.quickStart.isLoading) {
      return rejectWithValue('Already loading');
    }

    // If we already have questions and it's the same user, skip loading
    if (state.quickStart.questions.length > 0 && Object.keys(state.quickStart.answers).length > 0) {
      return {
        questions: state.quickStart.questions,
        existingAnswers: Object.entries(state.quickStart.answers).map(([questionId, answer]) => ({
          questionId,
          answer,
          sharedKey: ''
        }))
      };
    }

    const questions = quickStartService.getQuickStartQuestions();
    const existingAnswers = await quickStartService.getExistingQuickStartAnswers(userId);

    return {
      questions,
      existingAnswers
    };
  }
);

export const saveQuickStartAnswers = createAsyncThunk(
  'quickStart/saveAnswers',
  async ({ userId, answers }: { userId: string; answers: Record<string, string> }) => {
    const quickStartAnswers: QuickStartAnswer[] = Object.entries(answers)
      .filter(([_, value]) => value && value.trim() !== '')
      .map(([questionId, answer]) => ({
        questionId,
        answer: answer.trim(),
        sharedKey: '' // Will be filled by service
      }));

    const result = await quickStartService.syncQuickStartAnswers(userId, quickStartAnswers);
    return result;
  }
);

export const deleteQuickStartAnswer = createAsyncThunk(
  'quickStart/deleteAnswer',
  async ({ userId, questionId }: { userId: string; questionId: string }) => {
    const result = await quickStartService.deleteQuickStartAnswers(userId, [questionId]);
    return { result, questionId };
  }
);

// Slice
const quickStartSlice = createSlice({
  name: 'quickStart',
  initialState,
  reducers: {
    setAnswer: (state, action: PayloadAction<{ questionId: string; answer: string }>) => {
      const { questionId, answer } = action.payload;
      state.answers[questionId] = answer;
      
      // Update progress
      const answeredCount = Object.values(state.answers).filter(answer => answer && answer.trim() !== '').length;
      state.progress.answered = answeredCount;
    },
    
    clearAnswer: (state, action: PayloadAction<string>) => {
      const questionId = action.payload;
      delete state.answers[questionId];
      
      // Update progress
      const answeredCount = Object.values(state.answers).filter(answer => answer && answer.trim() !== '').length;
      state.progress.answered = answeredCount;
    },
    
    clearAllAnswers: (state) => {
      state.answers = {};
      state.progress.answered = 0;
    },
    
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    clearSyncResult: (state) => {
      state.lastSyncResult = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Load data
      .addCase(loadQuickStartData.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loadQuickStartData.fulfilled, (state, action) => {
        state.isLoading = false;
        state.questions = action.payload.questions;
        state.progress.total = action.payload.questions.length;
        
        // Pre-populate with existing answers
        const initialAnswers: Record<string, string> = {};
        action.payload.existingAnswers.forEach(answer => {
          initialAnswers[answer.questionId] = answer.answer;
        });
        state.answers = initialAnswers;
        state.progress.answered = Object.values(initialAnswers).filter(answer => answer && answer.trim() !== '').length;
      })
      .addCase(loadQuickStartData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to load Quick Start data';
      })
      
      // Save answers
      .addCase(saveQuickStartAnswers.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(saveQuickStartAnswers.fulfilled, (state, action) => {
        state.isSaving = false;
        state.lastSyncResult = action.payload;
      })
      .addCase(saveQuickStartAnswers.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.error.message || 'Failed to save Quick Start answers';
      })
      
      // Delete answer
      .addCase(deleteQuickStartAnswer.fulfilled, (state, action) => {
        const { questionId } = action.payload;
        delete state.answers[questionId];
        state.progress.answered = Object.values(state.answers).filter(answer => answer && answer.trim() !== '').length;
      });
  }
});

// Actions
export const { 
  setAnswer, 
  clearAnswer, 
  clearAllAnswers, 
  setError, 
  clearError, 
  clearSyncResult 
} = quickStartSlice.actions;

// Selectors
export const selectQuickStartQuestions = (state: { quickStart: QuickStartState }) => state.quickStart.questions;
export const selectQuickStartAnswers = (state: { quickStart: QuickStartState }) => state.quickStart.answers;
export const selectQuickStartLoading = (state: { quickStart: QuickStartState }) => state.quickStart.isLoading;
export const selectQuickStartSaving = (state: { quickStart: QuickStartState }) => state.quickStart.isSaving;
export const selectQuickStartError = (state: { quickStart: QuickStartState }) => state.quickStart.error;
export const selectQuickStartProgress = (state: { quickStart: QuickStartState }) => state.quickStart.progress;
export const selectQuickStartSyncResult = (state: { quickStart: QuickStartState }) => state.quickStart.lastSyncResult;

export default quickStartSlice.reducer; 