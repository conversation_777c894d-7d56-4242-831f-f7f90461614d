{"version": 3, "sources": ["../../normalize-wheel/src/UserAgent_DEPRECATED.js", "../../normalize-wheel/src/ExecutionEnvironment.js", "../../normalize-wheel/src/isEventSupported.js", "../../normalize-wheel/src/normalizeWheel.js", "../../normalize-wheel/index.js", "../../src/helpers.ts", "../../src/Cropper.tsx"], "sourcesContent": ["/**\n * Copyright 2004-present Facebook. All Rights Reserved.\n *\n * @providesModule UserAgent_DEPRECATED\n */\n\n/**\n *  Provides entirely client-side User Agent and OS detection. You should prefer\n *  the non-deprecated UserAgent module when possible, which exposes our\n *  authoritative server-side PHP-based detection to the client.\n *\n *  Usage is straightforward:\n *\n *    if (UserAgent_DEPRECATED.ie()) {\n *      //  IE\n *    }\n *\n *  You can also do version checks:\n *\n *    if (UserAgent_DEPRECATED.ie() >= 7) {\n *      //  IE7 or better\n *    }\n *\n *  The browser functions will return NaN if the browser does not match, so\n *  you can also do version compares the other way:\n *\n *    if (UserAgent_DEPRECATED.ie() < 7) {\n *      //  IE6 or worse\n *    }\n *\n *  Note that the version is a float and may include a minor version number,\n *  so you should always use range operators to perform comparisons, not\n *  strict equality.\n *\n *  **Note:** You should **strongly** prefer capability detection to browser\n *  version detection where it's reasonable:\n *\n *    http://www.quirksmode.org/js/support.html\n *\n *  Further, we have a large number of mature wrapper functions and classes\n *  which abstract away many browser irregularities. Check the documentation,\n *  grep for things, or <NAME_EMAIL> before writing yet\n *  another copy of \"event || window.event\".\n *\n */\n\nvar _populated = false;\n\n// Browsers\nvar _ie, _firefox, _opera, _webkit, _chrome;\n\n// Actual IE browser for compatibility mode\nvar _ie_real_version;\n\n// Platforms\nvar _osx, _windows, _linux, _android;\n\n// Architectures\nvar _win64;\n\n// Devices\nvar _iphone, _ipad, _native;\n\nvar _mobile;\n\nfunction _populate() {\n  if (_populated) {\n    return;\n  }\n\n  _populated = true;\n\n  // To work around buggy JS libraries that can't handle multi-digit\n  // version numbers, Opera 10's user agent string claims it's Opera\n  // 9, then later includes a Version/X.Y field:\n  //\n  // Opera/9.80 (foo) Presto/2.2.15 Version/10.10\n  var uas = navigator.userAgent;\n  var agent = /(?:MSIE.(\\d+\\.\\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\\d+\\.\\d+))|(?:Opera(?:.+Version.|.)(\\d+\\.\\d+))|(?:AppleWebKit.(\\d+(?:\\.\\d+)?))|(?:Trident\\/\\d+\\.\\d+.*rv:(\\d+\\.\\d+))/.exec(uas);\n  var os    = /(Mac OS X)|(Windows)|(Linux)/.exec(uas);\n\n  _iphone = /\\b(iPhone|iP[ao]d)/.exec(uas);\n  _ipad = /\\b(iP[ao]d)/.exec(uas);\n  _android = /Android/i.exec(uas);\n  _native = /FBAN\\/\\w+;/i.exec(uas);\n  _mobile = /Mobile/i.exec(uas);\n\n  // Note that the IE team blog would have you believe you should be checking\n  // for 'Win64; x64'.  But MSDN then reveals that you can actually be coming\n  // from either x64 or ia64;  so ultimately, you should just check for Win64\n  // as in indicator of whether you're in 64-bit IE.  32-bit IE on 64-bit\n  // Windows will send 'WOW64' instead.\n  _win64 = !!(/Win64/.exec(uas));\n\n  if (agent) {\n    _ie = agent[1] ? parseFloat(agent[1]) : (\n          agent[5] ? parseFloat(agent[5]) : NaN);\n    // IE compatibility mode\n    if (_ie && document && document.documentMode) {\n      _ie = document.documentMode;\n    }\n    // grab the \"true\" ie version from the trident token if available\n    var trident = /(?:Trident\\/(\\d+.\\d+))/.exec(uas);\n    _ie_real_version = trident ? parseFloat(trident[1]) + 4 : _ie;\n\n    _firefox = agent[2] ? parseFloat(agent[2]) : NaN;\n    _opera   = agent[3] ? parseFloat(agent[3]) : NaN;\n    _webkit  = agent[4] ? parseFloat(agent[4]) : NaN;\n    if (_webkit) {\n      // We do not add the regexp to the above test, because it will always\n      // match 'safari' only since 'AppleWebKit' appears before 'Chrome' in\n      // the userAgent string.\n      agent = /(?:Chrome\\/(\\d+\\.\\d+))/.exec(uas);\n      _chrome = agent && agent[1] ? parseFloat(agent[1]) : NaN;\n    } else {\n      _chrome = NaN;\n    }\n  } else {\n    _ie = _firefox = _opera = _chrome = _webkit = NaN;\n  }\n\n  if (os) {\n    if (os[1]) {\n      // Detect OS X version.  If no version number matches, set _osx to true.\n      // Version examples:  10, 10_6_1, 10.7\n      // Parses version number as a float, taking only first two sets of\n      // digits.  If only one set of digits is found, returns just the major\n      // version number.\n      var ver = /(?:Mac OS X (\\d+(?:[._]\\d+)?))/.exec(uas);\n\n      _osx = ver ? parseFloat(ver[1].replace('_', '.')) : true;\n    } else {\n      _osx = false;\n    }\n    _windows = !!os[2];\n    _linux   = !!os[3];\n  } else {\n    _osx = _windows = _linux = false;\n  }\n}\n\nvar UserAgent_DEPRECATED = {\n\n  /**\n   *  Check if the UA is Internet Explorer.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  ie: function() {\n    return _populate() || _ie;\n  },\n\n  /**\n   * Check if we're in Internet Explorer compatibility mode.\n   *\n   * @return bool true if in compatibility mode, false if\n   * not compatibility mode or not ie\n   */\n  ieCompatibilityMode: function() {\n    return _populate() || (_ie_real_version > _ie);\n  },\n\n\n  /**\n   * Whether the browser is 64-bit IE.  Really, this is kind of weak sauce;  we\n   * only need this because Skype can't handle 64-bit IE yet.  We need to remove\n   * this when we don't need it -- tracked by #601957.\n   */\n  ie64: function() {\n    return UserAgent_DEPRECATED.ie() && _win64;\n  },\n\n  /**\n   *  Check if the UA is Firefox.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  firefox: function() {\n    return _populate() || _firefox;\n  },\n\n\n  /**\n   *  Check if the UA is Opera.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  opera: function() {\n    return _populate() || _opera;\n  },\n\n\n  /**\n   *  Check if the UA is WebKit.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  webkit: function() {\n    return _populate() || _webkit;\n  },\n\n  /**\n   *  For Push\n   *  WILL BE REMOVED VERY SOON. Use UserAgent_DEPRECATED.webkit\n   */\n  safari: function() {\n    return UserAgent_DEPRECATED.webkit();\n  },\n\n  /**\n   *  Check if the UA is a Chrome browser.\n   *\n   *\n   *  @return float|NaN Version number (if match) or NaN.\n   */\n  chrome : function() {\n    return _populate() || _chrome;\n  },\n\n\n  /**\n   *  Check if the user is running Windows.\n   *\n   *  @return bool `true' if the user's OS is Windows.\n   */\n  windows: function() {\n    return _populate() || _windows;\n  },\n\n\n  /**\n   *  Check if the user is running Mac OS X.\n   *\n   *  @return float|bool   Returns a float if a version number is detected,\n   *                       otherwise true/false.\n   */\n  osx: function() {\n    return _populate() || _osx;\n  },\n\n  /**\n   * Check if the user is running Linux.\n   *\n   * @return bool `true' if the user's OS is some flavor of Linux.\n   */\n  linux: function() {\n    return _populate() || _linux;\n  },\n\n  /**\n   * Check if the user is running on an iPhone or iPod platform.\n   *\n   * @return bool `true' if the user is running some flavor of the\n   *    iPhone OS.\n   */\n  iphone: function() {\n    return _populate() || _iphone;\n  },\n\n  mobile: function() {\n    return _populate() || (_iphone || _ipad || _android || _mobile);\n  },\n\n  nativeApp: function() {\n    // webviews inside of the native apps\n    return _populate() || _native;\n  },\n\n  android: function() {\n    return _populate() || _android;\n  },\n\n  ipad: function() {\n    return _populate() || _ipad;\n  }\n};\n\nmodule.exports = UserAgent_DEPRECATED;\n", "/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule ExecutionEnvironment\n */\n\n/*jslint evil: true */\n\n'use strict';\n\nvar canUseDOM = !!(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\n/**\n * Simple, lightweight module assisting with the detection and context of\n * Worker. Helps avoid circular dependencies and allows code to reason about\n * whether or not they are in a Worker, even if they never include the main\n * `ReactWorker` dependency.\n */\nvar ExecutionEnvironment = {\n\n  canUseDOM: canUseDOM,\n\n  canUseWorkers: typeof Worker !== 'undefined',\n\n  canUseEventListeners:\n    canUseDOM && !!(window.addEventListener || window.attachEvent),\n\n  canUseViewport: canUseDOM && !!window.screen,\n\n  isInWorker: !canUseDOM // For now, this is true - might change in the future.\n\n};\n\nmodule.exports = ExecutionEnvironment;\n", "/**\n * Copyright 2013-2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule isEventSupported\n */\n\n'use strict';\n\nvar ExecutionEnvironment = require('./ExecutionEnvironment');\n\nvar useHasFeature;\nif (ExecutionEnvironment.canUseDOM) {\n  useHasFeature =\n    document.implementation &&\n    document.implementation.hasFeature &&\n    // always returns true in newer browsers as per the standard.\n    // @see http://dom.spec.whatwg.org/#dom-domimplementation-hasfeature\n    document.implementation.hasFeature('', '') !== true;\n}\n\n/**\n * Checks if an event is supported in the current execution environment.\n *\n * NOTE: This will not work correctly for non-generic events such as `change`,\n * `reset`, `load`, `error`, and `select`.\n *\n * Borrows from Modernizr.\n *\n * @param {string} eventNameSuffix Event name, e.g. \"click\".\n * @param {?boolean} capture Check if the capture phase is supported.\n * @return {boolean} True if the event is supported.\n * @internal\n * @license Modernizr 3.0.0pre (Custom Build) | MIT\n */\nfunction isEventSupported(eventNameSuffix, capture) {\n  if (!ExecutionEnvironment.canUseDOM ||\n      capture && !('addEventListener' in document)) {\n    return false;\n  }\n\n  var eventName = 'on' + eventNameSuffix;\n  var isSupported = eventName in document;\n\n  if (!isSupported) {\n    var element = document.createElement('div');\n    element.setAttribute(eventName, 'return;');\n    isSupported = typeof element[eventName] === 'function';\n  }\n\n  if (!isSupported && useHasFeature && eventNameSuffix === 'wheel') {\n    // This is the only way to test support for the `wheel` event in IE9+.\n    isSupported = document.implementation.hasFeature('Events.wheel', '3.0');\n  }\n\n  return isSupported;\n}\n\nmodule.exports = isEventSupported;\n", "/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule normalizeWheel\n * @typechecks\n */\n\n'use strict';\n\nvar UserAgent_DEPRECATED = require('./UserAgent_DEPRECATED');\n\nvar isEventSupported = require('./isEventSupported');\n\n\n// Reasonable defaults\nvar PIXEL_STEP  = 10;\nvar LINE_HEIGHT = 40;\nvar PAGE_HEIGHT = 800;\n\n/**\n * Mouse wheel (and 2-finger trackpad) support on the web sucks.  It is\n * complicated, thus this doc is long and (hopefully) detailed enough to answer\n * your questions.\n *\n * If you need to react to the mouse wheel in a predictable way, this code is\n * like your bestest friend. * hugs *\n *\n * As of today, there are 4 DOM event types you can listen to:\n *\n *   'wheel'                -- Chrome(31+), FF(17+), IE(9+)\n *   'mousewheel'           -- Chrome, IE(6+), Opera, Safari\n *   'MozMousePixelScroll'  -- FF(3.5 only!) (2010-2013) -- don't bother!\n *   'DOMMouseScroll'       -- FF(0.9.7+) since 2003\n *\n * So what to do?  The is the best:\n *\n *   normalizeWheel.getEventType();\n *\n * In your event callback, use this code to get sane interpretation of the\n * deltas.  This code will return an object with properties:\n *\n *   spinX   -- normalized spin speed (use for zoom) - x plane\n *   spinY   -- \" - y plane\n *   pixelX  -- normalized distance (to pixels) - x plane\n *   pixelY  -- \" - y plane\n *\n * Wheel values are provided by the browser assuming you are using the wheel to\n * scroll a web page by a number of lines or pixels (or pages).  Values can vary\n * significantly on different platforms and browsers, forgetting that you can\n * scroll at different speeds.  Some devices (like trackpads) emit more events\n * at smaller increments with fine granularity, and some emit massive jumps with\n * linear speed or acceleration.\n *\n * This code does its best to normalize the deltas for you:\n *\n *   - spin is trying to normalize how far the wheel was spun (or trackpad\n *     dragged).  This is super useful for zoom support where you want to\n *     throw away the chunky scroll steps on the PC and make those equal to\n *     the slow and smooth tiny steps on the Mac. Key data: This code tries to\n *     resolve a single slow step on a wheel to 1.\n *\n *   - pixel is normalizing the desired scroll delta in pixel units.  You'll\n *     get the crazy differences between browsers, but at least it'll be in\n *     pixels!\n *\n *   - positive value indicates scrolling DOWN/RIGHT, negative UP/LEFT.  This\n *     should translate to positive value zooming IN, negative zooming OUT.\n *     This matches the newer 'wheel' event.\n *\n * Why are there spinX, spinY (or pixels)?\n *\n *   - spinX is a 2-finger side drag on the trackpad, and a shift + wheel turn\n *     with a mouse.  It results in side-scrolling in the browser by default.\n *\n *   - spinY is what you expect -- it's the classic axis of a mouse wheel.\n *\n *   - I dropped spinZ/pixelZ.  It is supported by the DOM 3 'wheel' event and\n *     probably is by browsers in conjunction with fancy 3D controllers .. but\n *     you know.\n *\n * Implementation info:\n *\n * Examples of 'wheel' event if you scroll slowly (down) by one step with an\n * average mouse:\n *\n *   OS X + Chrome  (mouse)     -    4   pixel delta  (wheelDelta -120)\n *   OS X + Safari  (mouse)     -  N/A   pixel delta  (wheelDelta  -12)\n *   OS X + Firefox (mouse)     -    0.1 line  delta  (wheelDelta  N/A)\n *   Win8 + Chrome  (mouse)     -  100   pixel delta  (wheelDelta -120)\n *   Win8 + Firefox (mouse)     -    3   line  delta  (wheelDelta -120)\n *\n * On the trackpad:\n *\n *   OS X + Chrome  (trackpad)  -    2   pixel delta  (wheelDelta   -6)\n *   OS X + Firefox (trackpad)  -    1   pixel delta  (wheelDelta  N/A)\n *\n * On other/older browsers.. it's more complicated as there can be multiple and\n * also missing delta values.\n *\n * The 'wheel' event is more standard:\n *\n * http://www.w3.org/TR/DOM-Level-3-Events/#events-wheelevents\n *\n * The basics is that it includes a unit, deltaMode (pixels, lines, pages), and\n * deltaX, deltaY and deltaZ.  Some browsers provide other values to maintain\n * backward compatibility with older events.  Those other values help us\n * better normalize spin speed.  Example of what the browsers provide:\n *\n *                          | event.wheelDelta | event.detail\n *        ------------------+------------------+--------------\n *          Safari v5/OS X  |       -120       |       0\n *          Safari v5/Win7  |       -120       |       0\n *         Chrome v17/OS X  |       -120       |       0\n *         Chrome v17/Win7  |       -120       |       0\n *                IE9/Win7  |       -120       |   undefined\n *         Firefox v4/OS X  |     undefined    |       1\n *         Firefox v4/Win7  |     undefined    |       3\n *\n */\nfunction normalizeWheel(/*object*/ event) /*object*/ {\n  var sX = 0, sY = 0,       // spinX, spinY\n      pX = 0, pY = 0;       // pixelX, pixelY\n\n  // Legacy\n  if ('detail'      in event) { sY = event.detail; }\n  if ('wheelDelta'  in event) { sY = -event.wheelDelta / 120; }\n  if ('wheelDeltaY' in event) { sY = -event.wheelDeltaY / 120; }\n  if ('wheelDeltaX' in event) { sX = -event.wheelDeltaX / 120; }\n\n  // side scrolling on FF with DOMMouseScroll\n  if ( 'axis' in event && event.axis === event.HORIZONTAL_AXIS ) {\n    sX = sY;\n    sY = 0;\n  }\n\n  pX = sX * PIXEL_STEP;\n  pY = sY * PIXEL_STEP;\n\n  if ('deltaY' in event) { pY = event.deltaY; }\n  if ('deltaX' in event) { pX = event.deltaX; }\n\n  if ((pX || pY) && event.deltaMode) {\n    if (event.deltaMode == 1) {          // delta in LINE units\n      pX *= LINE_HEIGHT;\n      pY *= LINE_HEIGHT;\n    } else {                             // delta in PAGE units\n      pX *= PAGE_HEIGHT;\n      pY *= PAGE_HEIGHT;\n    }\n  }\n\n  // Fall-back if spin cannot be determined\n  if (pX && !sX) { sX = (pX < 1) ? -1 : 1; }\n  if (pY && !sY) { sY = (pY < 1) ? -1 : 1; }\n\n  return { spinX  : sX,\n           spinY  : sY,\n           pixelX : pX,\n           pixelY : pY };\n}\n\n\n/**\n * The best combination if you prefer spinX + spinY normalization.  It favors\n * the older DOMMouseScroll for Firefox, as FF does not include wheelDelta with\n * 'wheel' event, making spin speed determination impossible.\n */\nnormalizeWheel.getEventType = function() /*string*/ {\n  return (UserAgent_DEPRECATED.firefox())\n           ? 'DOMMouseScroll'\n           : (isEventSupported('wheel'))\n               ? 'wheel'\n               : 'mousewheel';\n};\n\nmodule.exports = normalizeWheel;\n", "module.exports = require('./src/normalizeWheel.js');\n", "import { Area, MediaSize, Point, Size } from './types'\n\n/**\n * Compute the dimension of the crop area based on media size,\n * aspect ratio and optionally rotation\n */\nexport function getCropSize(\n  mediaWidth: number,\n  mediaHeight: number,\n  containerWidth: number,\n  containerHeight: number,\n  aspect: number,\n  rotation = 0\n): Size {\n  const { width, height } = rotateSize(mediaWidth, mediaHeight, rotation)\n  const fittingWidth = Math.min(width, containerWidth)\n  const fittingHeight = Math.min(height, containerHeight)\n\n  if (fittingWidth > fittingHeight * aspect) {\n    return {\n      width: fittingHeight * aspect,\n      height: fittingHeight,\n    }\n  }\n\n  return {\n    width: fittingWidth,\n    height: fittingWidth / aspect,\n  }\n}\n\n/**\n * Compute media zoom.\n * We fit the media into the container with \"max-width: 100%; max-height: 100%;\"\n */\nexport function getMediaZoom(mediaSize: MediaSize) {\n  // Take the axis with more pixels to improve accuracy\n  return mediaSize.width > mediaSize.height\n    ? mediaSize.width / mediaSize.naturalWidth\n    : mediaSize.height / mediaSize.naturalHeight\n}\n\n/**\n * Ensure a new media position stays in the crop area.\n */\nexport function restrictPosition(\n  position: Point,\n  mediaSize: Size,\n  cropSize: Size,\n  zoom: number,\n  rotation = 0\n): Point {\n  const { width, height } = rotateSize(mediaSize.width, mediaSize.height, rotation)\n\n  return {\n    x: restrictPositionCoord(position.x, width, cropSize.width, zoom),\n    y: restrictPositionCoord(position.y, height, cropSize.height, zoom),\n  }\n}\n\nfunction restrictPositionCoord(\n  position: number,\n  mediaSize: number,\n  cropSize: number,\n  zoom: number\n): number {\n  const maxPosition = (mediaSize * zoom) / 2 - cropSize / 2\n\n  return clamp(position, -maxPosition, maxPosition)\n}\n\nexport function getDistanceBetweenPoints(pointA: Point, pointB: Point) {\n  return Math.sqrt(Math.pow(pointA.y - pointB.y, 2) + Math.pow(pointA.x - pointB.x, 2))\n}\n\nexport function getRotationBetweenPoints(pointA: Point, pointB: Point) {\n  return (Math.atan2(pointB.y - pointA.y, pointB.x - pointA.x) * 180) / Math.PI\n}\n\n/**\n * Compute the output cropped area of the media in percentages and pixels.\n * x/y are the top-left coordinates on the src media\n */\nexport function computeCroppedArea(\n  crop: Point,\n  mediaSize: MediaSize,\n  cropSize: Size,\n  aspect: number,\n  zoom: number,\n  rotation = 0,\n  restrictPosition = true\n): { croppedAreaPercentages: Area; croppedAreaPixels: Area } {\n  // if the media is rotated by the user, we cannot limit the position anymore\n  // as it might need to be negative.\n  const limitAreaFn = restrictPosition ? limitArea : noOp\n\n  const mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation)\n  const mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation)\n\n  // calculate the crop area in percentages\n  // in the rotated space\n  const croppedAreaPercentages = {\n    x: limitAreaFn(\n      100,\n      (((mediaBBoxSize.width - cropSize.width / zoom) / 2 - crop.x / zoom) / mediaBBoxSize.width) *\n        100\n    ),\n    y: limitAreaFn(\n      100,\n      (((mediaBBoxSize.height - cropSize.height / zoom) / 2 - crop.y / zoom) /\n        mediaBBoxSize.height) *\n        100\n    ),\n    width: limitAreaFn(100, ((cropSize.width / mediaBBoxSize.width) * 100) / zoom),\n    height: limitAreaFn(100, ((cropSize.height / mediaBBoxSize.height) * 100) / zoom),\n  }\n\n  // we compute the pixels size naively\n  const widthInPixels = Math.round(\n    limitAreaFn(\n      mediaNaturalBBoxSize.width,\n      (croppedAreaPercentages.width * mediaNaturalBBoxSize.width) / 100\n    )\n  )\n  const heightInPixels = Math.round(\n    limitAreaFn(\n      mediaNaturalBBoxSize.height,\n      (croppedAreaPercentages.height * mediaNaturalBBoxSize.height) / 100\n    )\n  )\n  const isImgWiderThanHigh = mediaNaturalBBoxSize.width >= mediaNaturalBBoxSize.height * aspect\n\n  // then we ensure the width and height exactly match the aspect (to avoid rounding approximations)\n  // if the media is wider than high, when zoom is 0, the crop height will be equals to image height\n  // thus we want to compute the width from the height and aspect for accuracy.\n  // Otherwise, we compute the height from width and aspect.\n  const sizePixels = isImgWiderThanHigh\n    ? {\n        width: Math.round(heightInPixels * aspect),\n        height: heightInPixels,\n      }\n    : {\n        width: widthInPixels,\n        height: Math.round(widthInPixels / aspect),\n      }\n\n  const croppedAreaPixels = {\n    ...sizePixels,\n    x: Math.round(\n      limitAreaFn(\n        mediaNaturalBBoxSize.width - sizePixels.width,\n        (croppedAreaPercentages.x * mediaNaturalBBoxSize.width) / 100\n      )\n    ),\n    y: Math.round(\n      limitAreaFn(\n        mediaNaturalBBoxSize.height - sizePixels.height,\n        (croppedAreaPercentages.y * mediaNaturalBBoxSize.height) / 100\n      )\n    ),\n  }\n\n  return { croppedAreaPercentages, croppedAreaPixels }\n}\n\n/**\n * Ensure the returned value is between 0 and max\n */\nfunction limitArea(max: number, value: number): number {\n  return Math.min(max, Math.max(0, value))\n}\n\nfunction noOp(_max: number, value: number) {\n  return value\n}\n\n/**\n * Compute crop and zoom from the croppedAreaPercentages.\n */\nexport function getInitialCropFromCroppedAreaPercentages(\n  croppedAreaPercentages: Area,\n  mediaSize: MediaSize,\n  rotation: number,\n  cropSize: Size,\n  minZoom: number,\n  maxZoom: number\n) {\n  const mediaBBoxSize = rotateSize(mediaSize.width, mediaSize.height, rotation)\n\n  // This is the inverse process of computeCroppedArea\n  const zoom = clamp(\n    (cropSize.width / mediaBBoxSize.width) * (100 / croppedAreaPercentages.width),\n    minZoom,\n    maxZoom\n  )\n\n  const crop = {\n    x:\n      (zoom * mediaBBoxSize.width) / 2 -\n      cropSize.width / 2 -\n      mediaBBoxSize.width * zoom * (croppedAreaPercentages.x / 100),\n    y:\n      (zoom * mediaBBoxSize.height) / 2 -\n      cropSize.height / 2 -\n      mediaBBoxSize.height * zoom * (croppedAreaPercentages.y / 100),\n  }\n\n  return { crop, zoom }\n}\n\n/**\n * Compute zoom from the croppedAreaPixels\n */\nfunction getZoomFromCroppedAreaPixels(\n  croppedAreaPixels: Area,\n  mediaSize: MediaSize,\n  cropSize: Size\n): number {\n  const mediaZoom = getMediaZoom(mediaSize)\n\n  return cropSize.height > cropSize.width\n    ? cropSize.height / (croppedAreaPixels.height * mediaZoom)\n    : cropSize.width / (croppedAreaPixels.width * mediaZoom)\n}\n\n/**\n * Compute crop and zoom from the croppedAreaPixels\n */\nexport function getInitialCropFromCroppedAreaPixels(\n  croppedAreaPixels: Area,\n  mediaSize: MediaSize,\n  rotation = 0,\n  cropSize: Size,\n  minZoom: number,\n  maxZoom: number\n): { crop: Point; zoom: number } {\n  const mediaNaturalBBoxSize = rotateSize(mediaSize.naturalWidth, mediaSize.naturalHeight, rotation)\n\n  const zoom = clamp(\n    getZoomFromCroppedAreaPixels(croppedAreaPixels, mediaSize, cropSize),\n    minZoom,\n    maxZoom\n  )\n\n  const cropZoom =\n    cropSize.height > cropSize.width\n      ? cropSize.height / croppedAreaPixels.height\n      : cropSize.width / croppedAreaPixels.width\n\n  const crop = {\n    x:\n      ((mediaNaturalBBoxSize.width - croppedAreaPixels.width) / 2 - croppedAreaPixels.x) * cropZoom,\n    y:\n      ((mediaNaturalBBoxSize.height - croppedAreaPixels.height) / 2 - croppedAreaPixels.y) *\n      cropZoom,\n  }\n  return { crop, zoom }\n}\n\n/**\n * Return the point that is the center of point a and b\n */\nexport function getCenter(a: Point, b: Point): Point {\n  return {\n    x: (b.x + a.x) / 2,\n    y: (b.y + a.y) / 2,\n  }\n}\n\nexport function getRadianAngle(degreeValue: number) {\n  return (degreeValue * Math.PI) / 180\n}\n\n/**\n * Returns the new bounding area of a rotated rectangle.\n */\nexport function rotateSize(width: number, height: number, rotation: number): Size {\n  const rotRad = getRadianAngle(rotation)\n\n  return {\n    width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),\n    height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height),\n  }\n}\n\n/**\n * Clamp value between min and max\n */\nexport function clamp(value: number, min: number, max: number) {\n  return Math.min(Math.max(value, min), max)\n}\n\n/**\n * Combine multiple class names into a single string.\n */\nexport function classNames(...args: (boolean | string | number | undefined | void | null)[]) {\n  return args\n    .filter((value) => {\n      if (typeof value === 'string' && value.length > 0) {\n        return true\n      }\n\n      return false\n    })\n    .join(' ')\n    .trim()\n}\n", "import * as React from 'react'\nimport normalizeWheel from 'normalize-wheel'\nimport { Area, MediaSize, Point, Size, VideoSrc } from './types'\nimport {\n  getCropSize,\n  restrictPosition,\n  getDistanceBetweenPoints,\n  getRotationBetweenPoints,\n  computeCroppedArea,\n  getCenter,\n  getInitialCropFromCroppedAreaPixels,\n  getInitialCropFromCroppedAreaPercentages,\n  classNames,\n  clamp,\n} from './helpers'\nimport cssStyles from './styles.css'\n\nexport type CropperProps = {\n  image?: string\n  video?: string | VideoSrc[]\n  transform?: string\n  crop: Point\n  zoom: number\n  rotation: number\n  aspect: number\n  minZoom: number\n  maxZoom: number\n  cropShape: 'rect' | 'round'\n  cropSize?: Size\n  objectFit?: 'contain' | 'cover' | 'horizontal-cover' | 'vertical-cover'\n  showGrid?: boolean\n  zoomSpeed: number\n  zoomWithScroll?: boolean\n  roundCropAreaPixels?: boolean\n  onCropChange: (location: Point) => void\n  onZoomChange?: (zoom: number) => void\n  onRotationChange?: (rotation: number) => void\n  onCropComplete?: (croppedArea: Area, croppedAreaPixels: Area) => void\n  onCropAreaChange?: (croppedArea: Area, croppedAreaPixels: Area) => void\n  onCropSizeChange?: (cropSize: Size) => void\n  onInteractionStart?: () => void\n  onInteractionEnd?: () => void\n  onMediaLoaded?: (mediaSize: MediaSize) => void\n  style: {\n    containerStyle?: React.CSSProperties\n    mediaStyle?: React.CSSProperties\n    cropAreaStyle?: React.CSSProperties\n  }\n  classes: {\n    containerClassName?: string\n    mediaClassName?: string\n    cropAreaClassName?: string\n  }\n  restrictPosition: boolean\n  mediaProps: React.ImgHTMLAttributes<HTMLElement> | React.VideoHTMLAttributes<HTMLElement>\n  cropperProps: React.HTMLAttributes<HTMLDivElement>\n  disableAutomaticStylesInjection?: boolean\n  initialCroppedAreaPixels?: Area\n  initialCroppedAreaPercentages?: Area\n  onTouchRequest?: (e: React.TouchEvent<HTMLDivElement>) => boolean\n  onWheelRequest?: (e: WheelEvent) => boolean\n  setCropperRef?: (ref: React.RefObject<HTMLDivElement>) => void\n  setImageRef?: (ref: React.RefObject<HTMLImageElement>) => void\n  setVideoRef?: (ref: React.RefObject<HTMLVideoElement>) => void\n  setMediaSize?: (size: MediaSize) => void\n  setCropSize?: (size: Size) => void\n  nonce?: string\n  keyboardStep: number\n}\n\ntype State = {\n  cropSize: Size | null\n  hasWheelJustStarted: boolean\n  mediaObjectFit: String | undefined\n}\n\nconst MIN_ZOOM = 1\nconst MAX_ZOOM = 3\nconst KEYBOARD_STEP = 1\n\ntype GestureEvent = UIEvent & {\n  rotation: number\n  scale: number\n  clientX: number\n  clientY: number\n}\n\nclass Cropper extends React.Component<CropperProps, State> {\n  static defaultProps = {\n    zoom: 1,\n    rotation: 0,\n    aspect: 4 / 3,\n    maxZoom: MAX_ZOOM,\n    minZoom: MIN_ZOOM,\n    cropShape: 'rect' as const,\n    objectFit: 'contain' as const,\n    showGrid: true,\n    style: {},\n    classes: {},\n    mediaProps: {},\n    cropperProps: {},\n    zoomSpeed: 1,\n    restrictPosition: true,\n    zoomWithScroll: true,\n    keyboardStep: KEYBOARD_STEP,\n  }\n\n  cropperRef: React.RefObject<HTMLDivElement> = React.createRef()\n  imageRef: React.RefObject<HTMLImageElement> = React.createRef()\n  videoRef: React.RefObject<HTMLVideoElement> = React.createRef()\n  containerPosition: Point = { x: 0, y: 0 }\n  containerRef: HTMLDivElement | null = null\n  styleRef: HTMLStyleElement | null = null\n  containerRect: DOMRect | null = null\n  mediaSize: MediaSize = { width: 0, height: 0, naturalWidth: 0, naturalHeight: 0 }\n  dragStartPosition: Point = { x: 0, y: 0 }\n  dragStartCrop: Point = { x: 0, y: 0 }\n  gestureZoomStart = 0\n  gestureRotationStart = 0\n  isTouching = false\n  lastPinchDistance = 0\n  lastPinchRotation = 0\n  rafDragTimeout: number | null = null\n  rafPinchTimeout: number | null = null\n  wheelTimer: number | null = null\n  currentDoc: Document | null = typeof document !== 'undefined' ? document : null\n  currentWindow: Window | null = typeof window !== 'undefined' ? window : null\n  resizeObserver: ResizeObserver | null = null\n\n  state: State = {\n    cropSize: null,\n    hasWheelJustStarted: false,\n    mediaObjectFit: undefined,\n  }\n\n  componentDidMount() {\n    if (!this.currentDoc || !this.currentWindow) return\n    if (this.containerRef) {\n      if (this.containerRef.ownerDocument) {\n        this.currentDoc = this.containerRef.ownerDocument\n      }\n      if (this.currentDoc.defaultView) {\n        this.currentWindow = this.currentDoc.defaultView\n      }\n\n      this.initResizeObserver()\n      // only add window resize listener if ResizeObserver is not supported. Otherwise, it would be redundant\n      if (typeof window.ResizeObserver === 'undefined') {\n        this.currentWindow.addEventListener('resize', this.computeSizes)\n      }\n      this.props.zoomWithScroll &&\n        this.containerRef.addEventListener('wheel', this.onWheel, { passive: false })\n      this.containerRef.addEventListener('gesturestart', this.onGestureStart as EventListener)\n    }\n\n    this.currentDoc.addEventListener('scroll', this.onScroll)\n\n    if (!this.props.disableAutomaticStylesInjection) {\n      this.styleRef = this.currentDoc.createElement('style')\n      this.styleRef.setAttribute('type', 'text/css')\n      if (this.props.nonce) {\n        this.styleRef.setAttribute('nonce', this.props.nonce)\n      }\n      this.styleRef.innerHTML = cssStyles\n      this.currentDoc.head.appendChild(this.styleRef)\n    }\n\n    // when rendered via SSR, the image can already be loaded and its onLoad callback will never be called\n    if (this.imageRef.current && this.imageRef.current.complete) {\n      this.onMediaLoad()\n    }\n\n    // set image and video refs in the parent if the callbacks exist\n    if (this.props.setImageRef) {\n      this.props.setImageRef(this.imageRef)\n    }\n\n    if (this.props.setVideoRef) {\n      this.props.setVideoRef(this.videoRef)\n    }\n\n    if (this.props.setCropperRef) {\n      this.props.setCropperRef(this.cropperRef)\n    }\n  }\n\n  componentWillUnmount() {\n    if (!this.currentDoc || !this.currentWindow) return\n    if (typeof window.ResizeObserver === 'undefined') {\n      this.currentWindow.removeEventListener('resize', this.computeSizes)\n    }\n    this.resizeObserver?.disconnect()\n    if (this.containerRef) {\n      this.containerRef.removeEventListener('gesturestart', this.preventZoomSafari)\n    }\n\n    if (this.styleRef) {\n      this.styleRef.parentNode?.removeChild(this.styleRef)\n    }\n\n    this.cleanEvents()\n    this.props.zoomWithScroll && this.clearScrollEvent()\n  }\n\n  componentDidUpdate(prevProps: CropperProps) {\n    if (prevProps.rotation !== this.props.rotation) {\n      this.computeSizes()\n      this.recomputeCropPosition()\n    } else if (prevProps.aspect !== this.props.aspect) {\n      this.computeSizes()\n    } else if (prevProps.objectFit !== this.props.objectFit) {\n      this.computeSizes()\n    } else if (prevProps.zoom !== this.props.zoom) {\n      this.recomputeCropPosition()\n    } else if (\n      prevProps.cropSize?.height !== this.props.cropSize?.height ||\n      prevProps.cropSize?.width !== this.props.cropSize?.width\n    ) {\n      this.computeSizes()\n    } else if (\n      prevProps.crop?.x !== this.props.crop?.x ||\n      prevProps.crop?.y !== this.props.crop?.y\n    ) {\n      this.emitCropAreaChange()\n    }\n    if (prevProps.zoomWithScroll !== this.props.zoomWithScroll && this.containerRef) {\n      this.props.zoomWithScroll\n        ? this.containerRef.addEventListener('wheel', this.onWheel, { passive: false })\n        : this.clearScrollEvent()\n    }\n    if (prevProps.video !== this.props.video) {\n      this.videoRef.current?.load()\n    }\n\n    const objectFit = this.getObjectFit()\n    if (objectFit !== this.state.mediaObjectFit) {\n      this.setState({ mediaObjectFit: objectFit }, this.computeSizes)\n    }\n  }\n\n  initResizeObserver = () => {\n    if (typeof window.ResizeObserver === 'undefined' || !this.containerRef) {\n      return\n    }\n    let isFirstResize = true\n    this.resizeObserver = new window.ResizeObserver((entries) => {\n      if (isFirstResize) {\n        isFirstResize = false // observe() is called on mount, we don't want to trigger a recompute on mount\n        return\n      }\n      this.computeSizes()\n    })\n    this.resizeObserver.observe(this.containerRef)\n  }\n\n  // this is to prevent Safari on iOS >= 10 to zoom the page\n  preventZoomSafari = (e: Event) => e.preventDefault()\n\n  cleanEvents = () => {\n    if (!this.currentDoc) return\n    this.currentDoc.removeEventListener('mousemove', this.onMouseMove)\n    this.currentDoc.removeEventListener('mouseup', this.onDragStopped)\n    this.currentDoc.removeEventListener('touchmove', this.onTouchMove)\n    this.currentDoc.removeEventListener('touchend', this.onDragStopped)\n    this.currentDoc.removeEventListener('gesturechange', this.onGestureChange as EventListener)\n    this.currentDoc.removeEventListener('gestureend', this.onGestureEnd as EventListener)\n    this.currentDoc.removeEventListener('scroll', this.onScroll)\n  }\n\n  clearScrollEvent = () => {\n    if (this.containerRef) this.containerRef.removeEventListener('wheel', this.onWheel)\n    if (this.wheelTimer) {\n      clearTimeout(this.wheelTimer)\n    }\n  }\n\n  onMediaLoad = () => {\n    const cropSize = this.computeSizes()\n\n    if (cropSize) {\n      this.emitCropData()\n      this.setInitialCrop(cropSize)\n    }\n\n    if (this.props.onMediaLoaded) {\n      this.props.onMediaLoaded(this.mediaSize)\n    }\n  }\n\n  setInitialCrop = (cropSize: Size) => {\n    if (this.props.initialCroppedAreaPercentages) {\n      const { crop, zoom } = getInitialCropFromCroppedAreaPercentages(\n        this.props.initialCroppedAreaPercentages,\n        this.mediaSize,\n        this.props.rotation,\n        cropSize,\n        this.props.minZoom,\n        this.props.maxZoom\n      )\n\n      this.props.onCropChange(crop)\n      this.props.onZoomChange && this.props.onZoomChange(zoom)\n    } else if (this.props.initialCroppedAreaPixels) {\n      const { crop, zoom } = getInitialCropFromCroppedAreaPixels(\n        this.props.initialCroppedAreaPixels,\n        this.mediaSize,\n        this.props.rotation,\n        cropSize,\n        this.props.minZoom,\n        this.props.maxZoom\n      )\n\n      this.props.onCropChange(crop)\n      this.props.onZoomChange && this.props.onZoomChange(zoom)\n    }\n  }\n\n  getAspect() {\n    const { cropSize, aspect } = this.props\n    if (cropSize) {\n      return cropSize.width / cropSize.height\n    }\n    return aspect\n  }\n\n  getObjectFit() {\n    if (this.props.objectFit === 'cover') {\n      const mediaRef = this.imageRef.current || this.videoRef.current\n\n      if (mediaRef && this.containerRef) {\n        this.containerRect = this.containerRef.getBoundingClientRect()\n        const containerAspect = this.containerRect.width / this.containerRect.height\n        const naturalWidth =\n          this.imageRef.current?.naturalWidth || this.videoRef.current?.videoWidth || 0\n        const naturalHeight =\n          this.imageRef.current?.naturalHeight || this.videoRef.current?.videoHeight || 0\n        const mediaAspect = naturalWidth / naturalHeight\n\n        return mediaAspect < containerAspect ? 'horizontal-cover' : 'vertical-cover'\n      }\n      return 'horizontal-cover'\n    }\n\n    return this.props.objectFit\n  }\n\n  computeSizes = () => {\n    const mediaRef = this.imageRef.current || this.videoRef.current\n\n    if (mediaRef && this.containerRef) {\n      this.containerRect = this.containerRef.getBoundingClientRect()\n      this.saveContainerPosition()\n      const containerAspect = this.containerRect.width / this.containerRect.height\n      const naturalWidth =\n        this.imageRef.current?.naturalWidth || this.videoRef.current?.videoWidth || 0\n      const naturalHeight =\n        this.imageRef.current?.naturalHeight || this.videoRef.current?.videoHeight || 0\n      const isMediaScaledDown =\n        mediaRef.offsetWidth < naturalWidth || mediaRef.offsetHeight < naturalHeight\n      const mediaAspect = naturalWidth / naturalHeight\n\n      // We do not rely on the offsetWidth/offsetHeight if the media is scaled down\n      // as the values they report are rounded. That will result in precision losses\n      // when calculating zoom. We use the fact that the media is positionned relative\n      // to the container. That allows us to use the container's dimensions\n      // and natural aspect ratio of the media to calculate accurate media size.\n      // However, for this to work, the container should not be rotated\n      let renderedMediaSize: Size\n\n      if (isMediaScaledDown) {\n        switch (this.state.mediaObjectFit) {\n          default:\n          case 'contain':\n            renderedMediaSize =\n              containerAspect > mediaAspect\n                ? {\n                    width: this.containerRect.height * mediaAspect,\n                    height: this.containerRect.height,\n                  }\n                : {\n                    width: this.containerRect.width,\n                    height: this.containerRect.width / mediaAspect,\n                  }\n            break\n          case 'horizontal-cover':\n            renderedMediaSize = {\n              width: this.containerRect.width,\n              height: this.containerRect.width / mediaAspect,\n            }\n            break\n          case 'vertical-cover':\n            renderedMediaSize = {\n              width: this.containerRect.height * mediaAspect,\n              height: this.containerRect.height,\n            }\n            break\n        }\n      } else {\n        renderedMediaSize = {\n          width: mediaRef.offsetWidth,\n          height: mediaRef.offsetHeight,\n        }\n      }\n\n      this.mediaSize = {\n        ...renderedMediaSize,\n        naturalWidth,\n        naturalHeight,\n      }\n\n      // set media size in the parent\n      if (this.props.setMediaSize) {\n        this.props.setMediaSize(this.mediaSize)\n      }\n\n      const cropSize = this.props.cropSize\n        ? this.props.cropSize\n        : getCropSize(\n            this.mediaSize.width,\n            this.mediaSize.height,\n            this.containerRect.width,\n            this.containerRect.height,\n            this.props.aspect,\n            this.props.rotation\n          )\n\n      if (\n        this.state.cropSize?.height !== cropSize.height ||\n        this.state.cropSize?.width !== cropSize.width\n      ) {\n        this.props.onCropSizeChange && this.props.onCropSizeChange(cropSize)\n      }\n      this.setState({ cropSize }, this.recomputeCropPosition)\n      // pass crop size to parent\n      if (this.props.setCropSize) {\n        this.props.setCropSize(cropSize)\n      }\n\n      return cropSize\n    }\n  }\n\n  saveContainerPosition = () => {\n    if (this.containerRef) {\n      const bounds = this.containerRef.getBoundingClientRect()\n      this.containerPosition = { x: bounds.left, y: bounds.top }\n    }\n  }\n\n  static getMousePoint = (e: MouseEvent | React.MouseEvent | GestureEvent) => ({\n    x: Number(e.clientX),\n    y: Number(e.clientY),\n  })\n\n  static getTouchPoint = (touch: Touch | React.Touch) => ({\n    x: Number(touch.clientX),\n    y: Number(touch.clientY),\n  })\n\n  onMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.currentDoc.addEventListener('mousemove', this.onMouseMove)\n    this.currentDoc.addEventListener('mouseup', this.onDragStopped)\n    this.saveContainerPosition()\n    this.onDragStart(Cropper.getMousePoint(e))\n  }\n\n  onMouseMove = (e: MouseEvent) => this.onDrag(Cropper.getMousePoint(e))\n\n  onScroll = (e: Event) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.saveContainerPosition()\n  }\n\n  onTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {\n    if (!this.currentDoc) return\n    this.isTouching = true\n    if (this.props.onTouchRequest && !this.props.onTouchRequest(e)) {\n      return\n    }\n\n    this.currentDoc.addEventListener('touchmove', this.onTouchMove, { passive: false }) // iOS 11 now defaults to passive: true\n    this.currentDoc.addEventListener('touchend', this.onDragStopped)\n\n    this.saveContainerPosition()\n\n    if (e.touches.length === 2) {\n      this.onPinchStart(e)\n    } else if (e.touches.length === 1) {\n      this.onDragStart(Cropper.getTouchPoint(e.touches[0]))\n    }\n  }\n\n  onTouchMove = (e: TouchEvent) => {\n    // Prevent whole page from scrolling on iOS.\n    e.preventDefault()\n    if (e.touches.length === 2) {\n      this.onPinchMove(e)\n    } else if (e.touches.length === 1) {\n      this.onDrag(Cropper.getTouchPoint(e.touches[0]))\n    }\n  }\n\n  onGestureStart = (e: GestureEvent) => {\n    if (!this.currentDoc) return\n    e.preventDefault()\n    this.currentDoc.addEventListener('gesturechange', this.onGestureChange as EventListener)\n    this.currentDoc.addEventListener('gestureend', this.onGestureEnd as EventListener)\n    this.gestureZoomStart = this.props.zoom\n    this.gestureRotationStart = this.props.rotation\n  }\n\n  onGestureChange = (e: GestureEvent) => {\n    e.preventDefault()\n    if (this.isTouching) {\n      // this is to avoid conflict between gesture and touch events\n      return\n    }\n\n    const point = Cropper.getMousePoint(e)\n    const newZoom = this.gestureZoomStart - 1 + e.scale\n    this.setNewZoom(newZoom, point, { shouldUpdatePosition: true })\n    if (this.props.onRotationChange) {\n      const newRotation = this.gestureRotationStart + e.rotation\n      this.props.onRotationChange(newRotation)\n    }\n  }\n\n  onGestureEnd = (e: GestureEvent) => {\n    this.cleanEvents()\n  }\n\n  onDragStart = ({ x, y }: Point) => {\n    this.dragStartPosition = { x, y }\n    this.dragStartCrop = { ...this.props.crop }\n    this.props.onInteractionStart?.()\n  }\n\n  onDrag = ({ x, y }: Point) => {\n    if (!this.currentWindow) return\n    if (this.rafDragTimeout) this.currentWindow.cancelAnimationFrame(this.rafDragTimeout)\n\n    this.rafDragTimeout = this.currentWindow.requestAnimationFrame(() => {\n      if (!this.state.cropSize) return\n      if (x === undefined || y === undefined) return\n      const offsetX = x - this.dragStartPosition.x\n      const offsetY = y - this.dragStartPosition.y\n      const requestedPosition = {\n        x: this.dragStartCrop.x + offsetX,\n        y: this.dragStartCrop.y + offsetY,\n      }\n\n      const newPosition = this.props.restrictPosition\n        ? restrictPosition(\n            requestedPosition,\n            this.mediaSize,\n            this.state.cropSize,\n            this.props.zoom,\n            this.props.rotation\n          )\n        : requestedPosition\n      this.props.onCropChange(newPosition)\n    })\n  }\n\n  onDragStopped = () => {\n    this.isTouching = false\n    this.cleanEvents()\n    this.emitCropData()\n    this.props.onInteractionEnd?.()\n  }\n\n  onPinchStart(e: React.TouchEvent<HTMLDivElement>) {\n    const pointA = Cropper.getTouchPoint(e.touches[0])\n    const pointB = Cropper.getTouchPoint(e.touches[1])\n    this.lastPinchDistance = getDistanceBetweenPoints(pointA, pointB)\n    this.lastPinchRotation = getRotationBetweenPoints(pointA, pointB)\n    this.onDragStart(getCenter(pointA, pointB))\n  }\n\n  onPinchMove(e: TouchEvent) {\n    if (!this.currentDoc || !this.currentWindow) return\n    const pointA = Cropper.getTouchPoint(e.touches[0])\n    const pointB = Cropper.getTouchPoint(e.touches[1])\n    const center = getCenter(pointA, pointB)\n    this.onDrag(center)\n\n    if (this.rafPinchTimeout) this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout)\n    this.rafPinchTimeout = this.currentWindow.requestAnimationFrame(() => {\n      const distance = getDistanceBetweenPoints(pointA, pointB)\n      const newZoom = this.props.zoom * (distance / this.lastPinchDistance)\n      this.setNewZoom(newZoom, center, { shouldUpdatePosition: false })\n      this.lastPinchDistance = distance\n\n      const rotation = getRotationBetweenPoints(pointA, pointB)\n      const newRotation = this.props.rotation + (rotation - this.lastPinchRotation)\n      this.props.onRotationChange && this.props.onRotationChange(newRotation)\n      this.lastPinchRotation = rotation\n    })\n  }\n\n  onWheel = (e: WheelEvent) => {\n    if (!this.currentWindow) return\n    if (this.props.onWheelRequest && !this.props.onWheelRequest(e)) {\n      return\n    }\n\n    e.preventDefault()\n    const point = Cropper.getMousePoint(e)\n    const { pixelY } = normalizeWheel(e)\n    const newZoom = this.props.zoom - (pixelY * this.props.zoomSpeed) / 200\n    this.setNewZoom(newZoom, point, { shouldUpdatePosition: true })\n\n    if (!this.state.hasWheelJustStarted) {\n      this.setState({ hasWheelJustStarted: true }, () => this.props.onInteractionStart?.())\n    }\n\n    if (this.wheelTimer) {\n      clearTimeout(this.wheelTimer)\n    }\n    this.wheelTimer = this.currentWindow.setTimeout(\n      () => this.setState({ hasWheelJustStarted: false }, () => this.props.onInteractionEnd?.()),\n      250\n    )\n  }\n\n  getPointOnContainer = ({ x, y }: Point, containerTopLeft: Point): Point => {\n    if (!this.containerRect) {\n      throw new Error('The Cropper is not mounted')\n    }\n    return {\n      x: this.containerRect.width / 2 - (x - containerTopLeft.x),\n      y: this.containerRect.height / 2 - (y - containerTopLeft.y),\n    }\n  }\n\n  getPointOnMedia = ({ x, y }: Point) => {\n    const { crop, zoom } = this.props\n    return {\n      x: (x + crop.x) / zoom,\n      y: (y + crop.y) / zoom,\n    }\n  }\n\n  setNewZoom = (zoom: number, point: Point, { shouldUpdatePosition = true } = {}) => {\n    if (!this.state.cropSize || !this.props.onZoomChange) return\n\n    const newZoom = clamp(zoom, this.props.minZoom, this.props.maxZoom)\n\n    if (shouldUpdatePosition) {\n      const zoomPoint = this.getPointOnContainer(point, this.containerPosition)\n      const zoomTarget = this.getPointOnMedia(zoomPoint)\n      const requestedPosition = {\n        x: zoomTarget.x * newZoom - zoomPoint.x,\n        y: zoomTarget.y * newZoom - zoomPoint.y,\n      }\n\n      const newPosition = this.props.restrictPosition\n        ? restrictPosition(\n            requestedPosition,\n            this.mediaSize,\n            this.state.cropSize,\n            newZoom,\n            this.props.rotation\n          )\n        : requestedPosition\n\n      this.props.onCropChange(newPosition)\n    }\n    this.props.onZoomChange(newZoom)\n  }\n\n  getCropData = () => {\n    if (!this.state.cropSize) {\n      return null\n    }\n\n    // this is to ensure the crop is correctly restricted after a zoom back (https://github.com/ValentinH/react-easy-crop/issues/6)\n    const restrictedPosition = this.props.restrictPosition\n      ? restrictPosition(\n          this.props.crop,\n          this.mediaSize,\n          this.state.cropSize,\n          this.props.zoom,\n          this.props.rotation\n        )\n      : this.props.crop\n    return computeCroppedArea(\n      restrictedPosition,\n      this.mediaSize,\n      this.state.cropSize,\n      this.getAspect(),\n      this.props.zoom,\n      this.props.rotation,\n      this.props.restrictPosition\n    )\n  }\n\n  emitCropData = () => {\n    const cropData = this.getCropData()\n    if (!cropData) return\n\n    const { croppedAreaPercentages, croppedAreaPixels } = cropData\n    if (this.props.onCropComplete) {\n      this.props.onCropComplete(croppedAreaPercentages, croppedAreaPixels)\n    }\n\n    if (this.props.onCropAreaChange) {\n      this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels)\n    }\n  }\n\n  emitCropAreaChange = () => {\n    const cropData = this.getCropData()\n    if (!cropData) return\n\n    const { croppedAreaPercentages, croppedAreaPixels } = cropData\n    if (this.props.onCropAreaChange) {\n      this.props.onCropAreaChange(croppedAreaPercentages, croppedAreaPixels)\n    }\n  }\n\n  recomputeCropPosition = () => {\n    if (!this.state.cropSize) return\n\n    const newPosition = this.props.restrictPosition\n      ? restrictPosition(\n          this.props.crop,\n          this.mediaSize,\n          this.state.cropSize,\n          this.props.zoom,\n          this.props.rotation\n        )\n      : this.props.crop\n\n    this.props.onCropChange(newPosition)\n    this.emitCropData()\n  }\n\n  onKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {\n    const { crop, onCropChange, keyboardStep, zoom, rotation } = this.props\n    let step = keyboardStep\n\n    if (!this.state.cropSize) return\n\n    // if the shift key is pressed, reduce the step to allow finer control\n    if (event.shiftKey) {\n      step *= 0.2\n    }\n\n    let newCrop = { ...crop }\n\n    switch (event.key) {\n      case 'ArrowUp':\n        newCrop.y -= step\n        event.preventDefault()\n        break\n      case 'ArrowDown':\n        newCrop.y += step\n        event.preventDefault()\n        break\n      case 'ArrowLeft':\n        newCrop.x -= step\n        event.preventDefault()\n        break\n      case 'ArrowRight':\n        newCrop.x += step\n        event.preventDefault()\n        break\n      default:\n        return\n    }\n\n    if (this.props.restrictPosition) {\n      newCrop = restrictPosition(newCrop, this.mediaSize, this.state.cropSize, zoom, rotation)\n    }\n\n    if (!event.repeat) {\n      this.props.onInteractionStart?.()\n    }\n\n    onCropChange(newCrop)\n  }\n\n  onKeyUp = (event: React.KeyboardEvent<HTMLDivElement>) => {\n    switch (event.key) {\n      case 'ArrowUp':\n      case 'ArrowDown':\n      case 'ArrowLeft':\n      case 'ArrowRight':\n        event.preventDefault()\n        break\n      default:\n        return\n    }\n    this.emitCropData()\n    this.props.onInteractionEnd?.()\n  }\n\n  render() {\n    const {\n      image,\n      video,\n      mediaProps,\n      cropperProps,\n      transform,\n      crop: { x, y },\n      rotation,\n      zoom,\n      cropShape,\n      showGrid,\n      roundCropAreaPixels,\n      style: { containerStyle, cropAreaStyle, mediaStyle },\n      classes: { containerClassName, cropAreaClassName, mediaClassName },\n    } = this.props\n\n    const objectFit = this.state.mediaObjectFit ?? this.getObjectFit()\n\n    return (\n      <div\n        onMouseDown={this.onMouseDown}\n        onTouchStart={this.onTouchStart}\n        ref={(el) => (this.containerRef = el)}\n        data-testid=\"container\"\n        style={containerStyle}\n        className={classNames('reactEasyCrop_Container', containerClassName)}\n      >\n        {image ? (\n          <img\n            alt=\"\"\n            className={classNames(\n              'reactEasyCrop_Image',\n              objectFit === 'contain' && 'reactEasyCrop_Contain',\n              objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal',\n              objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical',\n              mediaClassName\n            )}\n            {...(mediaProps as React.ImgHTMLAttributes<HTMLElement>)}\n            src={image}\n            ref={this.imageRef}\n            style={{\n              ...mediaStyle,\n              transform:\n                transform || `translate(${x}px, ${y}px) rotate(${rotation}deg) scale(${zoom})`,\n            }}\n            onLoad={this.onMediaLoad}\n          />\n        ) : (\n          video && (\n            <video\n              autoPlay\n              playsInline\n              loop\n              muted={true}\n              className={classNames(\n                'reactEasyCrop_Video',\n                objectFit === 'contain' && 'reactEasyCrop_Contain',\n                objectFit === 'horizontal-cover' && 'reactEasyCrop_Cover_Horizontal',\n                objectFit === 'vertical-cover' && 'reactEasyCrop_Cover_Vertical',\n                mediaClassName\n              )}\n              {...mediaProps}\n              ref={this.videoRef}\n              onLoadedMetadata={this.onMediaLoad}\n              style={{\n                ...mediaStyle,\n                transform:\n                  transform || `translate(${x}px, ${y}px) rotate(${rotation}deg) scale(${zoom})`,\n              }}\n              controls={false}\n            >\n              {(Array.isArray(video) ? video : [{ src: video }]).map((item) => (\n                <source key={item.src} {...item} />\n              ))}\n            </video>\n          )\n        )}\n        {this.state.cropSize && (\n          <div\n            ref={this.cropperRef}\n            style={{\n              ...cropAreaStyle,\n              width: roundCropAreaPixels\n                ? Math.round(this.state.cropSize.width)\n                : this.state.cropSize.width,\n              height: roundCropAreaPixels\n                ? Math.round(this.state.cropSize.height)\n                : this.state.cropSize.height,\n            }}\n            tabIndex={0}\n            onKeyDown={this.onKeyDown}\n            onKeyUp={this.onKeyUp}\n            data-testid=\"cropper\"\n            className={classNames(\n              'reactEasyCrop_CropArea',\n              cropShape === 'round' && 'reactEasyCrop_CropAreaRound',\n              showGrid && 'reactEasyCrop_CropAreaGrid',\n              cropAreaClassName\n            )}\n            {...cropperProps}\n          />\n        )}\n      </div>\n    )\n  }\n}\n\nexport default Cropper\n"], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AA8CA,QAAI,aAAa;AAGjB,QAAI;AAAJ,QAAS;AAAT,QAAmB;AAAnB,QAA2B;AAA3B,QAAoC;AAGpC,QAAI;AAGJ,QAAI;AAAJ,QAAU;AAAV,QAAoB;AAApB,QAA4B;AAG5B,QAAI;AAGJ,QAAI;AAAJ,QAAa;AAAb,QAAoB;AAEpB,QAAI;AAEJ,aAAS,YAAY;AACnB,UAAI,YAAY;AACd;AAAA,MACF;AAEA,mBAAa;AAOb,UAAI,MAAM,UAAU;AACpB,UAAI,QAAQ,iLAAiL,KAAK,GAAG;AACrM,UAAI,KAAQ,+BAA+B,KAAK,GAAG;AAEnD,gBAAU,qBAAqB,KAAK,GAAG;AACvC,cAAQ,cAAc,KAAK,GAAG;AAC9B,iBAAW,WAAW,KAAK,GAAG;AAC9B,gBAAU,cAAc,KAAK,GAAG;AAChC,gBAAU,UAAU,KAAK,GAAG;AAO5B,eAAS,CAAC,CAAE,QAAQ,KAAK,GAAG;AAE5B,UAAI,OAAO;AACT,cAAM,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAC9B,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAAI;AAExC,YAAI,OAAO,YAAY,SAAS,cAAc;AAC5C,gBAAM,SAAS;AAAA,QACjB;AAEA,YAAI,UAAU,yBAAyB,KAAK,GAAG;AAC/C,2BAAmB,UAAU,WAAW,QAAQ,CAAC,CAAC,IAAI,IAAI;AAE1D,mBAAW,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAAI;AAC7C,iBAAW,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAAI;AAC7C,kBAAW,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAAI;AAC7C,YAAI,SAAS;AAIX,kBAAQ,yBAAyB,KAAK,GAAG;AACzC,oBAAU,SAAS,MAAM,CAAC,IAAI,WAAW,MAAM,CAAC,CAAC,IAAI;AAAA,QACvD,OAAO;AACL,oBAAU;AAAA,QACZ;AAAA,MACF,OAAO;AACL,cAAM,WAAW,SAAS,UAAU,UAAU;AAAA,MAChD;AAEA,UAAI,IAAI;AACN,YAAI,GAAG,CAAC,GAAG;AAMT,cAAI,MAAM,iCAAiC,KAAK,GAAG;AAEnD,iBAAO,MAAM,WAAW,IAAI,CAAC,EAAE,QAAQ,KAAK,GAAG,CAAC,IAAI;AAAA,QACtD,OAAO;AACL,iBAAO;AAAA,QACT;AACA,mBAAW,CAAC,CAAC,GAAG,CAAC;AACjB,iBAAW,CAAC,CAAC,GAAG,CAAC;AAAA,MACnB,OAAO;AACL,eAAO,WAAW,SAAS;AAAA,MAC7B;AAAA,IACF;AAEA,QAAI,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQzB,IAAI,WAAW;AACb,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,qBAAqB,WAAW;AAC9B,eAAO,UAAU,KAAM,mBAAmB;AAAA,MAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,MAAM,WAAW;AACf,eAAO,qBAAqB,GAAG,KAAK;AAAA,MACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,SAAS,WAAW;AAClB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,OAAO,WAAW;AAChB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,QAAQ,WAAW;AACjB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,QAAQ,WAAW;AACjB,eAAO,qBAAqB,OAAO;AAAA,MACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,QAAS,WAAW;AAClB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,SAAS,WAAW;AAClB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,KAAK,WAAW;AACd,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,OAAO,WAAW;AAChB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,QAAQ,WAAW;AACjB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA,MAEA,QAAQ,WAAW;AACjB,eAAO,UAAU,MAAM,WAAW,SAAS,YAAY;AAAA,MACzD;AAAA,MAEA,WAAW,WAAW;AAEpB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA,MAEA,SAAS,WAAW;AAClB,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA,MAEA,MAAM,WAAW;AACf,eAAO,UAAU,KAAK;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACzRjB;AAAA;AAAA;AAeA,QAAI,YAAY,CAAC,EACf,OAAO,WAAW,eAClB,OAAO,YACP,OAAO,SAAS;AASlB,QAAI,uBAAuB;AAAA,MAEzB;AAAA,MAEA,eAAe,OAAO,WAAW;AAAA,MAEjC,sBACE,aAAa,CAAC,EAAE,OAAO,oBAAoB,OAAO;AAAA,MAEpD,gBAAgB,aAAa,CAAC,CAAC,OAAO;AAAA,MAEtC,YAAY,CAAC;AAAA;AAAA,IAEf;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1CjB;AAAA;AAAA;AAaA,QAAI,uBAAuB;AAE3B,QAAI;AACJ,QAAI,qBAAqB,WAAW;AAClC,sBACE,SAAS,kBACT,SAAS,eAAe;AAAA;AAAA,MAGxB,SAAS,eAAe,WAAW,IAAI,EAAE,MAAM;AAAA,IACnD;AAgBA,aAAS,iBAAiB,iBAAiB,SAAS;AAClD,UAAI,CAAC,qBAAqB,aACtB,WAAW,EAAE,sBAAsB,WAAW;AAChD,eAAO;AAAA,MACT;AAEA,UAAI,YAAY,OAAO;AACvB,UAAI,cAAc,aAAa;AAE/B,UAAI,CAAC,aAAa;AAChB,YAAI,UAAU,SAAS,cAAc,KAAK;AAC1C,gBAAQ,aAAa,WAAW,SAAS;AACzC,sBAAc,OAAO,QAAQ,SAAS,MAAM;AAAA,MAC9C;AAEA,UAAI,CAAC,eAAe,iBAAiB,oBAAoB,SAAS;AAEhE,sBAAc,SAAS,eAAe,WAAW,gBAAgB,KAAK;AAAA,MACxE;AAEA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC9DjB;AAAA;AAAA;AAcA,QAAI,uBAAuB;AAE3B,QAAI,mBAAmB;AAIvB,QAAI,aAAc;AAClB,QAAI,cAAc;AAClB,QAAI,cAAc;AAsGlB,aAASA,gBAA0B,OAAkB;AACnD,UAAI,KAAK,GAAG,KAAK,GACb,KAAK,GAAG,KAAK;AAGjB,UAAI,YAAiB,OAAO;AAAE,aAAK,MAAM;AAAA,MAAQ;AACjD,UAAI,gBAAiB,OAAO;AAAE,aAAK,CAAC,MAAM,aAAa;AAAA,MAAK;AAC5D,UAAI,iBAAiB,OAAO;AAAE,aAAK,CAAC,MAAM,cAAc;AAAA,MAAK;AAC7D,UAAI,iBAAiB,OAAO;AAAE,aAAK,CAAC,MAAM,cAAc;AAAA,MAAK;AAG7D,UAAK,UAAU,SAAS,MAAM,SAAS,MAAM,iBAAkB;AAC7D,aAAK;AACL,aAAK;AAAA,MACP;AAEA,WAAK,KAAK;AACV,WAAK,KAAK;AAEV,UAAI,YAAY,OAAO;AAAE,aAAK,MAAM;AAAA,MAAQ;AAC5C,UAAI,YAAY,OAAO;AAAE,aAAK,MAAM;AAAA,MAAQ;AAE5C,WAAK,MAAM,OAAO,MAAM,WAAW;AACjC,YAAI,MAAM,aAAa,GAAG;AACxB,gBAAM;AACN,gBAAM;AAAA,QACR,OAAO;AACL,gBAAM;AACN,gBAAM;AAAA,QACR;AAAA,MACF;AAGA,UAAI,MAAM,CAAC,IAAI;AAAE,aAAM,KAAK,IAAK,KAAK;AAAA,MAAG;AACzC,UAAI,MAAM,CAAC,IAAI;AAAE,aAAM,KAAK,IAAK,KAAK;AAAA,MAAG;AAEzC,aAAO;AAAA,QAAE,OAAS;AAAA,QACT,OAAS;AAAA,QACT,QAAS;AAAA,QACT,QAAS;AAAA,MAAG;AAAA,IACvB;AAQA,IAAAA,gBAAe,eAAe,WAAsB;AAClD,aAAQ,qBAAqB,QAAQ,IAC1B,mBACC,iBAAiB,OAAO,IACrB,UACA;AAAA,IACjB;AAEA,WAAO,UAAUA;AAAA;AAAA;;;ACpLjB;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;;;ACMD,SAAAC,YACdC,YACAC,aACAC,gBACAC,iBACAC,QACAC,UAAY;AAAZ,MAAAA,aAAA,QAAA;AAAAA,eAAY;EAAA;AAEN,MAAAC,KAAoBC,WAAWP,YAAYC,aAAaI,QAAQ,GAA9DG,QAAKF,GAAAE,OAAEC,SAAM,GAAA;AACrB,MAAMC,eAAeC,KAAKC,IAAIJ,OAAON,cAAc;AACnD,MAAMW,gBAAgBF,KAAKC,IAAIH,QAAQN,eAAe;AAEtD,MAAIO,eAAeG,gBAAgBT,QAAQ;AACzC,WAAO;MACLI,OAAOK,gBAAgBT;MACvBK,QAAQI;;EAEX;AAED,SAAO;IACLL,OAAOE;IACPD,QAAQC,eAAeN;;AAE3B;AAMM,SAAUU,aAAaC,WAAoB;AAE/C,SAAOA,UAAUP,QAAQO,UAAUN,SAC/BM,UAAUP,QAAQO,UAAUC,eAC5BD,UAAUN,SAASM,UAAUE;AACnC;AAKM,SAAUC,iBACdC,UACAJ,WACAK,UACAC,MACAhB,UAAY;AAAZ,MAAAA,aAAA,QAAA;AAAAA,eAAY;EAAA;AAEN,MAAAC,KAAoBC,WAAWQ,UAAUP,OAAOO,UAAUN,QAAQJ,QAAQ,GAAxEG,QAAK,GAAA,OAAEC,SAAM,GAAA;AAErB,SAAO;IACLa,GAAGC,sBAAsBJ,SAASG,GAAGd,OAAOY,SAASZ,OAAOa,IAAI;IAChEG,GAAGD,sBAAsBJ,SAASK,GAAGf,QAAQW,SAASX,QAAQY,IAAI;;AAEtE;AAEA,SAASE,sBACPJ,UACAJ,WACAK,UACAC,MAAY;AAEZ,MAAMI,cAAeV,YAAYM,OAAQ,IAAID,WAAW;AAExD,SAAOM,MAAMP,UAAU,CAACM,aAAaA,WAAW;AAClD;AAEgB,SAAAE,yBAAyBC,QAAeC,QAAa;AACnE,SAAOlB,KAAKmB,KAAKnB,KAAKoB,IAAIH,OAAOJ,IAAIK,OAAOL,GAAG,CAAC,IAAIb,KAAKoB,IAAIH,OAAON,IAAIO,OAAOP,GAAG,CAAC,CAAC;AACtF;AAEgB,SAAAU,yBAAyBJ,QAAeC,QAAa;AACnE,SAAQlB,KAAKsB,MAAMJ,OAAOL,IAAII,OAAOJ,GAAGK,OAAOP,IAAIM,OAAON,CAAC,IAAI,MAAOX,KAAKuB;AAC7E;AAMgB,SAAAC,mBACdC,MACArB,WACAK,UACAhB,QACAiB,MACAhB,UACAa,mBAAuB;AADvB,MAAAb,aAAA,QAAA;AAAAA,eAAY;EAAA;AACZ,MAAAa,sBAAA,QAAA;AAAAA,IAAAA,oBAAuB;EAAA;AAIvB,MAAMmB,cAAcnB,oBAAmBoB,YAAYC;AAEnD,MAAMC,gBAAgBjC,WAAWQ,UAAUP,OAAOO,UAAUN,QAAQJ,QAAQ;AAC5E,MAAMoC,uBAAuBlC,WAAWQ,UAAUC,cAAcD,UAAUE,eAAeZ,QAAQ;AAIjG,MAAMqC,yBAAyB;IAC7BpB,GAAGe,YACD,OACGG,cAAchC,QAAQY,SAASZ,QAAQa,QAAQ,IAAIe,KAAKd,IAAID,QAAQmB,cAAchC,QACnF,GAAG;IAEPgB,GAAGa,YACD,OACGG,cAAc/B,SAASW,SAASX,SAASY,QAAQ,IAAIe,KAAKZ,IAAIH,QAC/DmB,cAAc/B,SACd,GAAG;IAEPD,OAAO6B,YAAY,KAAOjB,SAASZ,QAAQgC,cAAchC,QAAS,MAAOa,IAAI;IAC7EZ,QAAQ4B,YAAY,KAAOjB,SAASX,SAAS+B,cAAc/B,SAAU,MAAOY,IAAI;;AAIlF,MAAMsB,gBAAgBhC,KAAKiC,MACzBP,YACEI,qBAAqBjC,OACpBkC,uBAAuBlC,QAAQiC,qBAAqBjC,QAAS,GAAG,CAClE;AAEH,MAAMqC,iBAAiBlC,KAAKiC,MAC1BP,YACEI,qBAAqBhC,QACpBiC,uBAAuBjC,SAASgC,qBAAqBhC,SAAU,GAAG,CACpE;AAEH,MAAMqC,qBAAqBL,qBAAqBjC,SAASiC,qBAAqBhC,SAASL;AAMvF,MAAM2C,aAAaD,qBACf;IACEtC,OAAOG,KAAKiC,MAAMC,iBAAiBzC,MAAM;IACzCK,QAAQoC;EACT,IACD;IACErC,OAAOmC;IACPlC,QAAQE,KAAKiC,MAAMD,gBAAgBvC,MAAM;;AAG/C,MAAM4C,oBAAiBC,SAAAA,SAAA,CAAA,GAClBF,UAAU,GAAA;IACbzB,GAAGX,KAAKiC,MACNP,YACEI,qBAAqBjC,QAAQuC,WAAWvC,OACvCkC,uBAAuBpB,IAAImB,qBAAqBjC,QAAS,GAAG,CAC9D;IAEHgB,GAAGb,KAAKiC,MACNP,YACEI,qBAAqBhC,SAASsC,WAAWtC,QACxCiC,uBAAuBlB,IAAIiB,qBAAqBhC,SAAU,GAAG,CAC/D;;AAIL,SAAO;IAAEiC;IAAwBM;;AACnC;AAKA,SAASV,UAAUY,KAAaC,OAAa;AAC3C,SAAOxC,KAAKC,IAAIsC,KAAKvC,KAAKuC,IAAI,GAAGC,KAAK,CAAC;AACzC;AAEA,SAASZ,KAAKa,MAAcD,OAAa;AACvC,SAAOA;AACT;AAKgB,SAAAE,yCACdX,wBACA3B,WACAV,UACAe,UACAkC,SACAC,SAAe;AAEf,MAAMf,gBAAgBjC,WAAWQ,UAAUP,OAAOO,UAAUN,QAAQJ,QAAQ;AAG5E,MAAMgB,OAAOK,MACVN,SAASZ,QAAQgC,cAAchC,SAAU,MAAMkC,uBAAuBlC,QACvE8C,SACAC,OAAO;AAGT,MAAMnB,OAAO;IACXd,GACGD,OAAOmB,cAAchC,QAAS,IAC/BY,SAASZ,QAAQ,IACjBgC,cAAchC,QAAQa,QAAQqB,uBAAuBpB,IAAI;IAC3DE,GACGH,OAAOmB,cAAc/B,SAAU,IAChCW,SAASX,SAAS,IAClB+B,cAAc/B,SAASY,QAAQqB,uBAAuBlB,IAAI;;AAG9D,SAAO;IAAEY;IAAMf;;AACjB;AAKA,SAASmC,6BACPR,mBACAjC,WACAK,UAAc;AAEd,MAAMqC,YAAY3C,aAAaC,SAAS;AAExC,SAAOK,SAASX,SAASW,SAASZ,QAC9BY,SAASX,UAAUuC,kBAAkBvC,SAASgD,aAC9CrC,SAASZ,SAASwC,kBAAkBxC,QAAQiD;AAClD;AAKgB,SAAAC,oCACdV,mBACAjC,WACAV,UACAe,UACAkC,SACAC,SAAe;AAHf,MAAAlD,aAAA,QAAA;AAAAA,eAAY;EAAA;AAKZ,MAAMoC,uBAAuBlC,WAAWQ,UAAUC,cAAcD,UAAUE,eAAeZ,QAAQ;AAEjG,MAAMgB,OAAOK,MACX8B,6BAA6BR,mBAAmBjC,WAAWK,QAAQ,GACnEkC,SACAC,OAAO;AAGT,MAAMI,WACJvC,SAASX,SAASW,SAASZ,QACvBY,SAASX,SAASuC,kBAAkBvC,SACpCW,SAASZ,QAAQwC,kBAAkBxC;AAEzC,MAAM4B,OAAO;IACXd,KACImB,qBAAqBjC,QAAQwC,kBAAkBxC,SAAS,IAAIwC,kBAAkB1B,KAAKqC;IACvFnC,KACIiB,qBAAqBhC,SAASuC,kBAAkBvC,UAAU,IAAIuC,kBAAkBxB,KAClFmC;;AAEJ,SAAO;IAAEvB;IAAMf;;AACjB;AAKgB,SAAAuC,UAAUC,GAAUC,GAAQ;AAC1C,SAAO;IACLxC,IAAIwC,EAAExC,IAAIuC,EAAEvC,KAAK;IACjBE,IAAIsC,EAAEtC,IAAIqC,EAAErC,KAAK;;AAErB;AAEM,SAAUuC,eAAeC,aAAmB;AAChD,SAAQA,cAAcrD,KAAKuB,KAAM;AACnC;SAKgB3B,WAAWC,OAAeC,QAAgBJ,UAAgB;AACxE,MAAM4D,SAASF,eAAe1D,QAAQ;AAEtC,SAAO;IACLG,OAAOG,KAAKuD,IAAIvD,KAAKwD,IAAIF,MAAM,IAAIzD,KAAK,IAAIG,KAAKuD,IAAIvD,KAAKyD,IAAIH,MAAM,IAAIxD,MAAM;IAC9EA,QAAQE,KAAKuD,IAAIvD,KAAKyD,IAAIH,MAAM,IAAIzD,KAAK,IAAIG,KAAKuD,IAAIvD,KAAKwD,IAAIF,MAAM,IAAIxD,MAAM;;AAEnF;SAKgBiB,MAAMyB,OAAevC,KAAasC,KAAW;AAC3D,SAAOvC,KAAKC,IAAID,KAAKuC,IAAIC,OAAOvC,GAAG,GAAGsC,GAAG;AAC3C;SAKgBmB,aAAU;AAAC,MAAgEC,OAAA,CAAA;WAAAC,KAAA,GAAhEA,KAAgEC,UAAAC,QAAhEF,MAAgE;AAAhED,SAAgEC,EAAA,IAAAC,UAAAD,EAAA;;AACzF,SAAOD,KACJI,OAAO,SAACvB,OAAK;AACZ,QAAI,OAAOA,UAAU,YAAYA,MAAMsB,SAAS,GAAG;AACjD,aAAO;IACR;AAED,WAAO;GACR,EACAE,KAAK,GAAG,EACRC,KAAI;AACT;;ACtOA,IAAMC,WAAW;AACjB,IAAMC,WAAW;AACjB,IAAMC,gBAAgB;AAStB,IAAAC;;EAAA,SAAAC,QAAA;AAAsBC,cAAoCF,UAAAC,MAAA;AAA1D,aAAAD,WAAA;AAAA,UAozBCG,QAAAF,WAAA,QAAAA,OAAAG,MAAA,MAAAZ,SAAA,KAAA;AAhyBCW,YAAAE,aAAoDC,gBAAS;AAC7DH,YAAAI,WAAoDD,gBAAS;AAC7DH,YAAAK,WAAoDF,gBAAS;AAC7DH,YAAiBM,oBAAU;QAAEnE,GAAG;QAAGE,GAAG;;AACtC2D,YAAYO,eAA0B;AACtCP,YAAQQ,WAA4B;AACpCR,YAAaS,gBAAmB;AAChCT,YAAApE,YAAuB;QAAEP,OAAO;QAAGC,QAAQ;QAAGO,cAAc;QAAGC,eAAe;;AAC9EkE,YAAiBU,oBAAU;QAAEvE,GAAG;QAAGE,GAAG;;AACtC2D,YAAaW,gBAAU;QAAExE,GAAG;QAAGE,GAAG;;AAClC2D,YAAgBY,mBAAG;AACnBZ,YAAoBa,uBAAG;AACvBb,YAAUc,aAAG;AACbd,YAAiBe,oBAAG;AACpBf,YAAiBgB,oBAAG;AACpBhB,YAAciB,iBAAkB;AAChCjB,YAAekB,kBAAkB;AACjClB,YAAUmB,aAAkB;AAC5BnB,YAAAoB,aAA8B,OAAOC,aAAa,cAAcA,WAAW;AAC3ErB,YAAAsB,gBAA+B,OAAOC,WAAW,cAAcA,SAAS;AACxEvB,YAAcwB,iBAA0B;AAExCxB,YAAAyB,QAAe;QACbxF,UAAU;QACVyF,qBAAqB;QACrBC,gBAAgBC;;AA4GlB5B,YAAA6B,qBAAqB,WAAA;AACnB,YAAI,OAAON,OAAOO,mBAAmB,eAAe,CAAC9B,MAAKO,cAAc;AACtE;QACD;AACD,YAAIwB,gBAAgB;AACpB/B,cAAKwB,iBAAiB,IAAID,OAAOO,eAAe,SAACE,SAAO;AACtD,cAAID,eAAe;AACjBA,4BAAgB;AAChB;UACD;AACD/B,gBAAKiC,aAAY;QACnB,CAAC;AACDjC,cAAKwB,eAAeU,QAAQlC,MAAKO,YAAY;;AAI/CP,YAAiBmC,oBAAG,SAACC,GAAa;AAAA,eAAAA,EAAEC,eAAc;;AAElDrC,YAAAsC,cAAc,WAAA;AACZ,YAAI,CAACtC,MAAKoB,WAAY;AACtBpB,cAAKoB,WAAWmB,oBAAoB,aAAavC,MAAKwC,WAAW;AACjExC,cAAKoB,WAAWmB,oBAAoB,WAAWvC,MAAKyC,aAAa;AACjEzC,cAAKoB,WAAWmB,oBAAoB,aAAavC,MAAK0C,WAAW;AACjE1C,cAAKoB,WAAWmB,oBAAoB,YAAYvC,MAAKyC,aAAa;AAClEzC,cAAKoB,WAAWmB,oBAAoB,iBAAiBvC,MAAK2C,eAAgC;AAC1F3C,cAAKoB,WAAWmB,oBAAoB,cAAcvC,MAAK4C,YAA6B;AACpF5C,cAAKoB,WAAWmB,oBAAoB,UAAUvC,MAAK6C,QAAQ;;AAG7D7C,YAAA8C,mBAAmB,WAAA;AACjB,YAAI9C,MAAKO,aAAcP,OAAKO,aAAagC,oBAAoB,SAASvC,MAAK+C,OAAO;AAClF,YAAI/C,MAAKmB,YAAY;AACnB6B,uBAAahD,MAAKmB,UAAU;QAC7B;;AAGHnB,YAAAiD,cAAc,WAAA;AACZ,YAAMhH,WAAW+D,MAAKiC,aAAY;AAElC,YAAIhG,UAAU;AACZ+D,gBAAKkD,aAAY;AACjBlD,gBAAKmD,eAAelH,QAAQ;QAC7B;AAED,YAAI+D,MAAKoD,MAAMC,eAAe;AAC5BrD,gBAAKoD,MAAMC,cAAcrD,MAAKpE,SAAS;QACxC;;AAGHoE,YAAcmD,iBAAG,SAAClH,UAAc;AAC9B,YAAI+D,MAAKoD,MAAME,+BAA+B;AACtC,cAAAnI,KAAiB+C,yCACrB8B,MAAKoD,MAAME,+BACXtD,MAAKpE,WACLoE,MAAKoD,MAAMlI,UACXe,UACA+D,MAAKoD,MAAMjF,SACX6B,MAAKoD,MAAMhF,OAAO,GANZnB,OAAI,GAAA,MAAEf,OAAI,GAAA;AASlB8D,gBAAKoD,MAAMG,aAAatG,IAAI;AAC5B+C,gBAAKoD,MAAMI,gBAAgBxD,MAAKoD,MAAMI,aAAatH,IAAI;QACxD,WAAU8D,MAAKoD,MAAMK,0BAA0B;AACxC,cAAAC,KAAiBnF,oCACrByB,MAAKoD,MAAMK,0BACXzD,MAAKpE,WACLoE,MAAKoD,MAAMlI,UACXe,UACA+D,MAAKoD,MAAMjF,SACX6B,MAAKoD,MAAMhF,OAAO,GANZnB,OAAI,GAAA,MAAEf,OAAI,GAAA;AASlB8D,gBAAKoD,MAAMG,aAAatG,IAAI;AAC5B+C,gBAAKoD,MAAMI,gBAAgBxD,MAAKoD,MAAMI,aAAatH,IAAI;QACxD;;AAgCH8D,YAAAiC,eAAe,WAAA;;AACb,YAAM0B,WAAW3D,MAAKI,SAASwD,WAAW5D,MAAKK,SAASuD;AAExD,YAAID,YAAY3D,MAAKO,cAAc;AACjCP,gBAAKS,gBAAgBT,MAAKO,aAAasD,sBAAqB;AAC5D7D,gBAAK8D,sBAAqB;AAC1B,cAAMC,kBAAkB/D,MAAKS,cAAcpF,QAAQ2E,MAAKS,cAAcnF;AACtE,cAAMO,iBACJV,KAAA6E,MAAKI,SAASwD,aAAS,QAAAzI,OAAA,SAAA,SAAAA,GAAAU,mBAAgB6H,KAAA1D,MAAKK,SAASuD,aAAO,QAAA,OAAA,SAAA,SAAA,GAAEI,eAAc;AAC9E,cAAMlI,kBACJmI,KAAAjE,MAAKI,SAASwD,aAAS,QAAAK,OAAA,SAAA,SAAAA,GAAAnI,oBAAiBoI,KAAAlE,MAAKK,SAASuD,aAAO,QAAA,OAAA,SAAA,SAAA,GAAEO,gBAAe;AAChF,cAAMC,oBACJT,SAASU,cAAcxI,gBAAgB8H,SAASW,eAAexI;AACjE,cAAMyI,cAAc1I,eAAeC;AAQnC,cAAI0I,oBAAiB;AAErB,cAAIJ,mBAAmB;AACrB,oBAAQpE,MAAKyB,MAAME,gBAAc;cAC/B;cACA,KAAK;AACH6C,oCACET,kBAAkBQ,cACd;kBACElJ,OAAO2E,MAAKS,cAAcnF,SAASiJ;kBACnCjJ,QAAQ0E,MAAKS,cAAcnF;gBAC5B,IACD;kBACED,OAAO2E,MAAKS,cAAcpF;kBAC1BC,QAAQ0E,MAAKS,cAAcpF,QAAQkJ;;AAE3C;cACF,KAAK;AACHC,oCAAoB;kBAClBnJ,OAAO2E,MAAKS,cAAcpF;kBAC1BC,QAAQ0E,MAAKS,cAAcpF,QAAQkJ;;AAErC;cACF,KAAK;AACHC,oCAAoB;kBAClBnJ,OAAO2E,MAAKS,cAAcnF,SAASiJ;kBACnCjJ,QAAQ0E,MAAKS,cAAcnF;;AAE7B;YAAK;UAEV,OAAM;AACLkJ,gCAAoB;cAClBnJ,OAAOsI,SAASU;cAChB/I,QAAQqI,SAASW;;UAEpB;AAEDtE,gBAAKpE,YAASkC,SAAAA,SAAA,CAAA,GACT0G,iBAAiB,GAAA;YACpB3I;YACAC;UAAa,CAAA;AAIf,cAAIkE,MAAKoD,MAAMqB,cAAc;AAC3BzE,kBAAKoD,MAAMqB,aAAazE,MAAKpE,SAAS;UACvC;AAED,cAAMK,WAAW+D,MAAKoD,MAAMnH,WACxB+D,MAAKoD,MAAMnH,WACXrB,YACEoF,MAAKpE,UAAUP,OACf2E,MAAKpE,UAAUN,QACf0E,MAAKS,cAAcpF,OACnB2E,MAAKS,cAAcnF,QACnB0E,MAAKoD,MAAMnI,QACX+E,MAAKoD,MAAMlI,QAAQ;AAGzB,gBACEwJ,KAAA1E,MAAKyB,MAAMxF,cAAQ,QAAAyI,OAAA,SAAA,SAAAA,GAAEpJ,YAAWW,SAASX,YACzCqJ,KAAA3E,MAAKyB,MAAMxF,cAAU,QAAA0I,OAAA,SAAA,SAAAA,GAAAtJ,WAAUY,SAASZ,OACxC;AACA2E,kBAAKoD,MAAMwB,oBAAoB5E,MAAKoD,MAAMwB,iBAAiB3I,QAAQ;UACpE;AACD+D,gBAAK6E,SAAS;YAAE5I;UAAU,GAAE+D,MAAK8E,qBAAqB;AAEtD,cAAI9E,MAAKoD,MAAM2B,aAAa;AAC1B/E,kBAAKoD,MAAM2B,YAAY9I,QAAQ;UAChC;AAED,iBAAOA;QACR;;AAGH+D,YAAA8D,wBAAwB,WAAA;AACtB,YAAI9D,MAAKO,cAAc;AACrB,cAAMyE,SAAShF,MAAKO,aAAasD,sBAAqB;AACtD7D,gBAAKM,oBAAoB;YAAEnE,GAAG6I,OAAOC;YAAM5I,GAAG2I,OAAOE;;QACtD;;AAaHlF,YAAWmF,cAAG,SAAC/C,GAA+C;AAC5D,YAAI,CAACpC,MAAKoB,WAAY;AACtBgB,UAAEC,eAAc;AAChBrC,cAAKoB,WAAWgE,iBAAiB,aAAapF,MAAKwC,WAAW;AAC9DxC,cAAKoB,WAAWgE,iBAAiB,WAAWpF,MAAKyC,aAAa;AAC9DzC,cAAK8D,sBAAqB;AAC1B9D,cAAKqF,YAAYxF,SAAQyF,cAAclD,CAAC,CAAC;;AAG3CpC,YAAAwC,cAAc,SAACJ,GAAa;AAAK,eAAApC,MAAKuF,OAAO1F,SAAQyF,cAAclD,CAAC,CAAC;;AAErEpC,YAAQ6C,WAAG,SAACT,GAAQ;AAClB,YAAI,CAACpC,MAAKoB,WAAY;AACtBgB,UAAEC,eAAc;AAChBrC,cAAK8D,sBAAqB;;AAG5B9D,YAAYwF,eAAG,SAACpD,GAAmC;AACjD,YAAI,CAACpC,MAAKoB,WAAY;AACtBpB,cAAKc,aAAa;AAClB,YAAId,MAAKoD,MAAMqC,kBAAkB,CAACzF,MAAKoD,MAAMqC,eAAerD,CAAC,GAAG;AAC9D;QACD;AAEDpC,cAAKoB,WAAWgE,iBAAiB,aAAapF,MAAK0C,aAAa;UAAEgD,SAAS;SAAO;AAClF1F,cAAKoB,WAAWgE,iBAAiB,YAAYpF,MAAKyC,aAAa;AAE/DzC,cAAK8D,sBAAqB;AAE1B,YAAI1B,EAAEuD,QAAQrG,WAAW,GAAG;AAC1BU,gBAAK4F,aAAaxD,CAAC;mBACVA,EAAEuD,QAAQrG,WAAW,GAAG;AACjCU,gBAAKqF,YAAYxF,SAAQgG,cAAczD,EAAEuD,QAAQ,CAAC,CAAC,CAAC;QACrD;;AAGH3F,YAAW0C,cAAG,SAACN,GAAa;AAE1BA,UAAEC,eAAc;AAChB,YAAID,EAAEuD,QAAQrG,WAAW,GAAG;AAC1BU,gBAAK8F,YAAY1D,CAAC;mBACTA,EAAEuD,QAAQrG,WAAW,GAAG;AACjCU,gBAAKuF,OAAO1F,SAAQgG,cAAczD,EAAEuD,QAAQ,CAAC,CAAC,CAAC;QAChD;;AAGH3F,YAAc+F,iBAAG,SAAC3D,GAAe;AAC/B,YAAI,CAACpC,MAAKoB,WAAY;AACtBgB,UAAEC,eAAc;AAChBrC,cAAKoB,WAAWgE,iBAAiB,iBAAiBpF,MAAK2C,eAAgC;AACvF3C,cAAKoB,WAAWgE,iBAAiB,cAAcpF,MAAK4C,YAA6B;AACjF5C,cAAKY,mBAAmBZ,MAAKoD,MAAMlH;AACnC8D,cAAKa,uBAAuBb,MAAKoD,MAAMlI;;AAGzC8E,YAAe2C,kBAAG,SAACP,GAAe;AAChCA,UAAEC,eAAc;AAChB,YAAIrC,MAAKc,YAAY;AAEnB;QACD;AAED,YAAMkF,QAAQnG,SAAQyF,cAAclD,CAAC;AACrC,YAAM6D,UAAUjG,MAAKY,mBAAmB,IAAIwB,EAAE8D;AAC9ClG,cAAKmG,WAAWF,SAASD,OAAO;UAAEI,sBAAsB;QAAI,CAAE;AAC9D,YAAIpG,MAAKoD,MAAMiD,kBAAkB;AAC/B,cAAMC,cAActG,MAAKa,uBAAuBuB,EAAElH;AAClD8E,gBAAKoD,MAAMiD,iBAAiBC,WAAW;QACxC;;AAGHtG,YAAY4C,eAAG,SAACR,GAAe;AAC7BpC,cAAKsC,YAAW;;AAGlBtC,YAAWqF,cAAG,SAAClK,IAAe;;YAAbgB,IAAChB,GAAAgB,GAAEE,IAAClB,GAAAkB;AACnB2D,cAAKU,oBAAoB;UAAEvE;UAAGE;;AAC9B2D,cAAKW,gBAAqB7C,SAAA,CAAA,GAAAkC,MAAKoD,MAAMnG,IAAI;AACzC,SAAAgH,MAAA,KAAAjE,MAAKoD,OAAMmD,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;;AAG/BvG,YAAMuF,SAAG,SAACpK,IAAe;YAAbgB,IAAChB,GAAAgB,GAAEE,IAAClB,GAAAkB;AACd,YAAI,CAAC2D,MAAKsB,cAAe;AACzB,YAAItB,MAAKiB,eAAgBjB,OAAKsB,cAAckF,qBAAqBxG,MAAKiB,cAAc;AAEpFjB,cAAKiB,iBAAiBjB,MAAKsB,cAAcmF,sBAAsB,WAAA;AAC7D,cAAI,CAACzG,MAAKyB,MAAMxF,SAAU;AAC1B,cAAIE,MAAMyF,UAAavF,MAAMuF,OAAW;AACxC,cAAM8E,UAAUvK,IAAI6D,MAAKU,kBAAkBvE;AAC3C,cAAMwK,UAAUtK,IAAI2D,MAAKU,kBAAkBrE;AAC3C,cAAMuK,oBAAoB;YACxBzK,GAAG6D,MAAKW,cAAcxE,IAAIuK;YAC1BrK,GAAG2D,MAAKW,cAActE,IAAIsK;;AAG5B,cAAME,cAAc7G,MAAKoD,MAAMrH,mBAC3BA,iBACE6K,mBACA5G,MAAKpE,WACLoE,MAAKyB,MAAMxF,UACX+D,MAAKoD,MAAMlH,MACX8D,MAAKoD,MAAMlI,QAAQ,IAErB0L;AACJ5G,gBAAKoD,MAAMG,aAAasD,WAAW;QACrC,CAAC;;AAGH7G,YAAAyC,gBAAgB,WAAA;;AACdzC,cAAKc,aAAa;AAClBd,cAAKsC,YAAW;AAChBtC,cAAKkD,aAAY;AACjB,SAAAQ,MAAA,KAAA1D,MAAKoD,OAAM0D,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;;AAgC7B9G,YAAO+C,UAAG,SAACX,GAAa;AACtB,YAAI,CAACpC,MAAKsB,cAAe;AACzB,YAAItB,MAAKoD,MAAM2D,kBAAkB,CAAC/G,MAAKoD,MAAM2D,eAAe3E,CAAC,GAAG;AAC9D;QACD;AAEDA,UAAEC,eAAc;AAChB,YAAM2D,QAAQnG,SAAQyF,cAAclD,CAAC;AAC7B,YAAA4E,aAAWC,uBAAAA,SAAe7E,CAAC,EAAC;AACpC,YAAM6D,UAAUjG,MAAKoD,MAAMlH,OAAQ8K,SAAShH,MAAKoD,MAAM8D,YAAa;AACpElH,cAAKmG,WAAWF,SAASD,OAAO;UAAEI,sBAAsB;QAAI,CAAE;AAE9D,YAAI,CAACpG,MAAKyB,MAAMC,qBAAqB;AACnC1B,gBAAK6E,SAAS;YAAEnD,qBAAqB;UAAM,GAAE,WAAM;AAAA,gBAAAvG,IAAAuI;AAAA,oBAAA,MAAAvI,KAAA6E,MAAKoD,OAAMmD,wBAAsB,QAAA7C,OAAA,SAAA,SAAAA,GAAAyD,KAAAhM,EAAA;UAAA,CAAA;QACrF;AAED,YAAI6E,MAAKmB,YAAY;AACnB6B,uBAAahD,MAAKmB,UAAU;QAC7B;AACDnB,cAAKmB,aAAanB,MAAKsB,cAAc8F,WACnC,WAAA;AAAM,iBAAApH,MAAK6E,SAAS;YAAEnD,qBAAqB;UAAO,GAAE,WAAA;;AAAM,oBAAAgC,MAAA,KAAA1D,MAAKoD,OAAM0D,sBAAoB,QAAApD,OAAA,SAAA,SAAAA,GAAAyD,KAAAhM,EAAA;WAAA;WACzF,GAAG;;AAIP6E,YAAAqH,sBAAsB,SAAClM,IAAiBmM,kBAAuB;YAAtCnL,IAAChB,GAAAgB,GAAEE,IAAClB,GAAAkB;AAC3B,YAAI,CAAC2D,MAAKS,eAAe;AACvB,gBAAM,IAAI8G,MAAM,4BAA4B;QAC7C;AACD,eAAO;UACLpL,GAAG6D,MAAKS,cAAcpF,QAAQ,KAAKc,IAAImL,iBAAiBnL;UACxDE,GAAG2D,MAAKS,cAAcnF,SAAS,KAAKe,IAAIiL,iBAAiBjL;;;AAI7D2D,YAAewH,kBAAG,SAACrM,IAAe;YAAbgB,IAAChB,GAAAgB,GAAEE,IAAClB,GAAAkB;AACjB,YAAAqH,KAAiB1D,MAAKoD,OAApBnG,OAAIyG,GAAAzG,MAAEf,OAAIwH,GAAAxH;AAClB,eAAO;UACLC,IAAIA,IAAIc,KAAKd,KAAKD;UAClBG,IAAIA,IAAIY,KAAKZ,KAAKH;;;AAItB8D,YAAAmG,aAAa,SAACjK,MAAc8J,OAAc7K,IAAoC;YAApCuI,KAAkCvI,OAAA,SAAA,CAAA,IAAE,IAAlC8I,KAAAP,GAAA0C,sBAAAA,uBAAoBnC,OAAA,SAAG,OAAIA;AACrE,YAAI,CAACjE,MAAKyB,MAAMxF,YAAY,CAAC+D,MAAKoD,MAAMI,aAAc;AAEtD,YAAMyC,UAAU1J,MAAML,MAAM8D,MAAKoD,MAAMjF,SAAS6B,MAAKoD,MAAMhF,OAAO;AAElE,YAAIgI,sBAAsB;AACxB,cAAMqB,YAAYzH,MAAKqH,oBAAoBrB,OAAOhG,MAAKM,iBAAiB;AACxE,cAAMoH,aAAa1H,MAAKwH,gBAAgBC,SAAS;AACjD,cAAMb,oBAAoB;YACxBzK,GAAGuL,WAAWvL,IAAI8J,UAAUwB,UAAUtL;YACtCE,GAAGqL,WAAWrL,IAAI4J,UAAUwB,UAAUpL;;AAGxC,cAAMwK,cAAc7G,MAAKoD,MAAMrH,mBAC3BA,iBACE6K,mBACA5G,MAAKpE,WACLoE,MAAKyB,MAAMxF,UACXgK,SACAjG,MAAKoD,MAAMlI,QAAQ,IAErB0L;AAEJ5G,gBAAKoD,MAAMG,aAAasD,WAAW;QACpC;AACD7G,cAAKoD,MAAMI,aAAayC,OAAO;;AAGjCjG,YAAA2H,cAAc,WAAA;AACZ,YAAI,CAAC3H,MAAKyB,MAAMxF,UAAU;AACxB,iBAAO;QACR;AAGD,YAAM2L,qBAAqB5H,MAAKoD,MAAMrH,mBAClCA,iBACEiE,MAAKoD,MAAMnG,MACX+C,MAAKpE,WACLoE,MAAKyB,MAAMxF,UACX+D,MAAKoD,MAAMlH,MACX8D,MAAKoD,MAAMlI,QAAQ,IAErB8E,MAAKoD,MAAMnG;AACf,eAAOD,mBACL4K,oBACA5H,MAAKpE,WACLoE,MAAKyB,MAAMxF,UACX+D,MAAK6H,UAAS,GACd7H,MAAKoD,MAAMlH,MACX8D,MAAKoD,MAAMlI,UACX8E,MAAKoD,MAAMrH,gBAAgB;;AAI/BiE,YAAAkD,eAAe,WAAA;AACb,YAAM4E,WAAW9H,MAAK2H,YAAW;AACjC,YAAI,CAACG,SAAU;AAEP,YAAAvK,yBAA8CuK,SAAQvK,wBAA9BM,oBAAsBiK,SAAQjK;AAC9D,YAAImC,MAAKoD,MAAM2E,gBAAgB;AAC7B/H,gBAAKoD,MAAM2E,eAAexK,wBAAwBM,iBAAiB;QACpE;AAED,YAAImC,MAAKoD,MAAM4E,kBAAkB;AAC/BhI,gBAAKoD,MAAM4E,iBAAiBzK,wBAAwBM,iBAAiB;QACtE;;AAGHmC,YAAAiI,qBAAqB,WAAA;AACnB,YAAMH,WAAW9H,MAAK2H,YAAW;AACjC,YAAI,CAACG,SAAU;AAEP,YAAAvK,yBAA8CuK,SAAQvK,wBAA9BM,oBAAsBiK,SAAQjK;AAC9D,YAAImC,MAAKoD,MAAM4E,kBAAkB;AAC/BhI,gBAAKoD,MAAM4E,iBAAiBzK,wBAAwBM,iBAAiB;QACtE;;AAGHmC,YAAA8E,wBAAwB,WAAA;AACtB,YAAI,CAAC9E,MAAKyB,MAAMxF,SAAU;AAE1B,YAAM4K,cAAc7G,MAAKoD,MAAMrH,mBAC3BA,iBACEiE,MAAKoD,MAAMnG,MACX+C,MAAKpE,WACLoE,MAAKyB,MAAMxF,UACX+D,MAAKoD,MAAMlH,MACX8D,MAAKoD,MAAMlI,QAAQ,IAErB8E,MAAKoD,MAAMnG;AAEf+C,cAAKoD,MAAMG,aAAasD,WAAW;AACnC7G,cAAKkD,aAAY;;AAGnBlD,YAASkI,YAAG,SAACC,OAA0C;;AAC/C,YAAAlE,KAAuDjE,MAAKoD,OAA1DnG,OAAI,GAAA,MAAEsG,eAAY,GAAA,cAAE6E,eAAY,GAAA,cAAElM,OAAI,GAAA,MAAEhB,WAAQ,GAAA;AACxD,YAAImN,OAAOD;AAEX,YAAI,CAACpI,MAAKyB,MAAMxF,SAAU;AAG1B,YAAIkM,MAAMG,UAAU;AAClBD,kBAAQ;QACT;AAED,YAAIE,UAAOzK,SAAA,CAAA,GAAQb,IAAI;AAEvB,gBAAQkL,MAAMK,KAAG;UACf,KAAK;AACHD,oBAAQlM,KAAKgM;AACbF,kBAAM9F,eAAc;AACpB;UACF,KAAK;AACHkG,oBAAQlM,KAAKgM;AACbF,kBAAM9F,eAAc;AACpB;UACF,KAAK;AACHkG,oBAAQpM,KAAKkM;AACbF,kBAAM9F,eAAc;AACpB;UACF,KAAK;AACHkG,oBAAQpM,KAAKkM;AACbF,kBAAM9F,eAAc;AACpB;UACF;AACE;QAAM;AAGV,YAAIrC,MAAKoD,MAAMrH,kBAAkB;AAC/BwM,oBAAUxM,iBAAiBwM,SAASvI,MAAKpE,WAAWoE,MAAKyB,MAAMxF,UAAUC,MAAMhB,QAAQ;QACxF;AAED,YAAI,CAACiN,MAAMM,QAAQ;AACjB,WAAA/E,MAAA,KAAA1D,MAAKoD,OAAMmD,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;QAC9B;AAEDhD,qBAAagF,OAAO;;AAGtBvI,YAAO0I,UAAG,SAACP,OAA0C;;AACnD,gBAAQA,MAAMK,KAAG;UACf,KAAK;UACL,KAAK;UACL,KAAK;UACL,KAAK;AACHL,kBAAM9F,eAAc;AACpB;UACF;AACE;QAAM;AAEVrC,cAAKkD,aAAY;AACjB,SAAAQ,MAAA,KAAA1D,MAAKoD,OAAM0D,sBAAgB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;;;IA6G/B;AApwBEjH,IAAAA,SAAA8I,UAAAC,oBAAA,WAAA;AACE,UAAI,CAAC,KAAKxH,cAAc,CAAC,KAAKE,cAAe;AAC7C,UAAI,KAAKf,cAAc;AACrB,YAAI,KAAKA,aAAasI,eAAe;AACnC,eAAKzH,aAAa,KAAKb,aAAasI;QACrC;AACD,YAAI,KAAKzH,WAAW0H,aAAa;AAC/B,eAAKxH,gBAAgB,KAAKF,WAAW0H;QACtC;AAED,aAAKjH,mBAAkB;AAEvB,YAAI,OAAON,OAAOO,mBAAmB,aAAa;AAChD,eAAKR,cAAc8D,iBAAiB,UAAU,KAAKnD,YAAY;QAChE;AACD,aAAKmB,MAAM2F,kBACT,KAAKxI,aAAa6E,iBAAiB,SAAS,KAAKrC,SAAS;UAAE2C,SAAS;QAAK,CAAE;AAC9E,aAAKnF,aAAa6E,iBAAiB,gBAAgB,KAAKW,cAA+B;MACxF;AAED,WAAK3E,WAAWgE,iBAAiB,UAAU,KAAKvC,QAAQ;AAExD,UAAI,CAAC,KAAKO,MAAM4F,iCAAiC;AAC/C,aAAKxI,WAAW,KAAKY,WAAW6H,cAAc,OAAO;AACrD,aAAKzI,SAAS0I,aAAa,QAAQ,UAAU;AAC7C,YAAI,KAAK9F,MAAM+F,OAAO;AACpB,eAAK3I,SAAS0I,aAAa,SAAS,KAAK9F,MAAM+F,KAAK;QACrD;AACD,aAAK3I,SAAS4I,YAAYC;AAC1B,aAAKjI,WAAWkI,KAAKC,YAAY,KAAK/I,QAAQ;MAC/C;AAGD,UAAI,KAAKJ,SAASwD,WAAW,KAAKxD,SAASwD,QAAQ4F,UAAU;AAC3D,aAAKvG,YAAW;MACjB;AAGD,UAAI,KAAKG,MAAMqG,aAAa;AAC1B,aAAKrG,MAAMqG,YAAY,KAAKrJ,QAAQ;MACrC;AAED,UAAI,KAAKgD,MAAMsG,aAAa;AAC1B,aAAKtG,MAAMsG,YAAY,KAAKrJ,QAAQ;MACrC;AAED,UAAI,KAAK+C,MAAMuG,eAAe;AAC5B,aAAKvG,MAAMuG,cAAc,KAAKzJ,UAAU;MACzC;;AAGHL,IAAAA,SAAA8I,UAAAiB,uBAAA,WAAA;;AACE,UAAI,CAAC,KAAKxI,cAAc,CAAC,KAAKE,cAAe;AAC7C,UAAI,OAAOC,OAAOO,mBAAmB,aAAa;AAChD,aAAKR,cAAciB,oBAAoB,UAAU,KAAKN,YAAY;MACnE;AACD,OAAA9G,KAAA,KAAKqG,oBAAgB,QAAArG,OAAA,SAAA,SAAAA,GAAA0O,WAAU;AAC/B,UAAI,KAAKtJ,cAAc;AACrB,aAAKA,aAAagC,oBAAoB,gBAAgB,KAAKJ,iBAAiB;MAC7E;AAED,UAAI,KAAK3B,UAAU;AACjB,SAAAkD,KAAA,KAAKlD,SAASsJ,gBAAU,QAAApG,OAAA,SAAA,SAAAA,GAAEqG,YAAY,KAAKvJ,QAAQ;MACpD;AAED,WAAK8B,YAAW;AAChB,WAAKc,MAAM2F,kBAAkB,KAAKjG,iBAAgB;;AAGpDjD,IAAAA,SAAkB8I,UAAAqB,qBAAlB,SAAmBC,WAAuB;;AACxC,UAAIA,UAAU/O,aAAa,KAAKkI,MAAMlI,UAAU;AAC9C,aAAK+G,aAAY;AACjB,aAAK6C,sBAAqB;iBACjBmF,UAAUhP,WAAW,KAAKmI,MAAMnI,QAAQ;AACjD,aAAKgH,aAAY;iBACRgI,UAAUC,cAAc,KAAK9G,MAAM8G,WAAW;AACvD,aAAKjI,aAAY;iBACRgI,UAAU/N,SAAS,KAAKkH,MAAMlH,MAAM;AAC7C,aAAK4I,sBAAqB;mBAE1B3J,KAAA8O,UAAUhO,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAEX,cAAWoI,KAAA,KAAKN,MAAMnH,cAAQ,QAAAyH,OAAA,SAAA,SAAAA,GAAEpI,aACpD,KAAA2O,UAAUhO,cAAQ,QAAAgI,OAAA,SAAA,SAAAA,GAAE5I,aAAU6I,KAAA,KAAKd,MAAMnH,cAAU,QAAAiI,OAAA,SAAA,SAAAA,GAAA7I,QACnD;AACA,aAAK4G,aAAY;mBAEjByC,KAAAuF,UAAUhN,UAAI,QAAA,OAAA,SAAA,SAAA,GAAEd,SAAMwI,KAAA,KAAKvB,MAAMnG,UAAI,QAAA0H,OAAA,SAAA,SAAAA,GAAExI,QACvC,KAAA8N,UAAUhN,UAAI,QAAAkN,OAAA,SAAA,SAAAA,GAAE9N,SAAM+N,KAAA,KAAKhH,MAAMnG,UAAM,QAAAmN,OAAA,SAAA,SAAAA,GAAA/N,IACvC;AACA,aAAK4L,mBAAkB;MACxB;AACD,UAAIgC,UAAUlB,mBAAmB,KAAK3F,MAAM2F,kBAAkB,KAAKxI,cAAc;AAC/E,aAAK6C,MAAM2F,iBACP,KAAKxI,aAAa6E,iBAAiB,SAAS,KAAKrC,SAAS;UAAE2C,SAAS;SAAO,IAC5E,KAAK5C,iBAAgB;MAC1B;AACD,UAAImH,UAAUI,UAAU,KAAKjH,MAAMiH,OAAO;AACxC,SAAAC,KAAA,KAAKjK,SAASuD,aAAS,QAAA0G,OAAA,SAAA,SAAAA,GAAAC,KAAI;MAC5B;AAED,UAAML,YAAY,KAAKM,aAAY;AACnC,UAAIN,cAAc,KAAKzI,MAAME,gBAAgB;AAC3C,aAAKkD,SAAS;UAAElD,gBAAgBuI;QAAS,GAAI,KAAKjI,YAAY;MAC/D;;AAgFHpC,IAAAA,SAAA8I,UAAAd,YAAA,WAAA;AACQ,UAAA1M,KAAuB,KAAKiI,OAA1BnH,WAAQd,GAAAc,UAAEhB,SAAME,GAAAF;AACxB,UAAIgB,UAAU;AACZ,eAAOA,SAASZ,QAAQY,SAASX;MAClC;AACD,aAAOL;;AAGT4E,IAAAA,SAAA8I,UAAA6B,eAAA,WAAA;;AACE,UAAI,KAAKpH,MAAM8G,cAAc,SAAS;AACpC,YAAMvG,WAAW,KAAKvD,SAASwD,WAAW,KAAKvD,SAASuD;AAExD,YAAID,YAAY,KAAKpD,cAAc;AACjC,eAAKE,gBAAgB,KAAKF,aAAasD,sBAAqB;AAC5D,cAAME,kBAAkB,KAAKtD,cAAcpF,QAAQ,KAAKoF,cAAcnF;AACtE,cAAMO,iBACJV,KAAA,KAAKiF,SAASwD,aAAS,QAAAzI,OAAA,SAAA,SAAAA,GAAAU,mBAAgB6H,KAAA,KAAKrD,SAASuD,aAAO,QAAA,OAAA,SAAA,SAAA,GAAEI,eAAc;AAC9E,cAAMlI,kBACJmI,KAAA,KAAK7D,SAASwD,aAAS,QAAAK,OAAA,SAAA,SAAAA,GAAAnI,oBAAiBoI,KAAA,KAAK7D,SAASuD,aAAO,QAAA,OAAA,SAAA,SAAA,GAAEO,gBAAe;AAChF,cAAMI,cAAc1I,eAAeC;AAEnC,iBAAOyI,cAAcR,kBAAkB,qBAAqB;QAC7D;AACD,eAAO;MACR;AAED,aAAO,KAAKX,MAAM8G;;AAuOpBrK,IAAAA,SAAY8I,UAAA/C,eAAZ,SAAaxD,GAAmC;AAC9C,UAAM3F,SAASoD,SAAQgG,cAAczD,EAAEuD,QAAQ,CAAC,CAAC;AACjD,UAAMjJ,SAASmD,SAAQgG,cAAczD,EAAEuD,QAAQ,CAAC,CAAC;AACjD,WAAK5E,oBAAoBvE,yBAAyBC,QAAQC,MAAM;AAChE,WAAKsE,oBAAoBnE,yBAAyBJ,QAAQC,MAAM;AAChE,WAAK2I,YAAY5G,UAAUhC,QAAQC,MAAM,CAAC;;AAG5CmD,IAAAA,SAAW8I,UAAA7C,cAAX,SAAY1D,GAAa;AAAzB,UAmBCpC,QAAA;AAlBC,UAAI,CAAC,KAAKoB,cAAc,CAAC,KAAKE,cAAe;AAC7C,UAAM7E,SAASoD,SAAQgG,cAAczD,EAAEuD,QAAQ,CAAC,CAAC;AACjD,UAAMjJ,SAASmD,SAAQgG,cAAczD,EAAEuD,QAAQ,CAAC,CAAC;AACjD,UAAM8E,SAAShM,UAAUhC,QAAQC,MAAM;AACvC,WAAK6I,OAAOkF,MAAM;AAElB,UAAI,KAAKvJ,gBAAiB,MAAKI,cAAckF,qBAAqB,KAAKtF,eAAe;AACtF,WAAKA,kBAAkB,KAAKI,cAAcmF,sBAAsB,WAAA;AAC9D,YAAMiE,WAAWlO,yBAAyBC,QAAQC,MAAM;AACxD,YAAMuJ,UAAUjG,MAAKoD,MAAMlH,QAAQwO,WAAW1K,MAAKe;AACnDf,cAAKmG,WAAWF,SAASwE,QAAQ;UAAErE,sBAAsB;QAAK,CAAE;AAChEpG,cAAKe,oBAAoB2J;AAEzB,YAAMxP,WAAW2B,yBAAyBJ,QAAQC,MAAM;AACxD,YAAM4J,cAActG,MAAKoD,MAAMlI,YAAYA,WAAW8E,MAAKgB;AAC3DhB,cAAKoD,MAAMiD,oBAAoBrG,MAAKoD,MAAMiD,iBAAiBC,WAAW;AACtEtG,cAAKgB,oBAAoB9F;MAC3B,CAAC;;AAyMH2E,IAAAA,SAAA8I,UAAAgC,SAAA,WAAA;AAAA,UAyGC3K,QAAA;;AAxGO,UAAA0D,KAcF,KAAKN,OAbPwH,QAAKlH,GAAAkH,OACLP,QAAK3G,GAAA2G,OACLQ,aAAU,GAAA,YACVC,eAAYpH,GAAAoH,cACZC,YAASrH,GAAAqH,WACT9G,KAAAP,GAAAzG,MAAQd,IAAC8H,GAAA9H,GAAEE,IAAC4H,GAAA5H,GACZnB,WAAQ,GAAA,UACRgB,OAAIwH,GAAAxH,MACJ8O,YAAStH,GAAAsH,WACTC,WAAQvH,GAAAuH,UACRC,sBAAmBxH,GAAAwH,qBACnBhH,KAAAR,GAAAyH,OAASC,iBAAc,GAAA,gBAAEC,gBAAanH,GAAAmH,eAAEC,aAAUpH,GAAAoH,YAClD5G,KAAAhB,GAAA6H,SAAWC,qBAAkB9G,GAAA8G,oBAAEC,oBAAiB/G,GAAA+G,mBAAEC,iBAAc,GAAA;AAGlE,UAAMxB,aAAY/O,KAAA,KAAKsG,MAAME,oBAAkB,QAAAxG,OAAA,SAAAA,KAAA,KAAKqP,aAAY;AAEhE,aAEIvB,oBAAA,OAAA;QAAA9D,aAAa,KAAKA;QAClBK,cAAc,KAAKA;QACnBmG,KAAK,SAACC,IAAAA,IAAE;AAAK,iBAAC5L,MAAKO,eAAeqL;;QAAG,eACzB;QACZT,OAAOC;QACPS,WAAW3M,WAAW,2BAA2BsM,kBAAkB;SAElEZ,QACCkB,oBAAAA,OAAAA,SAAAA;QACEC,KAAI;QACJF,WAAW3M,WACT,uBACAgL,cAAc,aAAa,yBAC3BA,cAAc,sBAAsB,kCACpCA,cAAc,oBAAoB,gCAClCwB,cAAc;SAEXb,YAAmD;QACxDmB,KAAKpB;QACLe,KAAK,KAAKvL;QACV+K,OACKrN,SAAAA,SAAA,CAAA,GAAAwN,UAAU,GAAA;UACbP,WACEA,aAAa,aAAAkB,OAAa9P,GAAQ,MAAA,EAAA8P,OAAA5P,GAAC,aAAA,EAAA,OAAcnB,UAAQ,aAAA,EAAA+Q,OAAc/P,MAAO,GAAA;QAAA,CAAA;QAElFgQ,QAAQ,KAAKjJ;MACb,CAAA,CAAA,IAEFoH,SAEIpB,oBAAA,SAAAnL,SAAA;QAAAqO,UACA;QAAAC,aACA;QAAAC,MAAI;QACJC,OAAO;QACPT,WAAW3M,WACT,uBACAgL,cAAc,aAAa,yBAC3BA,cAAc,sBAAsB,kCACpCA,cAAc,oBAAoB,gCAClCwB,cAAc;SAEZb,YAAU;QACdc,KAAK,KAAKtL;QACVkM,kBAAkB,KAAKtJ;QACvBkI,OACKrN,SAAAA,SAAA,CAAA,GAAAwN,UAAU,GACb;UAAAP,WACEA,aAAa,aAAA,OAAa5O,GAAC,MAAA,EAAA8P,OAAO5P,GAAe,aAAA,EAAA4P,OAAA/Q,UAAsB,aAAA,EAAA+Q,OAAA/P,MAAO,GAAA;QAAA,CAAA;QAElFsQ,UAAU;WAERC,MAAMC,QAAQrC,KAAK,IAAIA,QAAQ,CAAC;QAAE2B,KAAK3B;MAAO,CAAA,GAAGsC,IAAI,SAACC,MAAI;AAAK,eACvD3D,oBAAA,UAAAnL,SAAA;UAAA0K,KAAKoE,KAAKZ;WAASY,IAAI,CAAI;MAD4B,CAEhE,CAAC,GAIP,KAAKnL,MAAMxF,YACV6P,oBAAAA,OAAAA,SAAAA;QACEH,KAAK,KAAKzL;QACViL,OAAKrN,SAAAA,SAAA,CAAA,GACAuN,aAAa,GAChB;UAAAhQ,OAAO6P,sBACH1P,KAAKiC,MAAM,KAAKgE,MAAMxF,SAASZ,KAAK,IACpC,KAAKoG,MAAMxF,SAASZ;UACxBC,QAAQ4P,sBACJ1P,KAAKiC,MAAM,KAAKgE,MAAMxF,SAASX,MAAM,IACrC,KAAKmG,MAAMxF,SAASX;QAAM,CAAA;QAEhCuR,UAAU;QACV3E,WAAW,KAAKA;QAChBQ,SAAS,KAAKA;QAAO,eACT;QACZmD,WAAW3M,WACT,0BACA8L,cAAc,WAAW,+BACzBC,YAAY,8BACZQ,iBAAiB;MAClB,GACGX,YAAY,CAChB,CACH;;AA/yBAjL,IAAAA,SAAAiN,eAAe;MACpB5Q,MAAM;MACNhB,UAAU;MACVD,QAAQ,IAAI;MACZmD,SAASuB;MACTxB,SAASuB;MACTsL,WAAW;MACXd,WAAW;MACXe,UAAU;MACVE,OAAO,CAAA;MACPI,SAAS,CAAA;MACTV,YAAY,CAAA;MACZC,cAAc,CAAA;MACd5D,WAAW;MACXnL,kBAAkB;MAClBgN,gBAAgB;MAChBX,cAAcxI;;AAyVTC,IAAAA,SAAAyF,gBAAgB,SAAClD,GAA+C;AAAK,aAAC;QAC3EjG,GAAG4Q,OAAO3K,EAAE4K,OAAO;QACnB3Q,GAAG0Q,OAAO3K,EAAE6K,OAAO;;;AAGdpN,IAAAA,SAAAgG,gBAAgB,SAACqH,OAA0B;AAAK,aAAC;QACtD/Q,GAAG4Q,OAAOG,MAAMF,OAAO;QACvB3Q,GAAG0Q,OAAOG,MAAMD,OAAO;;;AAmc3B,WAACpN;EAAA,EApzB2BsN,eAAS;;", "names": ["normalizeWheel", "getCropSize", "mediaWidth", "mediaHeight", "containerWidth", "containerHeight", "aspect", "rotation", "_a", "rotateSize", "width", "height", "fittingWidth", "Math", "min", "fittingHeight", "getMediaZoom", "mediaSize", "naturalWidth", "naturalHeight", "restrictPosition", "position", "cropSize", "zoom", "x", "restrictPositionCoord", "y", "maxPosition", "clamp", "getDistanceBetweenPoints", "pointA", "pointB", "sqrt", "pow", "getRotationBetweenPoints", "atan2", "PI", "computeCroppedArea", "crop", "limitAreaFn", "limitArea", "noOp", "mediaBBoxSize", "mediaNaturalBBoxSize", "croppedAreaPercentages", "widthInPixels", "round", "heightInPixels", "isImgWiderThanHigh", "sizePixels", "croppedAreaPixels", "__assign", "max", "value", "_max", "getInitialCropFromCroppedAreaPercentages", "minZoom", "max<PERSON><PERSON>", "getZoomFromCroppedAreaPixels", "mediaZoom", "getInitialCropFromCroppedAreaPixels", "cropZoom", "getCenter", "a", "b", "getRadianAngle", "degreeValue", "rotRad", "abs", "cos", "sin", "classNames", "args", "_i", "arguments", "length", "filter", "join", "trim", "MIN_ZOOM", "MAX_ZOOM", "KEYBOARD_STEP", "C<PERSON>per", "_super", "__extends", "_this", "apply", "cropperRef", "createRef", "imageRef", "videoRef", "containerPosition", "containerRef", "styleRef", "containerRect", "dragStartPosition", "dragStartCrop", "gestureZoomStart", "gestureRotationStart", "isTouching", "lastPinchDistance", "lastPinchRotation", "rafDragTimeout", "rafPinchTimeout", "wheelTimer", "currentDoc", "document", "currentWindow", "window", "resizeObserver", "state", "hasWheelJustStarted", "mediaObjectFit", "undefined", "initResizeObserver", "ResizeObserver", "isFirstResize", "entries", "computeSizes", "observe", "preventZoomSafari", "e", "preventDefault", "cleanEvents", "removeEventListener", "onMouseMove", "onDragStopped", "onTouchMove", "onGestureChange", "onGestureEnd", "onScroll", "clearScrollEvent", "onWheel", "clearTimeout", "onMediaLoad", "emitCropData", "setInitialCrop", "props", "onMediaLoaded", "initialCroppedAreaPercentages", "onCropChange", "onZoomChange", "initialCroppedAreaPixels", "_b", "mediaRef", "current", "getBoundingClientRect", "saveContainerPosition", "containerAspect", "videoWidth", "_c", "_d", "videoHeight", "isMediaScaledDown", "offsetWidth", "offsetHeight", "mediaAspect", "renderedMediaSize", "setMediaSize", "_e", "_f", "onCropSizeChange", "setState", "recomputeCropPosition", "setCropSize", "bounds", "left", "top", "onMouseDown", "addEventListener", "onDragStart", "getMousePoint", "onDrag", "onTouchStart", "onTouchRequest", "passive", "touches", "onPinchStart", "getTouchPoint", "onPinchMove", "onGestureStart", "point", "newZoom", "scale", "setNewZoom", "shouldUpdatePosition", "onRotationChange", "newRotation", "onInteractionStart", "cancelAnimationFrame", "requestAnimationFrame", "offsetX", "offsetY", "requestedPosition", "newPosition", "onInteractionEnd", "onWheelRequest", "pixelY", "normalizeWheel", "zoomSpeed", "call", "setTimeout", "getPointOnContainer", "containerTopLeft", "Error", "getPointOnMedia", "zoomPoint", "zoomTarget", "getCropData", "restrictedPosition", "getAspect", "cropData", "onCropComplete", "onCropAreaChange", "emitCropAreaChange", "onKeyDown", "event", "keyboardStep", "step", "shift<PERSON>ey", "newCrop", "key", "repeat", "onKeyUp", "prototype", "componentDidMount", "ownerDocument", "defaultView", "zoomWithScroll", "disableAutomaticStylesInjection", "createElement", "setAttribute", "nonce", "innerHTML", "cssStyles", "head", "append<PERSON><PERSON><PERSON>", "complete", "setImageRef", "setVideoRef", "setCropperRef", "componentWillUnmount", "disconnect", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "componentDidUpdate", "prevProps", "objectFit", "_g", "_h", "video", "_j", "load", "getObjectFit", "center", "distance", "render", "image", "mediaProps", "cropperProps", "transform", "cropShape", "showGrid", "roundCropAreaPixels", "style", "containerStyle", "cropAreaStyle", "mediaStyle", "classes", "containerClassName", "cropAreaClassName", "mediaClassName", "ref", "el", "className", "React", "alt", "src", "concat", "onLoad", "autoPlay", "playsInline", "loop", "muted", "onLoadedMetadata", "controls", "Array", "isArray", "map", "item", "tabIndex", "defaultProps", "Number", "clientX", "clientY", "touch", "Component"]}