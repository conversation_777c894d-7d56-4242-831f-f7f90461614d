import logo from '@/assets/global/logo.png';
import { useAuth } from '@/contexts/AuthContext';
import { useAccessControl } from '@/hooks/useAccessControl';
import { markFooterLinksAccessibility, getCategoryIdFromPath } from '@/utils/footerUtils';
import { useRestrictedNavigation } from '@/hooks/useRestrictedNavigation';
import { UpgradeDialog } from '@/components/common/UpgradeDialog';
import { Lock } from 'lucide-react';

const footerLinks = [
  {
    heading: "Home Instructions",
    links: [
      { label: "Home Location", href: "/category/homeinstructions/homelocation" },
      { label: "Pets", href: "/category/homeinstructions/pets" },
      { label: "Trash", href: "/category/homeinstructions/trash" },
      { label: "Security", href: "/category/homeinstructions/security" },
      { label: "Others", href: "/category/homeinstructions/other", badge: "New" },
    ],
  },
  {
    heading: "Home Documents",
    links: [
      { label: "Utility", href: "/category/homedocuments/utility" },
      { label: "Gas", href: "/category/homedocuments/gas" },
      { label: "Water", href: "/category/homedocuments/water" },
      { label: "Trash", href: "/category/homedocuments/trashcollection" },
      { label: "HVAC", href: "/category/homedocuments/hvac" },
      { label: "Pest", href: "/category/homedocuments/pest" },
      { label: "Lawn", href: "/category/homedocuments/lawn" },
      { label: "Cable", href: "/category/homedocuments/cable" },
      { label: "Internet", href: "/category/homedocuments/internet" }
    ],
  },
  {
    heading: "Funeral Arrangements",
    links: [
      { label: "Details", href: "/category/funeralarrangements/details" },
      { label: "Ceremony Location", href: "/category/funeralarrangements/ceremonylocation" },
      { label: "Clergy", href: "/category/funeralarrangements/clergy" },
      { label: "Notifications", href: "/category/funeralarrangements/notification" },
      { label: "Proceedings", href: "/category/funeralarrangements/proceedings" }
    ],
  },
  {
    heading: "Contacts",
    links: [
      { label: "Friends/Family", href: "/category/importantcontacts/friendsfamily" },
      { label: "Work", href: "/category/importantcontacts/work" },
      { label: "Religion", href: "/category/importantcontacts/religiousaffiliation" },
      { label: "Clubs", href: "/category/importantcontacts/clubs" },
    ],
  },
  {
    heading: "Will & Testament",
    links: [
      { label: "Location", href: "/category/willinstructions/location" },
      { label: "Legal Representation", href: "/category/willinstructions/legal" },
    ],
  },
  {
    heading: "Social Media and Phone",
    links: [
      { label: "Email", href: "/category/socialmedia/email" },
      { label: "Facebook", href: "/category/socialmedia/facebook" },
      { label: "Instagram", href: "/category/socialmedia/instagram" },
      { label: "Other Socials", href: "/category/socialmedia/otheraccounts" },
      { label: "Cell Phone", href: "/category/socialmedia/cellphone" },
    ],
  },
];

const Footer = () => {
  const { isAuthenticated } = useAuth();
  const { canAccessCategory } = useAccessControl();
  const { 
    handleNavigation, 
    handleDisabledClick,
    handleUpgradeClick, 
    upgradeDialogOpen, 
    setUpgradeDialogOpen, 
    userAccess 
  } = useRestrictedNavigation();

  // Mark footer links as accessible/disabled based on user permissions
  const accessibleFooterLinks = isAuthenticated 
    ? markFooterLinksAccessibility(footerLinks, canAccessCategory)
    : footerLinks.map(section => ({
        ...section,
        links: section.links.map(link => ({ ...link, disabled: false }))
      }));

  return (
    <>
      <div className="bg-white border-t">
        <div className="max-w-7xl mx-auto px-4 py-12">
          <div className="grid grid-cols-6 gap-8">
            <div>
              <h4 className="text-sm font-semibold text-gray-500 uppercase mb-4">retail</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-800 hover:underline">Overview</a></li>
                <li><a href="#" className="text-gray-800 hover:underline">Features</a></li>
                <li className="flex items-center gap-2">
                  <a href="#" className="text-gray-800 hover:underline">Solutions</a>
                  <span className="text-xs bg-green-100 text-green-600 px-2 py-0.5 rounded-full">New</span>
                </li>
                <li><a href="#" className="text-gray-800 hover:underline">Tutorials</a></li>
                <li><a href="#" className="text-gray-800 hover:underline">Pricing</a></li>
                <li><a href="#" className="text-gray-800 hover:underline">Releases</a></li>
              </ul>
            </div>
            {accessibleFooterLinks.map((section, idx) => (
              <div key={idx}>
                <h4 className="text-xs font-semibold text-gray-700 uppercase mb-4">{section.heading}</h4>
                <ul className="space-y-2">
                  {section.links.map((link, linkIdx) => (
                    <li key={linkIdx} className="flex items-center gap-2">
                      <a
                        href={link.href}
                        className={`text-xs transition-all ${
                          link.disabled 
                            ? 'text-gray-400 cursor-pointer opacity-50 hover:opacity-70' 
                            : 'text-gray-800 hover:underline'
                        }`}
                        onClick={e => link.disabled ? handleDisabledClick(e, link.href) : handleNavigation(e, link.href)}
                      >
                        {link.label}
                      </a>
                      {link.badge && (
                        <span className="text-xs bg-green-100 text-green-600 px-2 py-0.5 rounded-full">
                          {link.badge}
                        </span>
                      )}
                      {link.disabled && (
                        <Lock className="w-3 h-3 text-gray-400" />
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        <div className="border-t">
          <div className="max-w-7xl mx-auto px-4 py-6 flex flex-col sm:flex-row justify-between items-center">
            <img src={logo} alt="Heirkey Logo" className="h-10 w-auto mb-4 sm:mb-0" />
            <p className="text-sm text-gray-500 text-center sm:text-right">
              © 2025 Heirkey. All rights reserved.
            </p>
          </div>
        </div>
      </div>

      {/* Upgrade Dialog */}
      <UpgradeDialog
        open={upgradeDialogOpen}
        onOpenChange={setUpgradeDialogOpen}
        onUpgradeClick={handleUpgradeClick}
        subscriptionType={userAccess?.subscriptionType}
      />
    </>
  );
};

export default Footer;
