import { useField, <PERSON><PERSON><PERSON>y, useFormikContext } from 'formik';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Field } from 'formik';

export interface BaseQuestion {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  isAnswered?: boolean;
  answer?: any;
  dependsOn?: {
    questionId: string;
    value: string;
  };
  validationRules?: {
    minLength?: number;
    maxLength?: number;
  };
}

export interface TextQuestion extends BaseQuestion {
  type: 'text' | 'password';
  placeholder?: string;
}

export interface NumberQuestion extends BaseQuestion {
  type: 'number';
  placeholder?: string;
}

export interface BooleanQuestion extends BaseQuestion {
  type: 'boolean';
}

export interface ChoiceQuestion extends BaseQuestion {
  type: 'choice' | 'dropdown';
  options: string[];
}

export interface TextareaQuestion extends BaseQuestion {
  type: 'textarea';
  placeholder?: string;
}

export interface GroupQuestion extends BaseQuestion {
  type: 'group';
  fields: Array<{
    id: string;
    type: string;
    options?: string[];
  }>;
}

export type Question = TextQuestion | NumberQuestion | BooleanQuestion | ChoiceQuestion | TextareaQuestion | GroupQuestion;

export const TextField = ({ question }: { question: TextQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  // console.log('TextField', question.id, field, meta, helpers);
  // console.log('TextField props', question.id, { ...field, ...meta, ...helpers });
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Input
        id={question.id}
        type={question.type === 'password' ? 'password' : 'text'}
        placeholder={question.placeholder}
        {...field}
        disabled={false}
        className={`w-full text-black ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const TextareaField = ({ question }: { question: TextareaQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    helpers.setValue(e.target.value);
    helpers.setTouched(true);
  };
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Textarea
        id={question.id}
        placeholder={question.placeholder}
        {...field}
        onChange={handleChange}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const NumberField = ({ question }: { question: NumberQuestion }) => {
  const [field, meta] = useField(question.id);
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Input
        id={question.id}
        type="number"
        placeholder={question.placeholder}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const BooleanField = ({ question }: { question: BooleanQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  return (
    <div className="mb-6">
      <div className="flex flex-col">
        <Label className="font-medium mb-2" htmlFor={question.id}>
          {question.text}
        </Label>
        <div className="flex gap-2">
          <Button
            type="button"
            onClick={() => helpers.setValue('yes')}
            className={field.value === 'yes'
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5]'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          >
            Yes
          </Button>
          <Button
            type="button"
            onClick={() => helpers.setValue('no')}
            className={field.value === 'no'
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5]'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          >
            No
          </Button>
        </div>
        {meta.touched && meta.error ? (
          <div className="text-red-500 text-sm mt-1">{meta.error}</div>
        ) : null}
      </div>
    </div>
  );
};

export const ChoiceField = ({ question }: { question: ChoiceQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium">
        {question.text}
      </Label>
      <Select
        value={field.value || ''}
        onValueChange={(value) => {
          helpers.setValue(value);
          helpers.setTouched(true);
        }}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          {question.options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const OtherSocialMediaAccountsField = ({ groupQuestion }: { groupQuestion: GroupQuestion }) => {
  const { values, setFieldValue } = useFormikContext<any>();
  const accounts = Array.isArray(values.otherSocialMediaAccounts) ? values.otherSocialMediaAccounts : [];

  return (
    <FieldArray name="otherSocialMediaAccounts">
      {({ push, remove }) => (
        <div className="mb-6">
          <Label className="block mb-2 font-medium">
            {groupQuestion.text}
          </Label>
          {accounts.map((account: any, idx: number) => (
            <div key={idx} className="mb-4 border p-4 rounded-lg">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label className="block text-xs font-medium mb-1">Service</Label>
                  <Select
                    value={account.service}
                    onValueChange={(value) => setFieldValue(`otherSocialMediaAccounts[${idx}].service`, value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      {groupQuestion.fields[0].options?.map((opt: string) => (
                        <SelectItem key={opt} value={opt}>{opt}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="block text-xs font-medium mb-1">Login ID</Label>
                  <Input
                    value={account.loginId}
                    onChange={(e) => setFieldValue(`otherSocialMediaAccounts[${idx}].loginId`, e.target.value)}
                    placeholder="Enter login ID"
                  />
                </div>
                <div>
                  <Label className="block text-xs font-medium mb-1">Password</Label>
                  <Input
                    type="password"
                    value={account.password}
                    onChange={(e) => setFieldValue(`otherSocialMediaAccounts[${idx}].password`, e.target.value)}
                    placeholder="Enter password"
                  />
                </div>
              </div>
              <Button
                type="button"
                onClick={() => remove(idx)}
                variant="destructive"
                className="mt-2"
              >
                Remove
              </Button>
            </div>
          ))}
          <Button
            type="button"
            onClick={() => push({ service: '', loginId: '', password: '' })}
            className="bg-[#2BCFD5] hover:bg-[#19bbb5] text-white mt-2"
          >
            Add another account
          </Button>
        </div>
      )}
    </FieldArray>
  );
};

// Main component for rendering questions based on condition
export const QuestionItem = ({
  question,
  values
}: {
  question: Question;
  values: Record<string, any>;
}) => {
  // Check if this question should be shown based on dependencies
  const shouldShow = !question.dependsOn ||
    (values[question.dependsOn.questionId]?.toString().toLowerCase() === question.dependsOn.value.toLowerCase());

  // If question depends on something and condition is not met, render ghosted version
  const isGhosted = question.dependsOn &&
    values[question.dependsOn.questionId]?.toString().toLowerCase() !== question.dependsOn.value.toLowerCase();

  if (!shouldShow && isGhosted) {
    return (
      <div className="mb-6 opacity-50 pointer-events-none">
        {renderQuestion(question)}
      </div>
    );
  } else if (!shouldShow) {
    return null;
  }

  return renderQuestion(question);
};

const renderQuestion = (question: Question) => {
  switch (question.type) {
    case 'text':
    case 'password':
      return <TextField question={question as TextQuestion} />;
    case 'textarea':
      return <TextareaField question={question as TextareaQuestion} />;
    case 'number':
      return <NumberField question={question as NumberQuestion} />;
    case 'boolean':
      return <BooleanField question={question as BooleanQuestion} />;
    case 'choice':
    case 'dropdown':
      return <ChoiceField question={question as ChoiceQuestion} />;
    case 'group':
      return <OtherSocialMediaAccountsField groupQuestion={question as GroupQuestion} />;
    default:
      return null;
  }
};

// Utility functions for handling forms
export const buildValidationSchema = (questions: Question[], Yup: any) => {
  const schemaShape: Record<string, any> = {};

  questions.forEach(question => {
    // Removed required field validation - all fields are now optional
    let schema = Yup.string();

    if (question.type === 'number') {
      schema = Yup.number().typeError('Must be a number');
    } else if (question.type === 'boolean') {
      schema = Yup.string().oneOf(['yes', 'no'], 'Select Yes or No');
    } else if (question.type === 'password') {
      schema = schema.min(8, 'Password must be at least 8 characters');
    }

    if (question.validationRules) {
      if (question.validationRules.minLength) {
        schema = schema.min(question.validationRules.minLength, `Minimum ${question.validationRules.minLength} characters`);
      }
      if (question.validationRules.maxLength) {
        schema = schema.max(question.validationRules.maxLength, `Maximum ${question.validationRules.maxLength} characters`);
      }
    }

    schemaShape[question.id] = schema.notRequired();
  });

  return Yup.object().shape(schemaShape);
};

export const generateInitialValues = (questions: Question[]) => {
  const initialValues: Record<string, any> = {};
  questions.forEach(question => {
    initialValues[question.id] = '';
  });
  return initialValues;
};

export const handleDependentAnswers = (
  values: Record<string, any>,
  questions: Question[],
  setValues: (values: Record<string, any>) => void
) => {
  const updatedValues = { ...values };
  let hasChanges = false;

  questions.forEach(question => {
    if (question.dependsOn) {
      const dependentValue = values[question.dependsOn.questionId];
      // Generalized comparison for any dependency value
      const shouldBeEmpty = dependentValue?.toString().toLowerCase() !== question.dependsOn.value.toLowerCase();
      if (shouldBeEmpty && updatedValues[question.id]) {
        updatedValues[question.id] = '';
        hasChanges = true;
      }
    }
  });

  if (hasChanges) {
    setValues(updatedValues);
  }
};

export const calculateProgress = (questions: Question[], values: Record<string, any>) => {
  // Since all fields are now optional, calculate progress based on all visible questions
  const visibleQuestions = questions.filter(q => {
    if (q.dependsOn) {
      const dependentValue = values[q.dependsOn.questionId];
      return dependentValue?.toString().toLowerCase() === q.dependsOn.value.toLowerCase();
    }
    return true;
  });

  const answeredQuestions = visibleQuestions.filter(q => {
    return values[q.id] !== undefined && values[q.id] !== '';
  }).length;

  return visibleQuestions.length > 0 ? (answeredQuestions / visibleQuestions.length) * 100 : 100;
};


