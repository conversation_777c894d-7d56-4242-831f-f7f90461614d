import { useField } from 'formik';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import * as Yup from 'yup';

export interface BaseQuestion {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  isAnswered?: boolean;
  answer?: any;
  dependsOn?: {
    questionId: string;
    value: string;
  };
}

export interface TextQuestion extends BaseQuestion {
  type: 'text' | 'email' | 'password';
}

export interface TextareaQuestion extends BaseQuestion {
  type: 'textarea';
}

export interface BooleanQuestion extends BaseQuestion {
  type: 'boolean';
}

export interface DropdownQuestion extends BaseQuestion {
  type: 'dropdown';
  options: string[];
}

export type Question = TextQuestion | TextareaQuestion | BooleanQuestion | DropdownQuestion;

// Form field components
const TextField = ({ question }: { question: TextQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="space-y-2">
      <Label htmlFor={question.id} className="text-sm font-medium text-gray-700">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Input
        {...field}
        id={question.id}
        type={question.type}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
        placeholder={`Enter ${question.text.toLowerCase()}`}
      />
      {meta.touched && meta.error && (
        <div className="text-red-500 text-sm">{meta.error}</div>
      )}
    </div>
  );
};

const TextareaField = ({ question }: { question: TextareaQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="space-y-2">
      <Label htmlFor={question.id} className="text-sm font-medium text-gray-700">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Textarea
        {...field}
        id={question.id}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
        placeholder={`Enter ${question.text.toLowerCase()}`}
        rows={4}
      />
      {meta.touched && meta.error && (
        <div className="text-red-500 text-sm">{meta.error}</div>
      )}
    </div>
  );
};

const BooleanField = ({ question }: { question: BooleanQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium text-gray-700">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <div className="flex flex-row space-x-4">
        <Button
          type="button"
          variant={field.value === 'yes' ? 'default' : 'outline'}
          onClick={() => helpers.setValue('yes')}
          className={`px-6 py-2 ${
            field.value === 'yes'
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5] text-white'
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          Yes
        </Button>
        <Button
          type="button"
          variant={field.value === 'no' ? 'default' : 'outline'}
          onClick={() => helpers.setValue('no')}
          className={`px-6 py-2 ${
            field.value === 'no'
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5] text-white'
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          No
        </Button>
      </div>
      {meta.touched && meta.error && (
        <div className="text-red-500 text-sm">{meta.error}</div>
      )}
    </div>
  );
};

const DropdownField = ({ question }: { question: DropdownQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  
  return (
    <div className="space-y-2">
      <Label htmlFor={question.id} className="text-sm font-medium text-gray-700">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Select
        value={field.value}
        onValueChange={(value) => helpers.setValue(value)}
      >
        <SelectTrigger className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}>
          <SelectValue placeholder={`Select ${question.text.toLowerCase()}`} />
        </SelectTrigger>
        <SelectContent>
          {question.options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {meta.touched && meta.error && (
        <div className="text-red-500 text-sm">{meta.error}</div>
      )}
    </div>
  );
};

// Helper function to check if a question should be visible based on dependencies
export const isQuestionVisible = (question: Question, values: Record<string, any>): boolean => {
  if (!question.dependsOn) return true;
  
  const dependentValue = values[question.dependsOn.questionId];
  if (!dependentValue) return false;
  
  // Check if the dependent value matches the required value
  return dependentValue === question.dependsOn.value;
};

// Helper function to handle dependent answers
export const handleDependentAnswers = (
  questionId: string,
  value: any,
  questions: Question[],
  setFieldValue: (field: string, value: any) => void
) => {
  // Find all questions that depend on this question
  const dependentQuestions = questions.filter(q => q.dependsOn?.questionId === questionId);
  
  // If the answer doesn't match the required value, clear dependent answers
  dependentQuestions.forEach(depQ => {
    if (value !== depQ.dependsOn?.value) {
      setFieldValue(depQ.id, '');
    }
  });
};

// Main QuestionItem component
export const QuestionItem = ({ question, values }: { question: Question; values: Record<string, any> }) => {
  if (!isQuestionVisible(question, values)) {
    return null;
  }

  const renderField = () => {
    switch (question.type) {
      case 'boolean':
        return <BooleanField question={question as BooleanQuestion} />;
      case 'dropdown':
        return <DropdownField question={question as DropdownQuestion} />;
      case 'textarea':
        return <TextareaField question={question as TextareaQuestion} />;
      case 'text':
      case 'email':
      case 'password':
      default:
        return <TextField question={question as TextQuestion} />;
    }
  };

  return renderField();
};

// Helper functions
export const buildValidationSchema = (questions: Question[]) => {
  const schema: Record<string, any> = {};

  questions.forEach((question) => {
    // For subscription information, all fields are optional
    schema[question.id] = Yup.string().nullable();
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: Question[], existingValues?: Record<string, any>) => {
  const initialValues: Record<string, any> = {};

  questions.forEach((question) => {
    initialValues[question.id] = existingValues?.[question.id] || '';
  });

  return initialValues;
};

export const calculateProgress = (questions: Question[], values: Record<string, any>) => {
  // Special handling for OtherServices (section 1104A)
  const isOtherServices = questions.some(q => q.sectionId === '1104A');

  if (isOtherServices) {
    // For OtherServices, progress is based on the services array stored in values.services
    const services = values.services || [];

    if (!Array.isArray(services) || services.length === 0) {
      // No services selected - only the dropdown question counts
      return {
        totalQuestions: 1,
        answeredQuestions: 0,
        completionPercentage: 0
      };
    }

    // Count services with complete account information
    let totalAccounts = 0;
    let completedAccounts = 0;

    services.forEach((service: any) => {
      if (service.accounts && Array.isArray(service.accounts)) {
        service.accounts.forEach((account: any) => {
          totalAccounts++;
          if (account.username && account.password) {
            completedAccounts++;
          }
        });
      }
    });

    // If no accounts, consider just having services as partial progress
    if (totalAccounts === 0) {
      return {
        totalQuestions: 1,
        answeredQuestions: services.length > 0 ? 1 : 0,
        completionPercentage: services.length > 0 ? 50 : 0 // 50% for having services but no credentials
      };
    }

    return {
      totalQuestions: totalAccounts,
      answeredQuestions: completedAccounts,
      completionPercentage: Math.round((completedAccounts / totalAccounts) * 100)
    };
  }

  // Original logic for other sections
  const relevantQuestions = questions.filter(q => {
    // For dependent questions, check if they should be included based on the parent's value
    if (q.dependsOn) {
      const parentValue = values[q.dependsOn.questionId];
      // Only include if parent question is answered and matches the required value
      return parentValue === q.dependsOn.value;
    }

    // Include all non-dependent questions
    return true;
  });

  const totalQuestions = relevantQuestions.length;
  const answeredQuestions = Object.entries(values).filter(([id, value]) => {
    const question = relevantQuestions.find(q => q.id === id);
    // Only count answered questions that are relevant
    return question && value !== '';
  }).length;

  const completionPercentage = totalQuestions > 0
    ? Math.round((answeredQuestions / totalQuestions) * 100)
    : 0;

  return {
    totalQuestions,
    answeredQuestions,
    completionPercentage
  };
};
