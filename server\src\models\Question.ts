import mongoose, { Schema, Types, Document } from 'mongoose';
import { IAnswer, IAnswerBySection, IQuestion } from '../types/Question';

// Answer schema
const AnswerSchema = new Schema<IAnswer>({
  index: { type: Number, required: true, default: 0 },
  questionId: { type: Schema.Types.ObjectId, default: () => new Types.ObjectId() },
  question: { type: String, required: true, default: '' },
  type: {
    type: String,
    enum: ['boolean', 'text', 'number', 'choice', 'date'],
    default: 'text',
    required: true
  }
});

// Section schema
const AnswerBySectionSchema = new Schema<IAnswerBySection>({
  sectionId: {  type: Schema.Types.ObjectId, default: () => new Types.ObjectId()  },
  isCompleted: { type: Boolean, default: false },
  answers: { type: [AnswerSchema], default: [] }
});

// Main schema
const QuestionSchema = new Schema<IQuestion>({
  subCategoryId: { type: Schema.Types.ObjectId, ref: 'SubCategory' },
  categoryId: { type: Schema.Types.ObjectId, ref: 'Category' },
  answersBySection: { type: [AnswerBySectionSchema], default: [] }
}, {
  timestamps: true
});

// Export model
export const Question = mongoose.model<IQuestion>('Question', QuestionSchema);
