import React from 'react';
import { Field, ErrorMessage } from 'formik';

export interface Question {
  id: string;
  text: string;
  type: 'text' | 'choice' | 'boolean' | 'dropdown' | 'currency' | 'password';
  required: boolean;
  sectionId: string;
  options?: string[];
  order: number;
  dependsOn?: {
    questionId: string;
    value: string;
  };
}

export const buildValidationSchema = (questions: Question[], Yup: any) => {
  const schema: Record<string, any> = {};

  questions.forEach(question => {
    let fieldSchema;

    switch (question.type) {
      case 'text':
      case 'currency':
      case 'password':
        fieldSchema = Yup.string();
        break;
      case 'choice':
      case 'dropdown':
        fieldSchema = Yup.string();
        break;
      case 'boolean':
        fieldSchema = Yup.string();
        break;
      default:
        fieldSchema = Yup.string();
    }

    // All fields are optional for mobile
    schema[question.id] = fieldSchema;
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: Question[]) => {
  const values: Record<string, any> = {};
  questions.forEach(question => {
    values[question.id] = '';
  });
  return values;
};

export const calculateProgress = (questions: Question[], values: Record<string, any>) => {
  const totalQuestions = questions.length;
  const answeredQuestions = Object.values(values).filter(value => value !== '').length;
  const completionPercentage = Math.round((answeredQuestions / totalQuestions) * 100);

  return {
    totalQuestions,
    answeredQuestions,
    completionPercentage
  };
};

export const handleDependentAnswers = (
  values: Record<string, any>,
  questions: Question[],
  setValues: (values: Record<string, any>) => void
) => {
  let needsUpdate = false;
  const newValues = { ...values };

  questions.forEach(question => {
    if (question.dependsOn) {
      const parentValue = values[question.dependsOn.questionId];
      const shouldClear = parentValue !== question.dependsOn.value && values[question.id] !== '';

      if (shouldClear) {
        newValues[question.id] = '';
        needsUpdate = true;
      }
    }
  });

  if (needsUpdate) {
    setValues(newValues);
  }
};

// Clean up dependent answers before saving - returns cleaned values
export const cleanDependentAnswers = (
  values: Record<string, any>,
  questions: Question[]
): Record<string, any> => {
  const cleanedValues = { ...values };

  questions.forEach((question) => {
    if (question.dependsOn) {
      const { questionId, value } = question.dependsOn;
      if (values[questionId] !== value) {
        cleanedValues[question.id] = '';
      }
    }
  });

  return cleanedValues;
};

interface QuestionItemProps {
  question: Question;
  values: Record<string, any>;
}

export const QuestionItem: React.FC<QuestionItemProps> = ({ question, values }) => {
  const shouldShow = !question.dependsOn ||
    (values[question.dependsOn.questionId]?.toString().toLowerCase() === question.dependsOn.value.toLowerCase());

  if (!shouldShow) {
    return null;
  }

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </label>

      {(question.type === 'text' || question.type === 'currency') && (
        <Field
          name={question.id}
          type={question.type === 'currency' ? 'number' : 'text'}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
          placeholder={question.type === 'currency' ? 'Enter amount' : ''}
        />
      )}

      {question.type === 'password' && (
        <Field
          name={question.id}
          type="password"
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
        />
      )}

      {(question.type === 'choice' || question.type === 'dropdown') && question.options && (
        <Field as="select" name={question.id} className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent">
          <option value="">Select...</option>
          {question.options.map((option: string) => (
            <option key={option} value={option}>{option}</option>
          ))}
        </Field>
      )}

      {question.type === 'boolean' && (
        <div className="flex gap-2">
          {['Yes', 'No'].map((option: string) => (
            <label
              key={option}
              className={`flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer transition-all ${
                values[question.id] === option.toLowerCase()
                  ? 'bg-[#2BCFD5] text-white border-[#2BCFD5]'
                  : 'bg-gray-50 text-gray-800 border-gray-200 hover:bg-[#25b6bb] hover:text-white'
              }`}
            >
              <Field
                type="radio"
                name={question.id}
                value={option.toLowerCase()}
                className="hidden"
              />
              {option}
            </label>
          ))}
        </div>
      )}
      <ErrorMessage name={question.id} component="div" className="text-red-500 text-sm mt-1" />
    </div>
  );
};
