import Stripe from "stripe"
import { NextFunction, Request, Response } from "express"
import PricingPlan from "../models/PricingPlan"
import dotenv from "dotenv"
dotenv.config();

import SubscribedPlan from '../models/SubscribedPlan';
import Owner from '../models/Owner';
import User from '../models/User';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string)

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET as string


interface RawRequest extends Request {
    body: Buffer;
}
function getFutureDate(days = 30) {
    const now = new Date();
    now.setDate(now.getDate() + days); // add 30 days

    // Format to `YYYY-MM-DD HH:mm:ss.sss+00`
    const iso = now.toISOString(); // e.g. 2025-07-30T13:13:03.775Z

    // Convert to desired format
    const [date, time] = iso.split('T');
    const formattedTime = time.replace('Z', '+00');
    return `${date} ${formattedTime}`;
}




export const SubscribeStripe = async (req: Request, res: Response, next: NextFunction) => {
    const plan = req.query.plan;
    const ownerId = req.query.ownerId;
    if (!plan) {
        return res.send('Subscription plan not found')
    }
    const findFirst = await PricingPlan.findOne({ type: plan })
    if (!findFirst) {
        return res.status(404).send('Subscription plan not found');
    }


    // Handle paid plans with Stripe checkout
    const priceId = findFirst.priceIdStripe;
    if (!priceId) {
        return res.status(400).json({ error: 'Stripe price ID not configured for this plan' });
    }

    try {
        const session = await stripe.checkout.sessions.create({
            mode: 'subscription',
            line_items: [
                { price: priceId, quantity: 1 }
            ],
            success_url: `${process.env.FRONTEND_URL}/success?session_id={CHECKOUT_SESSION_ID}&plan=${plan}`,
            cancel_url: process.env.FRONTEND_URL as string,
            metadata: {
                ownerId: ownerId ? String(ownerId) : '',
                planId: String(findFirst._id),
                planType: findFirst.type
            }
        })
        if (!session.url) {
            return res.status(500).send('Failed to create checkout session')
        }
        res.json(session)
    } catch (error: any) {
        // Stripe error handling
        const message = error?.message || 'An error occurred while creating the checkout session.'
        return res.status(400).json({ error: message })
    }
}


export const successPayment = async (req: Request, res: Response) => {

    try {

        const { session_id } = req.query

        if (!session_id || typeof session_id !== 'string') {
            return res.status(400).json('Session ID is required')
        }

        // Handle free plan sessions
        if (session_id === 'free_plan') {
            const { plan } = req.query;
            if (!plan) {
                return res.status(400).json('Plan type is required for free plan verification');
            }

            // For free plans, the subscription was already created in SubscribeStripe
            // Just return success response
            res.json({
                id: 'free_plan_session',
                payment_status: 'paid',
                metadata: {
                    planType: plan
                }
            });
            return;
        }

        const session = await stripe.checkout.sessions.retrieve(session_id, { expand: ['subscription'] })


        res.send(session)
    } catch (error) {
        console.error('Error in successPayment:', error);
        res.status(500).json({ error: 'Failed to process payment success' });
    }


}


export const customerDetails = async (req: Request, res: Response) => {
    try {
        const portalSession = await stripe.billingPortal.sessions.create({
            customer: req.params.customerId,
            return_url: `${process.env.FRONTEND_URL}`
        })

        res.redirect(portalSession.url)
    } catch (error) {
        console.error('Error creating customer portal session:', error);
        res.status(500).json({ error: 'Failed to create customer portal session' });
    }
}



export const stripeWebhooks = async (req: Request, res: Response) => {
    console.log('Webhook received:', req.headers['stripe-signature'] ? 'Signature present' : 'No signature');
    
    const sig = req.headers['stripe-signature']

    let event: Stripe.Event;

    try {
        // Use raw body for signature verification
        const rawBody = (req as any).rawBody || req.body;
        console.log('Raw body type:', typeof rawBody);
        console.log('Raw body length:', rawBody?.length || 'undefined');

        event = stripe.webhooks.constructEvent(rawBody, sig as string, endpointSecret as string)
        console.log('Webhook signature verified successfully');
        console.log('Event type:', event.type);

    } catch (error) {
        console.log(`Webhook signature verification failed`, error instanceof Error ? error.message : 'Unknown error')
        return res.status(400).json({ error: 'Webhook signature verification failed' });
    }

    switch (event.type) {
        case 'checkout.session.completed': {
            const session = event.data.object as Stripe.Checkout.Session;

            console.log('Processing checkout.session.completed webhook');
            console.log('Session metadata:', session.metadata);
            console.log('Session ID:', session.id);
            console.log('Payment status:', session.payment_status);
            
            // Create subscription in DB using metadata
            const metadata = session.metadata || {};
            const ownerId = metadata.ownerId;
            const planId = metadata.planId;
            console.log('OwnerId from metadata:', ownerId);
            console.log('PlanId from metadata:', planId);
            
            if (ownerId && planId) {
                try {
                    // Check if subscription already exists
                    const existing = await SubscribedPlan.findOne({ ownerId });
                    console.log('Existing subscription found:', existing);
                    
                    if (!existing) {
                        console.log('Creating new subscription...');
                        const subscription = new SubscribedPlan({
                            planId,
                            ownerId,
                            currentPlan: planId,
                            previousPlans: []
                        });
                        await subscription.save();
                        console.log('New subscription saved:', subscription._id);
                        
                        const ownerUpdate = await Owner.findByIdAndUpdate(ownerId, { subscribedPlanId: subscription._id });
                        console.log('Owner updated with subscription ID:', subscription._id);
                        console.log('Owner update result:', ownerUpdate ? 'Success' : 'Failed');
                        
                        // --- NEW LOGIC: Update user subscriptionType to match plan ---
                        const plan = await PricingPlan.findById(planId);
                        if (plan) {
                            const userUpdate = await User.updateMany(
                                { ownerId: ownerId },
                                {
                                    subscriptionType: plan.type,
                                    allowedCategoryId: null // Reset allowed category for new subscription type
                                }
                            );
                            console.log('Users updated:', userUpdate.modifiedCount, 'users modified');
                        } else {
                            console.log('Plan not found for ID:', planId);
                        }
                        // --- END NEW LOGIC ---
                    } else {
                        console.log('Owner already has a subscription:', ownerId);
                    }
                } catch (err) {
                    console.error('Error creating subscription in webhook:', err);
                    console.error('Error details:', err instanceof Error ? err.message : 'Unknown error');
                    console.error('Error stack:', err instanceof Error ? err.stack : 'No stack trace');
                }
            } else {
                console.log('Missing ownerId or planId in metadata');
                console.log('ownerId:', ownerId);
                console.log('planId:', planId);
            }
            break;
        }
        default:
            console.log(`Unhandled event type: ${event.type}`);
    }

    res.status(200).json({ received: true });
}
