import { Button } from "@/components/ui/button";
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@/components/ui/toggle-group";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { fetchPricingPlans } from "@/store/slices/subscriptionSlice";
import { PricingPlan } from "@/types/subscription";
import subscriptionService from '@/services/subscriptionService';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import StripeCheckout from './StripeCheckout';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

export default function SubscribePage() {
  const [billing, setBilling] = useState("monthly");
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
  const [showCheckout, setShowCheckout] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { plans, loading, error } = useSelector((state: RootState) => state.subscription);
  const { user } = useAuth();
  const [ownerId, setOwnerId] = useState<string | null>(null);
  const [ownerLoading, setOwnerLoading] = useState(true);
  const [checkingSubscription, setCheckingSubscription] = useState(false);

  // Map backend plans to include billingCycle
  const mappedPlans = plans.map(plan => ({
    ...plan,
    billingCycle:
      plan.duration === 1 ? "monthly" :
      plan.duration === 12 ? "annual" :
      "other"
  }));

  const filteredPlans = mappedPlans.filter(plan => plan.billingCycle === billing || plan.billingCycle === "other");

  useEffect(() => {
    dispatch(fetchPricingPlans());
    if (user?.id) {
      setOwnerLoading(true);
      getCachedOwnerIdFromUser(user)
        .then(ownerId => {
          setOwnerId(ownerId);
          setOwnerLoading(false);
        })
        .catch((err) => {
          setOwnerId(null);
          setOwnerLoading(false);
          console.log(err);
        });
    }
  }, [dispatch, user]);

  // Check for current, non-expired subscription
  useEffect(() => {
    const checkSubscription = async () => {
      if (!ownerId) return;
      setCheckingSubscription(true);
      try {
        const subscription = await subscriptionService.getOwnerSubscription(ownerId);
        if (subscription && subscription.expiryAt) {
          const expiry = new Date(subscription.expiryAt).getTime();
          const now = new Date().getTime();
          if (expiry > now) {
            navigate('/dashboard');
            return;
          }
        }
      } catch (err) {
        // ignore, allow page to load
      } finally {
        setCheckingSubscription(false);
      }
    };
    checkSubscription();
  }, [ownerId, navigate]);

  const handleSelectPlan = (plan: PricingPlan) => {
    setSelectedPlan(plan);
    setShowCheckout(true);
  };

  const handleCheckoutSuccess = () => {
    setShowCheckout(false);
    setSelectedPlan(null);
    navigate("/dashboard");
  };

  const handleCheckoutCancel = () => {
    setShowCheckout(false);
    setSelectedPlan(null);
  };

  if (loading || checkingSubscription) return <div className="text-center py-10">Loading plans...</div>;
  if (error) return <div className="text-center text-red-500 py-10">{error}</div>;

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex flex-col">
      {/* Header Section */}
      <div className="bg-[#1F4168] text-white pt-12 pb-32 px-4 sm:pt-16 sm:pb-40 md:pt-20 md:pb-48">
        <div className="max-w-7xl mx-auto flex flex-col md:flex-row md:justify-between md:items-start gap-6 md:gap-8">
          <div className="flex-1 min-w-0 space-y-4 sm:space-y-6">
            <div className="space-y-3 sm:space-y-4">
              <div className="text-3xl sm:text-4xl md:text-5xl text-[#2BCFD5] font-semibold">Pricing</div>
              <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold">Subscription Plans</h1>
              <div className="text-base sm:text-lg text-gray-200">We have plans to fit your family's needs.</div>
            </div>
            <ToggleGroup
              type="single"
              value={billing}
              onValueChange={(v) => v && setBilling(v)}
              className="flex flex-wrap gap-2"
            >
              <ToggleGroupItem
                value="monthly"
                className="px-4 sm:px-6 py-2 sm:py-3 data-[state=on]:bg-[#2BCFD5] data-[state=on]:text-white border border-[#2BCFD5] rounded-lg transition-all duration-200 hover:bg-[#2BCFD5]/10 text-sm sm:text-base"
              >
                Monthly billing
              </ToggleGroupItem>
              <ToggleGroupItem
                value="annual"
                className="px-4 sm:px-6 py-2 sm:py-3 data-[state=on]:bg-[#2BCFD5] data-[state=on]:text-white border border-[#2BCFD5] rounded-lg transition-all duration-200 hover:bg-[#2BCFD5]/10 text-sm sm:text-base"
              >
                Annual billing
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
          <div className="text-sm md:text-base text-[#2BCFD5] md:text-right mt-4 md:mt-0 bg-white/10 p-3 sm:p-4 rounded-lg backdrop-blur-sm">
            <span className="font-semibold block mb-2">With Heirkey, there are two users</span>
            <div className="space-y-1">
              <p><span className="font-bold text-white">Owner</span>: person loading in the information to share.</p>
              <p><span className="font-bold text-white">Keyholder</span>: The person retrieving the information</p>
            </div>
          </div>
        </div>
      </div>

      {/* Cards Section */}
      <div className="flex-1 flex flex-col items-center justify-center py-0 px-4 -mt-24 sm:-mt-28 md:-mt-36 pb-8 sm:pb-12 md:pb-20">
        <div className="w-full max-w-7xl grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
          {filteredPlans.map((plan: any) => (
            <Card
              key={plan._id}
              className={`flex flex-col h-full rounded-xl sm:rounded-2xl border-2 transition-all duration-300 hover:shadow-xl`}
            >
              <CardHeader className="flex flex-col items-center bg-[#F0F9FF] rounded-t-xl sm:rounded-t-2xl px-4 sm:px-6 py-6 sm:py-8">
                <div className="mb-3 sm:mb-4">
                  <span className="inline-flex items-center gap-2 px-3 sm:px-4 py-1 sm:py-1.5 rounded-full border border-[#D1E9F7] bg-white text-[#1F4168] text-sm sm:text-base font-semibold shadow-sm">
                    <span className="w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-[#2BCFD5] inline-block"></span>
                    {plan.type.replace(/_/g, " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                  </span>
                </div>
                <CardTitle className="text-3xl sm:text-4xl font-bold mb-2 text-center">{plan.displayPrice}</CardTitle>
                <CardDescription className="text-sm sm:text-base text-gray-600 text-center">{plan.tagline}</CardDescription>
                <hr className="w-full border-t border-gray-200 my-4 sm:my-6" />
              </CardHeader>
              <CardContent className="flex-1 flex flex-col p-4 sm:p-6">
                <ul className="mb-6 sm:mb-8 space-y-3 sm:space-y-4 text-sm sm:text-base text-gray-700">
                  {plan.features.map((feature: string, i: number) => (
                    <li key={i} className="flex items-start gap-2 sm:gap-3">
                      <span className="text-[#2BCFD5] mt-1 flex-shrink-0">✓</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button
                  className="mt-auto w-full font-semibold rounded-lg text-sm sm:text-base py-2 sm:py-3 transition-all duration-200 bg-[#2BCFD5] hover:bg-[#1F4168] text-white"
                  onClick={() => handleSelectPlan(plan)}
                  disabled={ownerLoading || !ownerId}
                >
                  Get started
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Stripe Checkout Dialog */}
      <Dialog open={showCheckout} onOpenChange={setShowCheckout}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Complete Your Subscription</DialogTitle>
          </DialogHeader>
          {selectedPlan && (
            <StripeCheckout
              plan={selectedPlan}
              onSuccess={handleCheckoutSuccess}
              onCancel={handleCheckoutCancel}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}