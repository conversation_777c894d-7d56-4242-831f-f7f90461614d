"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendEmergencyAccessAutoGrantEmail = exports.sendEmergencyAccessRejectionEmail = exports.sendEmergencyAccessApprovalEmail = exports.sendEmergencyAccessRequestEmail = exports.sendPasswordResetSuccessEmail = exports.sendPasswordResetEmail = exports.sendEmailVerificationOTP = void 0;
const nodemailer = require("nodemailer");
const emailTemplates_1 = require("./emailTemplates");
// Helper function to extract reset URL from email text
const extractResetUrl = (text) => {
    const urlMatch = text.match(/http:\/\/[^\s]+/);
    return urlMatch ? urlMatch[0] : 'URL not found';
};
const sendMail = (options) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const transporter = nodemailer.createTransport({
        service: "gmail",
        host: "smtp.gmail.com",
        port: 465,
        secure: true, // true for 465, false for other ports
        auth: {
            user: process.env.STEP_GMAIL,
            pass: process.env.STEP_PASSWORD,
        },
        tls: {
            rejectUnauthorized: false
        }
    });
    const emailOptions = {
        from: `Heirkey support<${process.env.STEP_GMAIL}>`,
        to: options.to,
        subject: options.subject,
        text: options.text,
        html: options.html
    };
    try {
        const result = yield transporter.sendMail(emailOptions);
        console.log('Email sent successfully:', result.messageId);
        return result;
    }
    catch (error) {
        console.error('Email sending failed:', error);
        // Check if it's a rate limit error
        if (error.responseCode === 550 && ((_a = error.response) === null || _a === void 0 ? void 0 : _a.includes('Daily user sending limit exceeded'))) {
            console.log('Gmail daily sending limit exceeded. Using debug mode...');
            // In development, log the email content for debugging
            // if (process.env.NODE_ENV === 'development') {
            //   console.log('📧 EMAIL DEBUG MODE:');
            //   console.log('To:', emailOptions.to);
            //   console.log('Subject:', emailOptions.subject);
            //   console.log('Text:', emailOptions.text);
            //   console.log('========================');
            // }
            return {
                messageId: 'debug-mode-' + Date.now(),
                debugMode: true,
                error: 'Daily sending limit exceeded'
            };
        }
        // For other errors, still return a fallback
        return {
            messageId: 'fallback-mode-' + Date.now(),
            error: error.message
        };
    }
});
// Send email verification OTP
const sendEmailVerificationOTP = (email, username, otp) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const emailTemplate = (0, emailTemplates_1.generateEmailVerificationTemplate)(username, otp);
        const emailOptions = {
            from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
            to: email,
            subject: emailTemplate.subject,
            text: emailTemplate.text,
            html: emailTemplate.html
        };
        const result = yield sendMail(emailOptions);
        // console.log('Email verification OTP sent successfully:', result.messageId);
        return result;
    }
    catch (error) {
        // console.error('Failed to send email verification OTP:', error);
        throw error;
    }
});
exports.sendEmailVerificationOTP = sendEmailVerificationOTP;
// Send password reset email
const sendPasswordResetEmail = (email, username, resetUrl) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const emailTemplate = (0, emailTemplates_1.generatePasswordResetTemplate)(username, resetUrl);
        const emailOptions = {
            from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
            to: email,
            subject: emailTemplate.subject,
            text: emailTemplate.text,
            html: emailTemplate.html
        };
        const result = yield sendMail(emailOptions);
        // console.log('Password reset email sent successfully:', result.messageId);
        return result;
    }
    catch (error) {
        // console.error('Failed to send password reset email:', error);
        throw error;
    }
});
exports.sendPasswordResetEmail = sendPasswordResetEmail;
// Send password reset success email
const sendPasswordResetSuccessEmail = (email, username) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const emailTemplate = (0, emailTemplates_1.generatePasswordResetSuccessTemplate)(username);
        const emailOptions = {
            from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
            to: email,
            subject: emailTemplate.subject,
            text: emailTemplate.text,
            html: emailTemplate.html
        };
        const result = yield sendMail(emailOptions);
        // console.log('Password reset success email sent successfully:', result.messageId);
        return result;
    }
    catch (error) {
        // console.error('Failed to send password reset success email:', error);
        throw error;
    }
});
exports.sendPasswordResetSuccessEmail = sendPasswordResetSuccessEmail;
// Send emergency access request email to owner
const sendEmergencyAccessRequestEmail = (ownerEmail, ownerName, keyHolderName) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const emailTemplate = (0, emailTemplates_1.generateEmergencyAccessRequestTemplate)(ownerName, keyHolderName);
        const emailOptions = {
            from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
            to: ownerEmail,
            subject: emailTemplate.subject,
            text: emailTemplate.text,
            html: emailTemplate.html
        };
        const result = yield sendMail(emailOptions);
        return result;
    }
    catch (error) {
        throw error;
    }
});
exports.sendEmergencyAccessRequestEmail = sendEmergencyAccessRequestEmail;
// Send emergency access approval email to key holder
const sendEmergencyAccessApprovalEmail = (keyHolderEmail, keyHolderName, ownerName) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const emailTemplate = (0, emailTemplates_1.generateEmergencyAccessApprovalTemplate)(keyHolderName, ownerName);
        const emailOptions = {
            from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
            to: keyHolderEmail,
            subject: emailTemplate.subject,
            text: emailTemplate.text,
            html: emailTemplate.html
        };
        const result = yield sendMail(emailOptions);
        return result;
    }
    catch (error) {
        throw error;
    }
});
exports.sendEmergencyAccessApprovalEmail = sendEmergencyAccessApprovalEmail;
// Send emergency access rejection email to key holder
const sendEmergencyAccessRejectionEmail = (keyHolderEmail, keyHolderName, ownerName) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const emailTemplate = (0, emailTemplates_1.generateEmergencyAccessRejectionTemplate)(keyHolderName, ownerName);
        const emailOptions = {
            from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
            to: keyHolderEmail,
            subject: emailTemplate.subject,
            text: emailTemplate.text,
            html: emailTemplate.html
        };
        const result = yield sendMail(emailOptions);
        return result;
    }
    catch (error) {
        throw error;
    }
});
exports.sendEmergencyAccessRejectionEmail = sendEmergencyAccessRejectionEmail;
// Send emergency access auto-grant email to key holder
const sendEmergencyAccessAutoGrantEmail = (keyHolderEmail, keyHolderName, ownerName) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const emailTemplate = (0, emailTemplates_1.generateEmergencyAccessAutoGrantTemplate)(keyHolderName, ownerName);
        const emailOptions = {
            from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
            to: keyHolderEmail,
            subject: emailTemplate.subject,
            text: emailTemplate.text,
            html: emailTemplate.html
        };
        const result = yield sendMail(emailOptions);
        return result;
    }
    catch (error) {
        throw error;
    }
});
exports.sendEmergencyAccessAutoGrantEmail = sendEmergencyAccessAutoGrantEmail;
exports.default = sendMail;
//# sourceMappingURL=email.js.map