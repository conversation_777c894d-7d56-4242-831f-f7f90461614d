import React, { useEffect, useState } from 'react';
import { Formik, Form, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { Button } from '@/components/ui/button';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  UserInput
} from '@/store/slices/homeInstructionsSlice';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import {
  Question,
  QuestionItem,
  buildValidationSchema,
  generateInitialValues,
  handleDependentAnswers
} from '@/web/components/Category/HomeInstructions/FormFields';
import ScrollToQuestion from '@/web/components/Category/HomeInstructions/ScrollToQuestion';
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';

const HomeInstructionSection: React.FC = () => {
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  // Get questions for Home Location (subcategory '105')
  const questions = useAppSelector((state: any) => state.homeInstructions.questions['105'] || []);
  const loading = useAppSelector((state: any) => state.homeInstructions.loading);
  const userInputs = useAppSelector((state: any) => state.homeInstructions.userInputs);

  // Fetch user inputs on mount
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (ownerId) dispatch(fetchUserInputs(ownerId));
      }
    };
    fetchData();
  }, [dispatch, user]);

  // Process user inputs to get saved answers
  useEffect(() => {
    if (!loading) {
      if (userInputs.length > 0) {
        const userInput = userInputs.find((input: UserInput) => input.originalSubCategoryId === '105');
        if (userInput) {
          setSavedAnswers(convertUserInputToFormValues(userInput));
          setExistingInputId(userInput._id || null);
        }
      }
      setIsDataLoaded(true);
    }
  }, [userInputs, loading]);

  // Handle form submission
  const handleSubmit = async (
    values: Record<string, string>,
    { setSubmitting }: FormikHelpers<Record<string, string>>
  ) => {
    try {
      if (!user || !user.id) throw new Error('You must be logged in to save answers');
      const ownerId = await getCachedOwnerIdFromUser(user);

      // Group answers by section
      const answersBySection = questions.reduce(
        (sections: Record<string, Array<any>>, question: Question) => {
          if (!sections[question.sectionId]) {
            sections[question.sectionId] = [];
          }
          const answer = values[question.id];
          if (answer) {
            sections[question.sectionId].push({
              index: sections[question.sectionId].length,
              originalQuestionId: question.id,
              question: question.text,
              type: question.type,
              answer,
            });
          }
          return sections;
        },
        {}
      );

      // Format answers data
      const formattedAnswersBySection = Object.entries(answersBySection).map(
        ([sectionId, answers]) => ({
          originalSectionId: sectionId,
          isCompleted: true,
          answers: answers as any[],
        })
      );

      console.log('Form values:', values);
      console.log('Formatted answers by section:', formattedAnswersBySection);

      if (existingInputId) {
        await dispatch(
          updateUserInput({
            id: existingInputId,
            userData: {
              userId: user.id,
              categoryId: generateObjectId(),
              originalCategoryId: '1',
              subCategoryId: generateObjectId(),
              originalSubCategoryId: '105',
              answersBySection: formattedAnswersBySection,
            } as UserInput,
          })
        ).unwrap();
      } else {
        const userData: Omit<UserInput, '_id'> = {
          userId: user.id,
          categoryId: generateObjectId(),
          originalCategoryId: '1',
          subCategoryId: generateObjectId(),
          originalSubCategoryId: '105',
          answersBySection: formattedAnswersBySection,
        };
        await dispatch(saveUserInput(userData)).unwrap();
      }

      // Force re-fetch to update Redux state everywhere
      if (ownerId) {
        await dispatch(fetchUserInputs(ownerId));
      }

      setSubmitting(false);
      // Optionally, show a toast or redirect
    } catch (error) {
      setSubmitting(false);
    }
  };

  // Show loading until both questions and user data are loaded
  if (questions.length === 0 || loading || !isDataLoaded) {
    return <div className="flex justify-center items-center h-40">Loading...</div>;
  }

  const validationSchema = buildValidationSchema(questions, Yup);
  const baseInitialValues = generateInitialValues(questions);
  const initialValues = { ...baseInitialValues, ...savedAnswers };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
      <Formik
        key={`${isDataLoaded}-${JSON.stringify(savedAnswers)}`} // Force re-render when data is loaded
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize={true} // Allow Formik to reinitialize with new values
      >
        {({ values, isSubmitting, isValid, dirty, setValues }) => {
          const handleDependentFields = () => {
            handleDependentAnswers(values, questions, setValues);
          };
          if (Object.keys(values).length > 0) setTimeout(handleDependentFields, 0);

          return (
            <Form>
              <ScrollToQuestion questions={questions}>
                {(refs) => (
                  <>
                    {[...questions]
                      .sort((a: Question, b: Question) => a.order - b.order)
                      .map((question: Question) => (
                        <div
                          key={question.id}
                          id={`question-${question.id}`}
                          ref={(el: HTMLDivElement | null) => {
                            refs[question.id] = el;
                          }}
                        >
                          <QuestionItem question={question} values={values} />
                        </div>
                      ))}
                  </>
                )}
              </ScrollToQuestion>
              <div className="mt-8 flex justify-end">
                <Button
                  type="submit"
                  disabled={isSubmitting || !isValid || !dirty}
                  className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                >
                  Save & Continue
                </Button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default HomeInstructionSection;