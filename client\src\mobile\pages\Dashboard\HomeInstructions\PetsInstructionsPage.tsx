// PetsStepperForm.jsx
import React, { useEffect, useState } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { Formik, Field, Form } from 'formik';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { fetchUserInputs, saveUserInput, updateUserInput, selectQuestionsBySubcategoryId, selectUserInputsBySubcategoryId, selectLoading, selectError } from '@/store/slices/homeInstructionsSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { Alert, AlertDescription } from '@/components/ui/alert';
import ScrollToQuestion from '@/mobile/components/dashboard/HomeInstructions/ScrollToQuestion';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { Question } from '@/store/slices/homeInstructionsSlice';
import { useAuth } from '@/contexts/AuthContext';
import { generateObjectId } from '@/services/userInputService';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

interface PetFormValues {
  [key: string]: string;
}

function buildInitialValues(questions: any[]): PetFormValues {
  const vals: PetFormValues = {};
  questions.forEach((q: any) => { vals[q.id] = ""; });
  return vals;
}

function splitSteps(questions: any[]): any[][] {
  // Don't split into steps, show all questions at once
  return [questions];
}

function isGhosted(q: any, values: PetFormValues): boolean {
  // Only ghost questions that depend on q1 being 'no'
  if (q.dependsOn && q.dependsOn.questionId === 'q1') {
    return values[q.dependsOn.questionId] !== q.dependsOn.value;
  }
  return false;
}

export default function PetsInstructionsPage() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { categoryName } = useParams();
  const { user } = useAuth();

  // Get questions and user inputs from Redux
  const questions: any[] = useAppSelector((state: any) => selectQuestionsBySubcategoryId('101')(state));
  const userInputs = useAppSelector((state: any) => selectUserInputsBySubcategoryId('101')(state));
  const isLoading = useAppSelector((state: any) => selectLoading(state));
  const reduxError = useAppSelector((state: any) => selectError(state));

  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [savedAnswers, setSavedAnswers] = useState<PetFormValues>({});
  const [showSuccessMessage, setShowSuccessMessage] = useState<boolean>(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState<boolean>(false);

  // Initial values
  const initialValues: PetFormValues = { ...buildInitialValues(questions), ...savedAnswers };

  // Get questionId from URL (for review/edit)
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');

  // Fetch user inputs on mount
  useEffect(() => {
    if (user && user.id) {
      getCachedOwnerIdFromUser(user).then(ownerId => {
        if (ownerId) dispatch(fetchUserInputs(ownerId));
      });
    }
  }, [dispatch, user]);

  // Load saved answers from Redux
  useEffect(() => {
    if (userInputs && userInputs.length > 0 && !savedAnswers.q1) {  // Only set if not already set
      const userInput = userInputs[0];
      if (userInput) {
        // Build answers object from backend
        const answers: PetFormValues = {};
        userInput.answersBySection?.forEach((section: any) => {
          section.answers?.forEach((a: any) => { answers[a.originalQuestionId] = a.answer; });
        });
        setSavedAnswers({ ...buildInitialValues(questions), ...answers });
        setExistingInputId(userInput._id ? String(userInput._id) : null);
      }
    }
  }, [userInputs, questions, savedAnswers.q1]);

  if (isLoading) {
    return <div className="p-4 text-center">Loading your answers...</div>;
  }

  // Validation: only maxLength for text fields
  function validate(values: PetFormValues) {
    const errors: { [key: string]: string } = {};
    questions.forEach((q: any) => {
      if (q.validationRules?.maxLength && values[q.id]?.length > q.validationRules.maxLength) {
        errors[q.id] = `Maximum ${q.validationRules.maxLength} characters`;
      }
    });
    return errors;
  }

  // Handle form submit
  async function handleSubmit(values: PetFormValues, { setSubmitting }: { setSubmitting: (isSubmitting: boolean) => void }) {
    setHasAttemptedSubmit(true);
    // Check if user is authenticated
    if (!user || !user.id) {
      setFormError('You must be logged in to save answers');
      setSubmitting(false);
      return;
    }
    try {
      // Format answers for backend
      const answers = Object.entries(values)
        .filter(([, v]) => v !== "")
        .map(([key, value], idx) => {
          const q = questions.find((q: any) => q.id === key);
          return {
            index: idx,
            originalQuestionId: key,
            question: q?.text || key,
            type: q?.type || 'text',
            answer: String(value)
          };
        });
      const formattedAnswersBySection = [{
        originalSectionId: '101A',
        isCompleted: true,
        answers
      }];
      // Save or update
      if (existingInputId) {
        await dispatch(updateUserInput({
          id: existingInputId,
          userData: {
            userId: user.id,
            categoryId: generateObjectId(),
            originalCategoryId: '1',
            subCategoryId: generateObjectId(),
            originalSubCategoryId: '101',
            answersBySection: formattedAnswersBySection
          }
        })).unwrap();
      } else {
        await dispatch(saveUserInput({
          userId: user.id,
          categoryId: generateObjectId(),
          originalCategoryId: '1',
          subCategoryId: generateObjectId(),
          originalSubCategoryId: '101',
          answersBySection: formattedAnswersBySection
        })).unwrap();
      }
      // Refresh Redux
      if (user && user.id) {
        getCachedOwnerIdFromUser(user).then(ownerId => {
          if (ownerId) dispatch(fetchUserInputs(ownerId));
        });
      }
      setShowSuccessMessage(true);
      navigate(`/category/${categoryName}/trash`);
      setSubmitting(false);
    } catch (err) {
      setFormError('Failed to save your answers.');
      setSubmitting(false);
    }
  }

  return (
     <>
      <GradiantHeader title="Home Instructions" showAvatar={true} />
      <div style={{ padding: 16 }}>
        {/* Tab Bar */}
        <div className="flex gap-2 mb-4 bg-gray-50 rounded-2xl p-1">
          {categoryTabsConfig.homeinstructions.map(tab => {
            const isActive = tab.label === "Pets";
            return (
              <button
                key={tab.label}
                type="button"
                className={
                  "flex-1 py-2 rounded-md font-medium " +
                  (isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500")
                }
                disabled={isActive}
                onClick={() => {
                  if (!isActive) navigate(tab.path);
                }}
              >
                {tab.label}
              </button>
            );
          })}
        </div>
        {hasAttemptedSubmit && (formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}
        <Formik
          enableReinitialize
          initialValues={initialValues}
          validate={validate}
          onSubmit={handleSubmit}
        >
          {({ values, isSubmitting, errors, setFieldValue }) => {
            // Clear dependent answers if q1 is set to 'no'
            React.useEffect(() => {
              if (values.q1 === 'no') {
                const dependentIds = questions
                  .filter(q => q.dependsOn && q.dependsOn.questionId === 'q1' && q.dependsOn.value === 'yes')
                  .map(q => q.id);
                dependentIds.forEach(id => {
                  if (values[id]) setFieldValue(id, '');
                });
              }
            }, [values.q1, questions, setFieldValue, values]);

            return (
              <Form>
                <div className="bg-gray-50 p-5 rounded-xl border">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-semibold">
                      Home Instructions: <span className="text-[#2BCFD5]">Pets</span>
                    </p>
                    {/* Section circular progress indicator */}
                    {(() => {
                      const visibleQuestions = questions.filter(q => !isGhosted(q, values));
                      const answeredCount = visibleQuestions.filter(q => values[q.id] && values[q.id].trim() !== '').length;
                      return (
                        <div className="flex items-center">
                          <CircularProgress
                            value={answeredCount}
                            max={visibleQuestions.length}
                            size={40}
                            stroke={3}
                            color="#2BCFD5"
                          />
                          <span className='absolute inset-0 flex items-center justify-center text-[#e5e7eb] font-semibold text-sm pointer-events-none'>
                            {answeredCount}/{visibleQuestions.length}
                          </span>
                        </div>
                      );
                    })()}
                  </div>
                </div>
                <div className="space-y-4 mt-8 bg-gray-50 p-5 rounded-xl border">
                  <ScrollToQuestion questions={questions}>
                    {(refs) => (
                      <>
                        {questions.map(q => {
                          const ghosted = isGhosted(q, values);
                          return (
                          <div
                            key={q.id}
                            id={`question-${q.id}`}
                            ref={(el) => { refs[q.id] = el; }}
                            className={ghosted ? "opacity-50 pointer-events-none" : ""}
                          >
                            <label className="block font-medium text-gray-700 mb-2">
                              {q.text}{q.required && " *"}
                            </label>
                            {q.type === 'boolean' ? (
                              <div className="flex space-x-4">
                                <label className={`flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer transition-colors
                                  ${values[q.id] === 'yes' ? 'bg-[#2BCFD5] text-white border-[#2BCFD5]' : 'bg-gray-50 hover:bg-[#25b6bb] hover:text-white'}`}
                                >
                                  <Field type="radio" name={q.id} value="yes" className="hidden" />
                                  Yes
                                </label>
                                <label className={`flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer transition-colors
                                  ${values[q.id] === 'no' ? 'bg-[#2BCFD5] text-white border-[#2BCFD5]' : 'bg-gray-50 hover:bg-[#25b6bb] hover:text-white'}`}
                                >
                                  <Field type="radio" name={q.id} value="no" className="hidden" />
                                  No
                                </label>
                              </div>
                            ) : q.type === 'choice' ? (
                              <Field
                                as="select"
                                name={q.id}
                                value={values[q.id] ?? ""}
                                className="w-full border rounded-lg px-3 py-2"
                              >
                                <option value="">Select</option>
                                {q.options?.map((opt: string) => (
                                  <option key={opt} value={opt}>{opt}</option>
                                ))}
                              </Field>
                            ) : q.id === 'q6' ? (
                              <PhoneInput
                                country={'us'}
                                value={values[q.id] || ''}
                                onChange={val => setFieldValue(q.id, val)}
                                inputClass="w-full border rounded-lg px-3 py-2"
                                inputStyle={{ width: '100%' }}
                                specialLabel=""
                                placeholder={q.placeholder || 'Phone Number'}
                              />
                            ) : (
                              <Field
                                name={q.id}
                                as={q.id === "q4" ? "textarea" : "input"}
                                value={values[q.id] ?? ""}
                                className="w-full border rounded-lg px-3 py-2"
                                rows={q.id === "q4" ? 3 : undefined}
                              />
                            )}
                            {isSubmitting && errors[q.id] && (
                              <div className="text-red-500 text-sm mt-1">{errors[q.id]}</div>
                            )}
                          </div>
                          );
                        })}
                      </>
                    )}
                  </ScrollToQuestion>
                </div>
                {showSuccessMessage && (
                  <div className="mt-4 p-4 bg-green-100 text-green-800 rounded-lg">
                    <div>
                      <p className="font-semibold">Data saved successfully!</p>
                      <p className="text-sm">Your pet information has been saved.</p>
                    </div>
                  </div>
                )}
                <div className="mt-6 flex justify-end">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                  >
                    Save
                  </button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <Footer />
    </>
  );
}