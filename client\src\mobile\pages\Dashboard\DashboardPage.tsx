import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useEffect, useState, useRef, useMemo } from 'react';
import CategoryCard from '@/mobile/components/dashboard/CategoryCard';
import RequestList from '@/components/RequestCategories/RequestList';
import { useAuth } from '@/contexts/AuthContext';
import homeImg from '@/assets/global/category/home.jpg'
import documentsImg from '@/assets/global/category/document.jpg'
import willImg from '@/assets/global/category/will.jpg'
import funeralImg from '@/assets/global/category/funeral.jpg'
import contactImg from '@/assets/global/category/contact.jpg'
import socialMediaImg from '@/assets/global/category/socialMedia.jpg'
import insuranceImg from '@/assets/global/category/Insurance.jpg'
import quickStartImg from '@/assets/global/category/QuickStart.jpg'
import financialImg from '@/assets/global/category/Financial.jpg'
import bankingImg from '@/assets/global/category/Banking.jpg'
import subscriptionImg from '@/assets/global/category/OTT.jpg'
import ownershipImg from '@/assets/global/category/Ownership.jpg'
import passwordsImg from '@/assets/global/category/Passwords.jpg'
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import userInputService from '@/services/userInputService';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { selectDynamicOverallProgress } from '@/store/slices/homeInstructionsSlice';
import { selectQuickStartProgress, loadQuickStartData } from '@/store/slices/quickStartSlice';
import { selectProgressStats as selectInsuranceProgress } from '@/store/slices/insuranceSlice';
import { selectProgressStats as selectFinancialPlansProgress, fetchUserInputs as fetchFinancialPlansUserInputs } from '@/store/slices/financialPlansSlice';
import { selectDynamicOverallProgress as selectSubscriptionProgress, fetchUserInputs as fetchSubscriptionUserInputs } from '@/store/slices/subscriptionCategorySlice';
import { selectProgressStats as selectOwnershipProgress, fetchUserInputs as fetchOwnershipUserInputs } from '@/store/slices/ownershipInfoSlice';
import { selectProgressStats as selectPasswordsProgress, fetchUserInputs as fetchPasswordsUserInputs } from '@/store/slices/passwordsSlice';
import { useAccessControl } from '@/hooks/useAccessControl';
import { useToast } from '@/contexts/ToastContext';
import { fetchOwnerRequests } from '@/store/slices/requestedCategoriesSlice';

const categories = [
  {
    id: 'quickstart',
    name: 'Quick Start',
    description: 'Get started quickly with essential information.',
    imageUrl: quickStartImg,
  },
  {
    id: 'homeinstructions',
    name: 'Home Instructions',
    description: 'Instructions for your home and pets.',
    imageUrl: homeImg,
  },
  {
    id: 'homedocuments',
    name: 'Home Documents',
    description: 'Important documents for your home.',
    imageUrl: documentsImg,
  },
  {
    id: 'willlocation',
    name: 'Will Location',
    description: 'Where your will is stored.',
    imageUrl: willImg,
  },
  {
    id: 'funeralarrangements',
    name: 'Funeral Arrangements',
    description: 'Your funeral preferences.',
    imageUrl: funeralImg,
  },
  {
    id: 'importantcontacts',
    name: 'Important Contacts',
    description: 'People to contact in case of emergency.',
    imageUrl: contactImg,
  },
  {
    id: 'socialmediaphone',
    name: 'Social Media & Phone',
    description: 'Your social media and phone details.',
    imageUrl: socialMediaImg,
  },
  {
    id: 'insurance',
    name: 'Insurance',
    description: 'Your insurance policies and coverage details.',
    imageUrl: insuranceImg,
  },
  {
    id: 'financialplans',
    name: 'Financial Plans',
    description: 'Your financial planning and investment information.',
    imageUrl: financialImg,
  },
  {
    id: 'ownershipinfo',
    name: 'Ownership Information',
    description: 'Your property, vehicle, and driver information.',
    imageUrl: ownershipImg,
  },
  {
    id: 'bankinginformation',
    name: 'Banking Information',
    description: 'Your banking account details and credentials.',
    imageUrl: bankingImg,
  },
  {
    id: 'passwords',
    name: 'Passwords',
    description: 'Store passwords for various services and accounts.',
    imageUrl: passwordsImg,
  },
  {
    id: 'subscription',
    name: 'Subscription',
    description: 'Your subscription service information.',
    imageUrl: subscriptionImg,
  },
];

// Define interface for category stats
interface CategoryStat {
  categoryId: string;
  answeredQuestions: number;
}

// Define total questions for each category
const totalQuestions = {
  'homeinstructions': 21,
  'homedocuments': 64,
  'willlocation': 8,
  'funeralarrangements': 15,
  'importantcontacts': 4,
  'socialmediaphone': 22,
  'quickstart': 10,
  'insurance': 34,
  'financialplans': 11,
  'bankinginformation': 6,
  'passwords': 1,
  'subscription': 16,
  'ownershipinfo': 28,
};

export default function DashboardPage() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const dataFetchedRef = useRef(false);
  const homeInstructionsProgress = useAppSelector(selectDynamicOverallProgress);
  const quickStartProgress = useAppSelector(selectQuickStartProgress);
  const insuranceProgress = useAppSelector(selectInsuranceProgress);
  const financialPlansProgress = useAppSelector(selectFinancialPlansProgress);
  const subscriptionProgress = useAppSelector(selectSubscriptionProgress);
  const ownershipProgress = useAppSelector(selectOwnershipProgress);
  const passwordsProgress = useAppSelector(selectPasswordsProgress);
  const dispatch = useAppDispatch();
  const { canAccessCategory, userAccess } = useAccessControl();
  const { toast } = useToast();

  // Initialize question counts with zeros
  const [questionCounts, setQuestionCounts] = useState({
    'homeinstructions': 0,
    'homedocuments': 0,
    'willlocation': 0,
    'funeralarrangements': 0,
    'importantcontacts': 0,
    'socialmediaphone': 0,
    'quickstart': 0,
    'insurance': 0,
    'financialplans': 0,
    'bankinginformation': 0,
    'passwords': 0,
    'subscription': 0,
    'ownershipinfo': 0,
  });

  // Use dynamic total for homeinstructions - memoized to prevent re-renders
  const dynamicTotalQuestions = useMemo(() => ({
    ...totalQuestions,
    'homeinstructions': homeInstructionsProgress.totalQuestions
  }), [homeInstructionsProgress.totalQuestions]);

  // Map category titles to IDs for access control
  const categoryIdMap: Record<string, string> = {
    'Quick Start': '7',
    'Home Instructions': '1',
    'Home Documents': '2',
    'Will Location': '3',
    'Funeral Arrangements': '4',
    'Important Contacts': '5',
    'Social Media & Phone': '6',
    'Insurance': '8',
    'Financial Plans': '9',
    'Ownership Information': '10',
    'Banking Information': '13',
    'Subscription': '11',
    'Passwords': '12',
  };

  // Simplified data fetching - use user.id directly to avoid dependency issues
  useEffect(() => {
    const fetchData = async () => {
      const userId = user?.id;
      if (!userId || dataFetchedRef.current) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        // Get owner ID for the user using just the user ID
        const ownerId = await getCachedOwnerIdFromUser({
          id: userId,
          email: user?.email || '',
          username: user?.username || ''
        });
        if (!ownerId) {
          console.error('No owner ID found for user');
          setIsLoading(false);
          return;
        }

        // Fetch dashboard stats using owner ID
        const dashboardStats = await userInputService.getDashboardStats(ownerId);

        // Create a new question counts object with default values
        const newQuestionCounts = {
          'homeinstructions': 0,
          'homedocuments': 0,
          'willlocation': 0,
          'funeralarrangements': 0,
          'importantcontacts': 0,
          'socialmediaphone': 0,
          'quickstart': 0,
          'insurance': 0,
          'financialplans': 0,
          'bankinginformation': 0,
          'passwords': 0,
          'subscription': 0,
          'ownershipinfo': 0,
        };

        // Update counts based on the stats from the API
        dashboardStats.forEach((stat: CategoryStat) => {
          switch (stat.categoryId) {
            case '1':
              newQuestionCounts.homeinstructions = stat.answeredQuestions;
              break;
            case '2':
              newQuestionCounts.homedocuments = stat.answeredQuestions;
              break;
            case '3':
              newQuestionCounts.willlocation = stat.answeredQuestions;
              break;
            case '4':
              newQuestionCounts.funeralarrangements = stat.answeredQuestions;
              break;
            case '5':
              newQuestionCounts.importantcontacts = stat.answeredQuestions;
              break;
            case '6':
              newQuestionCounts.socialmediaphone = stat.answeredQuestions;
                break;
            case '7':
              newQuestionCounts.quickstart = stat.answeredQuestions;
              break;
            case '8':
              newQuestionCounts.insurance = stat.answeredQuestions;
              break;
            case '9':
              newQuestionCounts.financialplans = stat.answeredQuestions;
              break;
            case '13':
              newQuestionCounts.bankinginformation = stat.answeredQuestions;
              break;
            case '12':
              newQuestionCounts.passwords = stat.answeredQuestions;
              break;
            case '11':
              newQuestionCounts.subscription = stat.answeredQuestions;
              break;
            case '10':
              newQuestionCounts.ownershipinfo = stat.answeredQuestions;
              break;
          }
        });

        // Update state with the new counts
        setQuestionCounts(newQuestionCounts);

        // Mark data as fetched
        dataFetchedRef.current = true;
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [user?.id, user?.email, user?.username]); // Depend on user.id, user.email, and user.username

  const quickStartDataLoadedRef = useRef(false);
  useEffect(() => {
    if (user?.id && !quickStartDataLoadedRef.current) {
      dispatch(loadQuickStartData(user.id));
      quickStartDataLoadedRef.current = true;
    }
  }, [dispatch, user?.id]); // Only depend on user.id, not the entire user object

  // Load ownership data
  const ownershipDataLoadedRef = useRef(false);
  useEffect(() => {
    const loadOwnershipData = async () => {
      if (!user?.id || ownershipDataLoadedRef.current) return;

      try {
        const ownerId = await getCachedOwnerIdFromUser({
          id: user.id,
          email: user?.email || '',
          username: user?.username || ''
        });
        if (ownerId) {
          await dispatch(fetchOwnershipUserInputs(ownerId));
          ownershipDataLoadedRef.current = true;
        }
      } catch (error) {
        console.error('Error loading ownership data:', error);
      }
    };

    loadOwnershipData();
  }, [dispatch, user?.id, user?.email, user?.username]); // Depend on user properties


  // Load financial plans data
  const financialPlansDataLoadedRef = useRef(false);
  useEffect(() => {
    const loadFinancialPlansData = async () => {
      if (!user?.id || financialPlansDataLoadedRef.current) return;

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (ownerId) {
          await dispatch(fetchFinancialPlansUserInputs(ownerId));
          financialPlansDataLoadedRef.current = true;
        }
      } catch (error) {
        console.error('Error loading financial plans data:', error);
        // Don't throw the error, just log it to prevent crashes
      }
    };
    loadFinancialPlansData();
  }, [dispatch, user?.id]); // Only depend on user.id, not the entire user object


  // Load subscription data
  const subscriptionDataLoadedRef = useRef(false);
  useEffect(() => {
    const loadSubscriptionData = async () => {
      if (!user?.id || subscriptionDataLoadedRef.current) return;

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (ownerId) {
          await dispatch(fetchSubscriptionUserInputs(ownerId));
          subscriptionDataLoadedRef.current = true;
        }
      } catch (error) {
        console.error('Error loading subscription data:', error);
        // Don't throw the error, just log it to prevent crashes
      }
    };

    loadSubscriptionData();
  }, [dispatch, user?.id]); // Only depend on user.id, not the entire user object

  // Load passwords data
  const passwordsDataLoadedRef = useRef(false);
  useEffect(() => {
    const loadPasswordsData = async () => {
      if (!user?.id || passwordsDataLoadedRef.current) return;

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (ownerId) {
          await dispatch(fetchPasswordsUserInputs(ownerId));
          passwordsDataLoadedRef.current = true;
        }
      } catch (error) {
        console.error('Error loading passwords data:', error);
        // Don't throw the error, just log it to prevent crashes
      }
    };

    loadPasswordsData();
  }, [dispatch, user?.id]); // Only depend on user.id, not the entire user object

  // Load insurance data - temporarily disabled to fix infinite loop
  // const insuranceDataLoadedRef = useRef(false);
  // useEffect(() => {
  //   const loadInsuranceData = async () => {
  //     if (!user?.id || insuranceDataLoadedRef.current) return;

  //     try {
  //       const ownerId = await getCachedOwnerIdFromUser(user);
  //       if (ownerId) {
  //         await dispatch(fetchInsuranceUserInputs(ownerId));
  //         insuranceDataLoadedRef.current = true;
  //       }
  //     } catch (error) {
  //       console.error('Error loading insurance data:', error);
  //       // Don't throw the error, just log it to prevent crashes
  //     }
  //   };

  //   loadInsuranceData();
  // }, [dispatch, user?.id]); // Only depend on user.id, not the entire user object

  // Simplified request handling - removed complex logic that might cause infinite loops
  useEffect(() => {
    if (userAccess?.isOwner) {
      dispatch(fetchOwnerRequests());
    }
  }, [dispatch, userAccess?.isOwner]);



  // 2. Success toast when data loads successfully - memoized to prevent infinite loops
  // const totalCompleted = useMemo(() =>
  //   Object.values(questionCounts).reduce((sum, count) => sum + count, 0),
  //   [questionCounts]
  // );

  // const totalQuestionsSum = useMemo(() =>
  //   Object.values(dynamicTotalQuestions).reduce((sum, total) => sum + total, 0),
  //   [dynamicTotalQuestions]
  // );

  // useEffect(() => {
  //   if (totalCompleted > 0 && totalCompleted === totalQuestionsSum && !allCategoriesCompletedToastShown.current) {
  //     toast({
  //       title: 'Congratulations!',
  //       description: 'You have completed all categories!',
  //       variant: 'default',
  //     });
  //     allCategoriesCompletedToastShown.current = true;
  //   }
  // }, [totalCompleted, totalQuestionsSum, toast]);

  // 3. Category completion toast - prevent duplicate toasts
  // useEffect(() => {
  //   Object.entries(questionCounts).forEach(([categoryId, answered]) => {
  //     const total = dynamicTotalQuestions[categoryId as keyof typeof dynamicTotalQuestions];
  //     if (answered > 0 && answered >= total && !completionToastShown.current.has(categoryId)) {
  //       const categoryName = categories.find(cat => cat.id === categoryId)?.name;
  //       toast({
  //         title: 'Category Completed!',
  //         description: `${categoryName} has been completed successfully.`,
  //         variant: 'default',
  //       });
  //       completionToastShown.current.add(categoryId);
  //     }
  //   });
  // }, [questionCounts, dynamicTotalQuestions, toast]);

  const handleCategoryClick = (categoryId: string, categoryName: string) => {
    // Check if category is locked
    const catId = categoryIdMap[categoryName];
    const locked = !canAccessCategory(catId);
    
    if (locked) {
      // Show toast notification for locked category
      toast({
        title: 'Upgrade Required',
        description: 'Upgrade your plan to answer or view this category.',
        variant: 'default',
      });
      return;
    }
    
    navigate(`/category/${categoryId}`, { state: { categoryName } });
  };

  const headerTitle = user?.firstName && user?.lastName
    ? `${user.firstName} ${user.lastName}`
    : user?.firstName || user?.email || 'Welcome';

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="min-h-screen overflow-y-auto bg-background"
    >
      <GradiantHeader
        title={headerTitle}
        showAvatar={true}
        showEmergencyStatus={true}
      />
      <div className="container mx-auto px-4 py-8 max-w-md" style={{ minHeight: '100vh' }}>
        <div className="overflow-y-auto">
          <div className="w-full max-w-md mx-auto px-4 py-6">
            

            {/* Emergency access UI removed as requested */}

            {/* Category Access Requests Section for Owners */}
            {userAccess?.isOwner && (
              <div className="mb-8">
                <RequestList isOwner={true} />
              </div>
            )}

            <div className="flex justify-between items-center mb-1">
              <h1 className="text-xl font-bold text-secondary-900">Your Folders</h1>
            </div>
            <p className="text-sm text-muted-foreground mb-6">
              Your information is organized in folders. Tap any section to explore the details.
            </p>

            <div className="flex flex-col gap-4 pb-10">
              {isLoading ? (
                // Loading indicator
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : (
                // Render category cards
                categories.map((category, idx) => {
                  // Use Redux selector for Quick Start and Subscription, backend stats for others
                  let questionCount = questionCounts[category.id as keyof typeof questionCounts] || 0;
                  let totalQuestions = dynamicTotalQuestions[category.id as keyof typeof dynamicTotalQuestions] || 0;
                  let isCompleted = false;
                  if (category.id === 'quickstart') {
                    questionCount = quickStartProgress?.answered || 0;
                    totalQuestions = quickStartProgress?.total || 10;
                    isCompleted = (quickStartProgress?.answered || 0) > 0 && (quickStartProgress?.answered || 0) >= (quickStartProgress?.total || 10);
                  } else if (category.id === 'insurance') {
                    questionCount = insuranceProgress?.answeredQuestions || 0;
                    totalQuestions = insuranceProgress?.totalQuestions || 34;
                    isCompleted = (insuranceProgress?.answeredQuestions || 0) > 0 && (insuranceProgress?.answeredQuestions || 0) >= (insuranceProgress?.totalQuestions || 34);
                  } else if (category.id === 'financialplans') {
                    questionCount = financialPlansProgress?.answeredQuestions || 0;
                    totalQuestions = financialPlansProgress?.totalQuestions || 11;
                    isCompleted = (financialPlansProgress?.answeredQuestions || 0) > 0 && (financialPlansProgress?.answeredQuestions || 0) >= (financialPlansProgress?.totalQuestions || 11);
                  } else if (category.id === 'subscription') {
                    questionCount = subscriptionProgress.answeredQuestions;
                    totalQuestions = subscriptionProgress.totalQuestions;
                    isCompleted = subscriptionProgress.answeredQuestions > 0 && subscriptionProgress.answeredQuestions >= subscriptionProgress.totalQuestions;
                  } else if (category.id === 'ownershipinfo') {
                    questionCount = ownershipProgress?.answeredQuestions || 0;
                    totalQuestions = ownershipProgress?.totalQuestions || 28;
                    isCompleted = (ownershipProgress?.answeredQuestions || 0) > 0 && (ownershipProgress?.answeredQuestions || 0) >= (ownershipProgress?.totalQuestions || 28);
                  } else if (category.id === 'passwords') {
                    questionCount = passwordsProgress?.answeredQuestions || 0;
                    totalQuestions = passwordsProgress?.totalQuestions || 1;
                    isCompleted = (passwordsProgress?.answeredQuestions || 0) > 0 && (passwordsProgress?.answeredQuestions || 0) >= (passwordsProgress?.totalQuestions || 1);
                  } else {
                    isCompleted = questionCount > 0 && questionCount >= totalQuestions;
                  }

                  // Check if category is locked
                  const catId = categoryIdMap[category.name];
                  const locked = !canAccessCategory(catId);

                  return (
                    <CategoryCard
                      key={category.id}
                      name={category.name}
                      description={category.description}
                      imageUrl={category.imageUrl}
                      questionCount={questionCount}
                      totalQuestions={totalQuestions}
                      isCompleted={isCompleted}
                      locked={locked}
                      index={idx}
                      onClick={() => handleCategoryClick(category.id, category.name)}
                    />
                  );
                })
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
