import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Printer } from 'lucide-react';
import { Button } from '@/components/ui/button';
import GradiantHeader from '../header/gradiantHeader';
import Footer from '../layout/Footer';
import { ReactNode } from 'react';

interface Topic {
  id: string;
  title: string;
  subtitle?: string;
  data: string;
  onEdit: () => void;
}

interface CategoryReviewPageProps {
  categoryTitle: string;
  infoTitle: string;
  infoDescription: string;
  topics: Topic[];
  onPrint?: () => void;
  afterTopics?: ReactNode;
}

const CategoryReviewPage = ({
  categoryTitle,
  infoTitle,
  infoDescription,
  topics,
  onPrint,
  afterTopics,
}: CategoryReviewPageProps) => {
  const navigate = useNavigate();
  const [infoVisible, setInfoVisible] = useState(true);

  return (
    <>
      <GradiantHeader
        title={categoryTitle || "Current Category"}
        showAvatar={true}
      />

      {/* Main Content */}
      <div className="px-4 py-5">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">{categoryTitle ? `${categoryTitle} Review` : "My Category Details"}</h2>
          <Button
            variant="outline"
            size="sm"
            className="text-sm"
            onClick={onPrint}
          >
            <Printer className="h-4 w-4 mr-1" />
            Print
          </Button>
        </div>

        {/* Info Box */}
        {infoVisible && (
          <div className="bg-[#f5f8ff] border border-[#d6e0ef] rounded-md p-3 mb-5 flex items-start gap-2">
            <div className="flex-1">
              <div className="font-semibold text-[#4b4e7a] text-sm">{infoTitle}</div>
              <div className="text-[#555] text-xs">{infoDescription}</div>
            </div>
            <button
              className="text-[#888] hover:text-[#222] text-lg"
              onClick={() => setInfoVisible(false)}
            >
              ×
            </button>
          </div>
        )}

        {/* Topics List - Single Column for Mobile */}
        <div className="flex flex-col gap-4">
          {topics.map(topic => (
            <div key={topic.id} className="bg-white border border-[#e5e7ef] rounded-lg p-3 shadow-sm">
              <div className="flex items-center gap-3 mb-2">
                {/* <div className="w-8 h-8 rounded-full bg-[#223] flex items-center justify-center text-white font-semibold text-sm">
                  {topic.title[0]}
                </div> */}
                <div className="flex-1">
                  <div className="font-normal text-sm">{topic.title}</div>
                  {/* {topic.subtitle && <div className="text-xs text-[#888]">{topic.subtitle}</div>} */}
                </div>
              </div>
              <div className="text-[#555] mb-3">
                {(() => {
                  let parsed = topic.data;
                  if (typeof parsed === 'string') {
                    try {
                      parsed = JSON.parse(parsed);
                    } catch (e) {
                      // Not JSON, leave as is
                    }
                  }
                  
                  // Handle password data format
                  if (typeof topic.data === 'string' && topic.data.includes('Service:') && topic.data.includes('Account')) {
                    return (
                      <div className="text-sm font-normal space-y-1">
                        {topic.data.split('\n').map((line, index) => {
                          if (line.trim() === '') return null;
                          if (line.includes('Service:')) {
                            return <div key={index} className="font-semibold text-blue-600">{line}</div>;
                          }
                          if (line.includes('Account')) {
                            return <div key={index} className="font-medium text-gray-700">{line}</div>;
                          }
                          if (line.includes('Username:') || line.includes('Password:')) {
                            return <div key={index} className="text-gray-600 ml-2">{line}</div>;
                          }
                          return <div key={index} className="text-gray-600">{line}</div>;
                        })}
                      </div>
                    );
                  }
                  
                  if (
                    Array.isArray(parsed) &&
                    parsed.length > 0 &&
                    typeof parsed[0] === 'object' &&
                    parsed[0] !== null &&
                    'name' in parsed[0] &&
                    ('info' in parsed[0] || 'phone' in parsed[0])
                  ) {
                    return (
                      <ul className="list-disc pl-5">
                        {parsed.map((contact: any, idx: number) => (
                          <li key={idx}><strong>{contact.name}</strong>: {contact.info || contact.phone}</li>
                        ))}
                      </ul>
                    );
                  }
                  if (
                    parsed &&
                    typeof parsed === 'object' &&
                    'name' in parsed &&
                    'info' in parsed
                  ) {
                    return <span><strong>{(parsed as any).name}</strong>: {(parsed as any).info}</span>;
                  }
                  return typeof topic.data === 'string' ? topic.data : JSON.stringify(topic.data);
                })()}
              </div>
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={topic.onEdit}
                >
                  Edit
                </Button>
              </div>
            </div>
          ))}
        </div>
        {afterTopics && (
          <div className="flex justify-end mt-6">{afterTopics}</div>
        )}
        <Footer />
      </div>
    </>
  );
};

export default CategoryReviewPage;
