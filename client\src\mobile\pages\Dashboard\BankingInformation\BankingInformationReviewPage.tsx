import { useState, useEffect } from 'react';
import CategoryReviewPage from '@/mobile/components/category/CategoryReviewPage';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  UserInput as ReduxUserInput
} from '@/store/slices/bankingInformationSlice';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import bankingData from '@/data/banking.json';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Button } from '@/components/ui/button';

// Define interfaces for the data structure
interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

interface UserInput {
  userId: string;
  categoryId: string;
  originalCategoryId: string;
  subCategoryId: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
}

// Map section IDs to their routes
const subCategoryRoutes: Record<string, string> = {
  '405': 'bankdetails',
};

// Map question IDs to their section IDs
const questionToSubcategoryMap: Record<string, string> = {};

// Initialize the question to section mapping
Object.entries(bankingData).forEach(([subcategoryId, questions]) => {
  questions.forEach((question: any) => {
    questionToSubcategoryMap[question.id] = subcategoryId;
  });
});

const BankingInformationReviewPage = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { categoryName } = useParams();
  const { user } = useAuth();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);
  const [error, setError] = useState<string | null>(null);

  // Get data from Redux store
  const userInputs = useAppSelector((state: any) => state.bankingInformation.userInputs) as ReduxUserInput[];
  const isLoading = useAppSelector((state: any) => state.bankingInformation.loading);

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: user?.image || avatar
  };

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs to create topics for review page
  useEffect(() => {    
    if (!isLoading && userInputs.length > 0) {
      // Transform the data for the review page
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      // Process all user inputs
      userInputs.forEach((userInput: ReduxUserInput) => {        
        userInput.answersBySection.forEach((section) => {
          section.answers.forEach((answer) => {
            // Find the original question from our data
            const questionId = answer.originalQuestionId;
            const subcategoryId = questionToSubcategoryMap[questionId];
            const allQuestions = bankingData[subcategoryId as keyof typeof bankingData];
            const questionData = allQuestions?.find((q: any) => q.id === questionId);

            if (questionData) {
              let displayAnswer = answer.answer;
              
              // Handle choice type answers
              if (questionData.type === 'choice') {
                // If the answer is "Other", look for additional details
                if (answer.answer === "Other") {
                  const otherAnswer = section.answers.find(a => a.originalQuestionId === `${answer.originalQuestionId}_other`);
                  if (otherAnswer) {
                    displayAnswer = `Other: ${otherAnswer.answer}`;
                  }
                }
              }

              allTopics.push({
                id: questionId,
                title: questionData.text,
                subtitle: `Section: ${section.originalSectionId}`,
                data: displayAnswer,
                onEdit: () => {
                  // Navigate to the appropriate section page with question ID as a parameter
                  const route = subCategoryRoutes[subcategoryId];
                  if (route) {
                    const basePath = `/category/${categoryName || 'bankinginformation'}/${route}`;
                    navigate(`${basePath}?questionId=${questionId}&fromReview=1`);
                  }
                }
              });
            }
          });
        });
      });

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, navigate, categoryName]);

  if (isLoading) {
    return <div className="p-4 text-center">Loading your answers...</div>;
  }

  if (error) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!user || !user.id) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertDescription>You must be logged in to view your answers</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (userInputs.length === 0 && !isLoading) {
    return (
      <div className="p-4">
        <Alert>
          <AlertDescription>No banking information answers found. Please complete some questions first.</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <CategoryReviewPage
      categoryTitle="Banking Information"
      infoTitle="How to edit your information"
      infoDescription="Review the details about your banking information. Tap Edit on any item to update it."
      topics={topics}
      onPrint={() => window.print()}
      afterTopics={
        <Button 
          onClick={() => navigate('/dashboard')}
          className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
        >
          Back to Dashboard
        </Button>
      }
    />
  );
};

export default BankingInformationReviewPage;
