import { useField } from 'formik';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import * as Yup from 'yup';

export interface BaseQuestion {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  isAnswered?: boolean;
  answer?: any;
  dependsOn?: string;
  dependsOnValue?: string;
}

export interface TextQuestion extends BaseQuestion {
  type: 'text' | 'email' | 'password';
}

export interface TextareaQuestion extends BaseQuestion {
  type: 'textarea';
}

export interface BooleanQuestion extends BaseQuestion {
  type: 'boolean';
}

export interface DropdownQuestion extends BaseQuestion {
  type: 'dropdown';
  options: string[];
}

export type Question = TextQuestion | TextareaQuestion | BooleanQuestion | DropdownQuestion;

// Form field components
const TextField = ({ question }: { question: TextQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="space-y-2">
      <Label htmlFor={question.id} className="text-sm font-medium text-gray-700">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Input
        {...field}
        id={question.id}
        type={question.type}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
        placeholder={`Enter ${question.text.toLowerCase()}`}
      />
      {meta.touched && meta.error && (
        <div className="text-red-500 text-sm">{meta.error}</div>
      )}
    </div>
  );
};

const TextareaField = ({ question }: { question: TextareaQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="space-y-2">
      <Label htmlFor={question.id} className="text-sm font-medium text-gray-700">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Textarea
        {...field}
        id={question.id}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
        placeholder={`Enter ${question.text.toLowerCase()}`}
        rows={4}
      />
      {meta.touched && meta.error && (
        <div className="text-red-500 text-sm">{meta.error}</div>
      )}
    </div>
  );
};

const BooleanField = ({ question }: { question: BooleanQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  
  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium text-gray-700">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <RadioGroup
        value={field.value}
        onValueChange={(value) => helpers.setValue(value)}
        className="flex flex-row space-x-6"
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="yes" id={`${question.id}-yes`} />
          <Label htmlFor={`${question.id}-yes`}>Yes</Label>
        </div>
        <div className="flex items-center space-x-2">
          <RadioGroupItem value="no" id={`${question.id}-no`} />
          <Label htmlFor={`${question.id}-no`}>No</Label>
        </div>
      </RadioGroup>
      {meta.touched && meta.error && (
        <div className="text-red-500 text-sm">{meta.error}</div>
      )}
    </div>
  );
};

const DropdownField = ({ question }: { question: DropdownQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  
  return (
    <div className="space-y-2">
      <Label htmlFor={question.id} className="text-sm font-medium text-gray-700">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Select
        value={field.value}
        onValueChange={(value) => helpers.setValue(value)}
      >
        <SelectTrigger className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}>
          <SelectValue placeholder={`Select ${question.text.toLowerCase()}`} />
        </SelectTrigger>
        <SelectContent>
          {question.options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {meta.touched && meta.error && (
        <div className="text-red-500 text-sm">{meta.error}</div>
      )}
    </div>
  );
};

// Helper function to check if a question should be visible based on dependencies
export const isQuestionVisible = (question: Question, values: Record<string, any>): boolean => {
  if (!question.dependsOn) return true;
  
  const dependentValue = values[question.dependsOn];
  if (!dependentValue) return false;
  
  // For boolean questions, check if the dependent question is answered "yes"
  if (typeof dependentValue === 'string' && dependentValue.toLowerCase() === 'yes') {
    return true;
  }
  
  // For dropdown questions with specific values
  if (question.dependsOnValue && dependentValue === question.dependsOnValue) {
    return true;
  }
  
  // For dropdown questions without specific values, show if any option is selected
  if (!question.dependsOnValue && dependentValue && dependentValue !== '') {
    return true;
  }
  
  return false;
};

// Helper function to handle dependent answers
export const handleDependentAnswers = (
  questionId: string,
  value: any,
  questions: Question[],
  setFieldValue: (field: string, value: any) => void
) => {
  // Find all questions that depend on this question
  const dependentQuestions = questions.filter(q => q.dependsOn === questionId);
  
  // If the answer is "no" or empty, clear dependent answers
  if (!value || value.toLowerCase() === 'no') {
    dependentQuestions.forEach(depQ => {
      setFieldValue(depQ.id, '');
    });
  }
};

// Main QuestionItem component
export const QuestionItem = ({ question, values }: { question: Question; values: Record<string, any> }) => {
  if (!isQuestionVisible(question, values)) {
    return null;
  }

  const renderField = () => {
    switch (question.type) {
      case 'boolean':
        return <BooleanField question={question as BooleanQuestion} />;
      case 'dropdown':
        return <DropdownField question={question as DropdownQuestion} />;
      case 'textarea':
        return <TextareaField question={question as TextareaQuestion} />;
      case 'text':
      case 'email':
      case 'password':
      default:
        return <TextField question={question as TextQuestion} />;
    }
  };

  return (
    <div className="mb-4">
      {renderField()}
    </div>
  );
};

// Helper functions
export const buildValidationSchema = (questions: Question[]) => {
  const schema: Record<string, any> = {};

  questions.forEach((question) => {
    // For subscription information, all fields are optional
    schema[question.id] = Yup.string().nullable();
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: Question[], existingValues?: Record<string, any>) => {
  const initialValues: Record<string, any> = {};

  questions.forEach((question) => {
    initialValues[question.id] = existingValues?.[question.id] || '';
  });

  return initialValues;
};

// Calculate progress for subscription questions
export const calculateProgress = (questions: Question[], values: Record<string, any>) => {
  // Special handling for OtherServices (section 1104A)
  const isOtherServices = questions.some(q => q.sectionId === '1104A');

  if (isOtherServices) {
    // For OtherServices, progress is based on the services array stored in values.services
    const services = values.services || [];

    if (!Array.isArray(services) || services.length === 0) {
      // No services selected - only the dropdown question counts
      return {
        totalQuestions: 1,
        answeredQuestions: 0,
        completionPercentage: 0
      };
    }

    // Count services with complete account information
    let totalAccounts = 0;
    let completedAccounts = 0;

    services.forEach((service: any) => {
      if (service.accounts && Array.isArray(service.accounts)) {
        service.accounts.forEach((account: any) => {
          totalAccounts++;
          if (account.username && account.password) {
            completedAccounts++;
          }
        });
      }
    });

    // If no accounts, consider just having services as partial progress
    if (totalAccounts === 0) {
      return {
        totalQuestions: 1,
        answeredQuestions: services.length > 0 ? 1 : 0,
        completionPercentage: services.length > 0 ? 50 : 0 // 50% for having services but no credentials
      };
    }

    return {
      totalQuestions: totalAccounts,
      answeredQuestions: completedAccounts,
      completionPercentage: Math.round((completedAccounts / totalAccounts) * 100)
    };
  }

  // Original logic for other sections
  let totalQuestions = 0;
  let answeredQuestions = 0;

  questions.forEach((question) => {
    // Only count questions that are visible (not dependent on other answers)
    if (isQuestionVisible(question, values)) {
      totalQuestions++;

      const value = values[question.id];
      if (value && value.toString().trim() !== '') {
        answeredQuestions++;
      }
    }
  });

  return {
    totalQuestions,
    answeredQuestions,
    completionPercentage: totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0
  };
};
