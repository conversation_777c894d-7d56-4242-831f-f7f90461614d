"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireSubscriptionType = exports.requirePermission = exports.requireRole = exports.requireOwnerAccess = exports.requireCategoryEditAccess = exports.requireCategoryAccess = exports.accessControlMiddleware = void 0;
const User_1 = __importDefault(require("../models/User"));
const Owner_1 = __importDefault(require("../models/Owner"));
const InvitedUser_1 = __importDefault(require("../models/InvitedUser"));
const Role_1 = require("../types/Role");
const email_1 = require("../utils/email");
// Emergency access wait time in milliseconds (24 hours)
const EMERGENCY_ACCESS_WAIT_TIME = 24 * 60 * 60 * 1000;
// Check if emergency access should be auto-granted
const shouldAutoGrantEmergencyAccess = (user) => {
    if (!user.emergencyAccessRequested || user.emergencyAccessRequestedAt === null) {
        return false;
    }
    const requestTime = new Date(user.emergencyAccessRequestedAt).getTime();
    const currentTime = Date.now();
    const timeElapsed = currentTime - requestTime;
    return timeElapsed >= EMERGENCY_ACCESS_WAIT_TIME && !user.emergencyAccessDenied;
};
// Auto-grant emergency access if wait time has passed
const autoGrantEmergencyAccess = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield User_1.default.findById(userId);
        if (user && shouldAutoGrantEmergencyAccess(user)) {
            user.emergencyAccessGranted = true;
            user.emergencyAccessGrantedAt = new Date();
            yield user.save();
            // Send email notification to key holder
            if (user.ownerId) {
                const ownerRecord = yield Owner_1.default.findById(user.ownerId);
                const ownerName = (ownerRecord === null || ownerRecord === void 0 ? void 0 : ownerRecord.firstName) || (ownerRecord === null || ownerRecord === void 0 ? void 0 : ownerRecord.username) || 'Owner';
                const keyHolderName = user.firstName || user.username || user.email || 'Key Holder';
                (0, email_1.sendEmergencyAccessAutoGrantEmail)(user.email, keyHolderName, ownerName)
                    .then(() => console.log('Emergency access auto-grant email sent to key holder.'))
                    .catch((err) => console.error('Failed to send emergency access auto-grant email:', err));
            }
        }
    }
    catch (error) {
        console.error('Error auto-granting emergency access:', error);
    }
});
// Get user role and permissions
const getUserRoleAndPermissions = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield User_1.default.findById(userId).populate('roleId');
        if (!user || !user.roleId) {
            return { permissions: [] };
        }
        const role = user.roleId;
        return {
            role: role.name,
            permissions: role.permissions || []
        };
    }
    catch (error) {
        console.error('Error getting user role and permissions:', error);
        return { permissions: [] };
    }
});
// Check if user is an owner
const isUserOwner = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const owner = yield Owner_1.default.findOne({ userId });
        return !!owner;
    }
    catch (error) {
        console.error('Error checking if user is owner:', error);
        return false;
    }
});
// Check if user is invited by an owner
const isUserInvited = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const invitedUser = yield InvitedUser_1.default.findOne({
            invitedUserId: userId,
            status: 'ACCEPTED' // Only consider accepted invitations
        });
        return !!invitedUser;
    }
    catch (error) {
        console.error('Error checking if user is invited:', error);
        return false;
    }
});
// Main access control middleware
const accessControlMiddleware = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user) {
            res.status(401).json({ message: 'Authentication required' });
            return;
        }
        const user = yield User_1.default.findById(req.user._id).populate('roleId');
        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }
        // Get user role and permissions
        const { role: userRole, permissions: userPermissions } = yield getUserRoleAndPermissions(req.user._id.toString());
        // Check if user is an owner
        const isOwner = yield isUserOwner(req.user._id.toString());
        // Check if user is invited
        const isInvited = yield isUserInvited(req.user._id.toString());
        // Auto-grant emergency access if needed (for spare_key users)
        if (user.subscriptionType === 'spare_key' && !isOwner) {
            yield autoGrantEmergencyAccess(user._id.toString());
            // Refresh user data after potential update
            const updatedUser = yield User_1.default.findById(user._id);
            if (updatedUser) {
                Object.assign(user, updatedUser);
            }
        }
        // Determine access permissions based on subscription type, role, and owner status
        let canAccessCategory;
        let canEditCategory;
        // First check subscription-based restrictions
        switch (user.subscriptionType) {
            case 'temporary_key':
                canAccessCategory = (categoryId) => {
                    // Temporary Key users can access Home Instructions and Quick Start
                    return categoryId === '1' || categoryId === '7';
                };
                canEditCategory = (categoryId) => {
                    // Temporary Key users can edit Home Instructions and Quick Start
                    return categoryId === '1' || categoryId === '7';
                };
                break;
            case 'spare_key':
                if (isOwner) {
                    // Owner has full access regardless of role
                    canAccessCategory = () => true;
                    canEditCategory = () => true;
                }
                else {
                    // Key holder needs emergency access
                    const hasEmergencyAccess = user.emergencyAccessGranted || shouldAutoGrantEmergencyAccess(user);
                    canAccessCategory = () => hasEmergencyAccess;
                    canEditCategory = () => false; // Key holders can only view, not edit
                }
                break;
            case 'all_access_key':
                // Check role-based permissions for all_access_key users
                if (userRole === Role_1.RoleType.OWNER) {
                    canAccessCategory = () => true;
                    canEditCategory = () => true;
                }
                else if (userRole === Role_1.RoleType.NOMINEE) {
                    canAccessCategory = () => userPermissions.includes('canViewAll');
                    canEditCategory = () => userPermissions.includes('canEditAll');
                }
                else if (userRole === Role_1.RoleType.FAMILY) {
                    canAccessCategory = () => userPermissions.includes('canViewAll');
                    canEditCategory = () => userPermissions.includes('canEditAll');
                }
                else {
                    // Default for all_access_key without specific role
                    canAccessCategory = () => true;
                    canEditCategory = () => true;
                }
                break;
            default:
                // Default to no access
                canAccessCategory = () => false;
                canEditCategory = () => false;
        }
        // Attach access control information to request
        req.userAccess = {
            subscriptionType: user.subscriptionType || 'temporary_key',
            allowedCategoryId: user.allowedCategoryId,
            isOwner,
            userRole,
            userPermissions,
            emergencyAccessGranted: user.emergencyAccessGranted || false,
            canAccessCategory,
            canEditCategory
        };
        next();
    }
    catch (error) {
        console.error('Access control middleware error:', error);
        res.status(500).json({ message: 'Access control error' });
    }
});
exports.accessControlMiddleware = accessControlMiddleware;
// Middleware to check category access
const requireCategoryAccess = (categoryIdParam = 'categoryId') => {
    return (req, res, next) => {
        if (!req.userAccess) {
            res.status(401).json({ message: 'Access control not initialized' });
            return;
        }
        const categoryId = req.params[categoryIdParam] || req.body[categoryIdParam];
        if (!categoryId) {
            res.status(400).json({ message: 'Category ID required' });
            return;
        }
        if (!req.userAccess.canAccessCategory(categoryId)) {
            res.status(403).json({
                message: 'Access denied for this category',
                subscriptionType: req.userAccess.subscriptionType,
                userRole: req.userAccess.userRole,
                isOwner: req.userAccess.isOwner
            });
            return;
        }
        next();
    };
};
exports.requireCategoryAccess = requireCategoryAccess;
// Middleware to check category edit permissions
const requireCategoryEditAccess = (categoryIdParam = 'categoryId') => {
    return (req, res, next) => {
        if (!req.userAccess) {
            res.status(401).json({ message: 'Access control not initialized' });
            return;
        }
        const categoryId = req.params[categoryIdParam] || req.body[categoryIdParam];
        if (!categoryId) {
            res.status(400).json({ message: 'Category ID required' });
            return;
        }
        if (!req.userAccess.canEditCategory(categoryId)) {
            res.status(403).json({
                message: 'Edit access denied for this category',
                subscriptionType: req.userAccess.subscriptionType,
                userRole: req.userAccess.userRole,
                isOwner: req.userAccess.isOwner
            });
            return;
        }
        next();
    };
};
exports.requireCategoryEditAccess = requireCategoryEditAccess;
// Middleware to check if user is owner
const requireOwnerAccess = (req, res, next) => {
    if (!req.userAccess) {
        res.status(401).json({ message: 'Access control not initialized' });
        return;
    }
    if (!req.userAccess.isOwner) {
        res.status(403).json({ message: 'Owner access required' });
        return;
    }
    next();
};
exports.requireOwnerAccess = requireOwnerAccess;
// Middleware to check specific role
const requireRole = (requiredRole) => {
    return (req, res, next) => {
        if (!req.userAccess) {
            res.status(401).json({ message: 'Access control not initialized' });
            return;
        }
        if (req.userAccess.userRole !== requiredRole) {
            res.status(403).json({
                message: `Role ${requiredRole} required for this operation`,
                currentRole: req.userAccess.userRole
            });
            return;
        }
        next();
    };
};
exports.requireRole = requireRole;
// Middleware to check specific permission
const requirePermission = (requiredPermission) => {
    return (req, res, next) => {
        var _a;
        if (!req.userAccess) {
            res.status(401).json({ message: 'Access control not initialized' });
            return;
        }
        if (!((_a = req.userAccess.userPermissions) === null || _a === void 0 ? void 0 : _a.includes(requiredPermission))) {
            res.status(403).json({
                message: `Permission ${requiredPermission} required for this operation`,
                currentPermissions: req.userAccess.userPermissions
            });
            return;
        }
        next();
    };
};
exports.requirePermission = requirePermission;
// Middleware to check subscription type
const requireSubscriptionType = (allowedTypes) => {
    return (req, res, next) => {
        if (!req.userAccess) {
            res.status(401).json({ message: 'Access control not initialized' });
            return;
        }
        if (!allowedTypes.includes(req.userAccess.subscriptionType)) {
            res.status(403).json({
                message: `Subscription type ${req.userAccess.subscriptionType} not allowed for this operation`,
                requiredTypes: allowedTypes
            });
            return;
        }
        next();
    };
};
exports.requireSubscriptionType = requireSubscriptionType;
//# sourceMappingURL=accessControlMiddleware.js.map