import mongoose from 'mongoose';
import crypto from 'crypto';
import { IRequestedCategories, IRequestedCategoriesModel, RequestStatus } from '../types/RequestedCategories';

const requestedCategoriesSchema = new mongoose.Schema({
  ownerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Owner',
    required: true
  },
  requestedUserId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  categoryIds: [{
    type: String,
    required: true
  }],
  status: {
    type: String,
    enum: Object.values(RequestStatus),
    default: RequestStatus.PENDING,
    required: true
  },
  requestMessage: {
    type: String,
    trim: true,
    maxlength: 500
  },
  approvalToken: {
    type: String,
    default: null
  },
  approvalTokenExpire: {
    type: Date,
    default: null
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add indexes for better query performance
requestedCategoriesSchema.index({ ownerId: 1 });
requestedCategoriesSchema.index({ requestedUserId: 1 });
requestedCategoriesSchema.index({ status: 1 });
requestedCategoriesSchema.index({ ownerId: 1, requestedUserId: 1 });
requestedCategoriesSchema.index({ approvalToken: 1 });

// Virtual to populate owner data
requestedCategoriesSchema.virtual('owner', {
  ref: 'Owner',
  localField: 'ownerId',
  foreignField: '_id',
  justOne: true
});

// Virtual to populate requested user data
requestedCategoriesSchema.virtual('requestedUser', {
  ref: 'User',
  localField: 'requestedUserId',
  foreignField: '_id',
  justOne: true
});

// Note: categoryIds are stored as strings (numeric IDs from frontend)
// No virtual population needed since we store the IDs directly

// Static method to find requests by owner ID
requestedCategoriesSchema.statics.findByOwnerId = function(ownerId: mongoose.Types.ObjectId) {
  return this.find({ ownerId }).populate('owner').populate('requestedUser');
};

// Static method to find requests by user ID
requestedCategoriesSchema.statics.findByUserId = function(userId: mongoose.Types.ObjectId) {
  return this.find({ requestedUserId: userId }).populate('owner').populate('requestedUser');
};

// Static method to find pending requests
requestedCategoriesSchema.statics.findPendingRequests = function(ownerId?: mongoose.Types.ObjectId) {
  const query = { status: RequestStatus.PENDING };
  if (ownerId) {
    (query as any).ownerId = ownerId;
  }
  return this.find(query).populate('owner').populate('requestedUser');
};

// Static method to find requests by owner and categories
requestedCategoriesSchema.statics.findByOwnerAndCategories = function(ownerId: mongoose.Types.ObjectId, categoryIds: string[]) {
  return this.find({
    ownerId,
    categoryIds: { $in: categoryIds }
  }).populate('owner').populate('requestedUser');
};

// Instance method to generate approval token
requestedCategoriesSchema.methods.generateApprovalToken = function() {
  const token = crypto.randomBytes(32).toString('hex');
  const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

  this.approvalToken = hashedToken;
  this.approvalTokenExpire = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

  return token; // return the raw token (unhashed) to be sent via email
};

const RequestedCategories = mongoose.model<IRequestedCategories, IRequestedCategoriesModel>('RequestedCategories', requestedCategoriesSchema);

export default RequestedCategories;
