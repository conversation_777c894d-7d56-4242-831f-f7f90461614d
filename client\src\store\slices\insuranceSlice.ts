import { createSlice, createAsyncThunk, PayloadAction, createSelector } from '@reduxjs/toolkit';
import userInputService from '../../services/userInputService';
import insuranceData from '../../data/insurance.json';
import { convertUserInputToFormValues } from '@/services/userInputService';

// Define types for our state
export interface SubCategory {
  id: string;
  title: string;
  questionsCount: number;
}

export interface Question {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  options?: string[];
  dependsOn?: {
    questionId: string;
    value: string;
  };
  placeholder?: string;
  validationRules?: {
    minLength?: number;
    maxLength?: number;
  };
}

export interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

export interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

export interface UserInput {
  userId: string;
  categoryId?: string;
  originalCategoryId: string;
  subCategoryId?: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
  _id?: string;
}

interface InsuranceState {
  subcategories: SubCategory[];
  questions: Record<string, Question[]>;
  userInputs: UserInput[];
  loading: boolean;
  error: string | null;
  progressStats: {
    totalQuestions: number;
    answeredQuestions: number;
    completionPercentage: number;
  };
}

// Define initial state
const initialState: InsuranceState = {
  subcategories: [
    { id: '401', title: 'Insurance', questionsCount: (insuranceData['401'] || []).length },
  ],
  questions: insuranceData,
  userInputs: [],
  loading: false,
  error: null,
  progressStats: {
    totalQuestions: Object.values(insuranceData).reduce(
      (sum, questions) => sum + questions.length, 0
    ),
    answeredQuestions: 0,
    completionPercentage: 0
  }
};

// Create async thunks
export const fetchUserInputs = createAsyncThunk<UserInput[], string>(
  'insurance/fetchUserInputs',
  async (userOrOwnerId: string, { rejectWithValue }) => {
    try {
      // Try owner-based fetching first, fallback to user-based
      const response = await userInputService.getUserInputsByCategory(userOrOwnerId, '8', true);
      return response as UserInput[];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user inputs';
      return rejectWithValue(errorMessage);
    }
  }
);

export const saveUserInput = createAsyncThunk<UserInput, Omit<UserInput, '_id'>>(
  'insurance/saveUserInput',
  async (userData: Omit<UserInput, '_id'>, { rejectWithValue }) => {
    try {
      const response = await userInputService.saveUserInput(userData);
      return response as UserInput;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save user input';
      return rejectWithValue(errorMessage);
    }
  }
);

export const updateUserInput = createAsyncThunk<
  UserInput,
  { id: string, userData: Omit<UserInput, '_id'> }
>(
  'insurance/updateUserInput',
  async ({ id, userData }: { id: string, userData: Omit<UserInput, '_id'> }, { rejectWithValue }) => {
    try {
      const response = await userInputService.updateUserInput(id, userData);
      return response as UserInput;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update user input';
      return rejectWithValue(errorMessage);
    }
  }
);

// Create slice
const insuranceSlice = createSlice({
  name: 'insurance',
  initialState,
  reducers: {
    updateProgressStats: (state) => {
      // Calculate total questions
      const totalQuestions = Object.values(state.questions).reduce(
        (sum, questions) => sum + questions.length, 0
      );

      // Calculate answered questions with deduplication
      const answeredQuestionIds = new Set<string>();
      state.userInputs.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
              answeredQuestionIds.add(answer.originalQuestionId);
            }
          });
        });
      });

      const answeredQuestions = answeredQuestionIds.size;

      // Calculate completion percentage
      const completionPercentage = totalQuestions > 0
        ? Math.round((answeredQuestions / totalQuestions) * 100)
        : 0;

      state.progressStats = {
        totalQuestions,
        answeredQuestions,
        completionPercentage
      };
    },
  },
  extraReducers: (builder) => {
    // Handle fetchUserInputs
    builder.addCase(fetchUserInputs.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchUserInputs.fulfilled, (state, action: PayloadAction<UserInput[]>) => {
      state.loading = false;
      state.userInputs = action.payload;
      // Update progress stats after fetching user inputs
      const totalQuestions = state.progressStats.totalQuestions;

      // Calculate answered questions with deduplication
      const answeredQuestionIds = new Set<string>();
      action.payload.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
              answeredQuestionIds.add(answer.originalQuestionId);
            }
          });
        });
      });

      const answeredQuestions = answeredQuestionIds.size;

      const completionPercentage = totalQuestions > 0
        ? Math.round((answeredQuestions / totalQuestions) * 100)
        : 0;

      state.progressStats = {
        totalQuestions,
        answeredQuestions,
        completionPercentage
      };
    });
    builder.addCase(fetchUserInputs.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Handle saveUserInput
    builder.addCase(saveUserInput.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(saveUserInput.fulfilled, (state, action: PayloadAction<UserInput>) => {
      state.loading = false;
      state.userInputs.push(action.payload);
      // Recalculate progress stats with deduplication after saving
      const answeredQuestionIds = new Set<string>();
      state.userInputs.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
              answeredQuestionIds.add(answer.originalQuestionId);
            }
          });
        });
      });

      state.progressStats.answeredQuestions = answeredQuestionIds.size;
      state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
        ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
        : 0;
    });
    builder.addCase(saveUserInput.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Handle updateUserInput
    builder.addCase(updateUserInput.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateUserInput.fulfilled, (state, action: PayloadAction<UserInput>) => {
      state.loading = false;
      const index = state.userInputs.findIndex(input => input._id === action.payload._id);
      if (index !== -1) {
        // Update existing user input
        state.userInputs[index] = action.payload;
      }
      // Recalculate progress stats with deduplication
      const answeredQuestionIds = new Set<string>();
      state.userInputs.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
              answeredQuestionIds.add(answer.originalQuestionId);
            }
          });
        });
      });

      state.progressStats.answeredQuestions = answeredQuestionIds.size;
      state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
        ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
        : 0;
    });
    builder.addCase(updateUserInput.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
  },
});

export const { updateProgressStats } = insuranceSlice.actions;

// Basic selectors
export const selectInsuranceState = (state: { insurance: InsuranceState }) =>
  state.insurance;

export const selectSubcategories = createSelector(
  [selectInsuranceState],
  (insurance) => insurance.subcategories
);

export const selectQuestions = createSelector(
  [selectInsuranceState],
  (insurance) => insurance.questions
);

export const selectUserInputs = createSelector(
  [selectInsuranceState],
  (insurance) => insurance.userInputs
);

export const selectProgressStats = createSelector(
  [selectInsuranceState],
  (insurance) => insurance.progressStats
);

export const selectLoading = createSelector(
  [selectInsuranceState],
  (insurance) => insurance.loading
);

export const selectError = createSelector(
  [selectInsuranceState],
  (insurance) => insurance.error
);

// Memoized selectors with parameters
export const selectSubcategoryById = (subcategoryId: string) =>
  createSelector(
    [selectSubcategories],
    (subcategories) => subcategories.find(subcategory => subcategory.id === subcategoryId)
  );

export const selectQuestionsBySubcategoryId = (subcategoryId: string) =>
  createSelector(
    [selectQuestions],
    (questions) => questions[subcategoryId] || []
  );

export const selectUserInputsBySubcategoryId = (subcategoryId: string) =>
  createSelector(
    [selectUserInputs],
    (userInputs) => userInputs.filter(input => input.originalSubCategoryId === subcategoryId)
  );

export default insuranceSlice.reducer;
