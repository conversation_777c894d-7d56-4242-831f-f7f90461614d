import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import userInputService from '@/services/userInputService';
import subscriptionData from '@/data/subscription.json';
import { calculateProgress } from '@/web/components/Category/Subscription/FormFields';
import { convertUserInputToFormValues } from '@/services/userInputService';

// Types
export interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

export interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

export interface UserInput {
  _id: string;
  userId: string;
  categoryId: string;
  originalCategoryId: string;
  subCategoryId: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
}

export interface Subcategory {
  id: string;
  title: string;
  questionsCount: number;
}

export interface ProgressStats {
  totalQuestions: number;
  answeredQuestions: number;
  completionPercentage: number;
}

export interface SubscriptionCategoryState {
  subcategories: Subcategory[];
  questions: Record<string, any[]>;
  userInputs: UserInput[];
  formValues: Record<string, any>;
  loading: boolean;
  error: string | null;
  progressStats: ProgressStats;
}

// Async thunks
export const fetchUserInputs = createAsyncThunk<UserInput[], string>(
  'subscriptionCategory/fetchUserInputs',
  async (ownerId: string, { rejectWithValue }) => {
    try {
      const response = await userInputService.getUserInputsByCategory(ownerId, '11', true);
      return response as UserInput[];
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch user inputs');
    }
  }
);

export const saveUserInput = createAsyncThunk<UserInput, Omit<UserInput, '_id'>>(
  'subscriptionCategory/saveUserInput',
  async (userData: Omit<UserInput, '_id'>, { rejectWithValue }) => {
    try {
      // Ensure originalCategoryId is '11' for subscription category
      const dataToSave = { ...userData, originalCategoryId: '11' };
      const response = await userInputService.saveUserInput(dataToSave);
      return response as UserInput;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to save user input');
    }
  }
);

export const updateUserInput = createAsyncThunk<
  UserInput,
  { id: string, userData: Omit<UserInput, '_id'> }
>(
  'subscriptionCategory/updateUserInput',
  async ({ id, userData }: { id: string, userData: Omit<UserInput, '_id'> }, { rejectWithValue }) => {
    try {
      // Ensure originalCategoryId is '11' for subscription category
      const dataToUpdate = { ...userData, originalCategoryId: '11' };
      const response = await userInputService.updateUserInput(id, dataToUpdate);
      return response as UserInput;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to update user input');
    }
  }
);

// Define initial state
const initialState: SubscriptionCategoryState = {
  subcategories: [
    {
      id: '1101A',
      title: 'Netflix',
      questionsCount: subscriptionData['1101']?.length || 0
    },
    {
      id: '1102A',
      title: 'Apple TV',
      questionsCount: subscriptionData['1102']?.length || 0
    },
    {
      id: '1103A',
      title: 'Amazon Prime',
      questionsCount: subscriptionData['1103']?.length || 0
    },
    {
      id: '1104A',
      title: 'Other Services',
      questionsCount: subscriptionData['1104']?.length || 0
    }
  ],
  questions: subscriptionData,
  userInputs: [],
  formValues: {},
  loading: false,
  error: null,
  progressStats: {
    totalQuestions: Object.values(subscriptionData).reduce(
      (sum, questions) => sum + questions.length, 0
    ),
    answeredQuestions: 0,
    completionPercentage: 0
  }
};

// Create slice
const subscriptionCategorySlice = createSlice({
  name: 'subscriptionCategory',
  initialState,
  reducers: {
    updateFormValues: (state, action) => {
      state.formValues = { ...state.formValues, ...action.payload };
    },
    updateProgressStats: (state, action) => {
      state.progressStats = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch user inputs
    builder.addCase(fetchUserInputs.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchUserInputs.fulfilled, (state, action) => {
      state.loading = false;
      state.userInputs = action.payload;
      // Calculate progress stats with deduplication
      const answeredQuestionIds = new Set<string>();
      action.payload.forEach(input => {
        input.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
              answeredQuestionIds.add(answer.originalQuestionId);
            }
          });
        });
      });

      state.progressStats.answeredQuestions = answeredQuestionIds.size;
      state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
        ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
        : 0;
    });
    builder.addCase(fetchUserInputs.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Save user input
    builder.addCase(saveUserInput.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(saveUserInput.fulfilled, (state, action) => {
      state.loading = false;
      state.userInputs.push(action.payload);
      // Update progress stats after saving
      state.progressStats.answeredQuestions += action.payload.answersBySection.reduce(
        (sum, section) => sum + section.answers.length, 0
      );
      state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
        ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
        : 0;
    });
    builder.addCase(saveUserInput.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Update user input
    builder.addCase(updateUserInput.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateUserInput.fulfilled, (state, action) => {
      state.loading = false;
      const index = state.userInputs.findIndex(input => input._id === action.payload._id);
      if (index !== -1) {
        state.userInputs[index] = action.payload;
      }
      // Recalculate progress stats with deduplication
      const answeredQuestionIds = new Set<string>();
      state.userInputs.forEach(input => {
        input.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
              answeredQuestionIds.add(answer.originalQuestionId);
            }
          });
        });
      });

      state.progressStats.answeredQuestions = answeredQuestionIds.size;
      state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
        ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
        : 0;
    });
    builder.addCase(updateUserInput.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
  },
});

export const { updateFormValues, updateProgressStats } = subscriptionCategorySlice.actions;

// Basic selectors
export const selectSubscriptionCategoryState = (state: { subscriptionCategory: SubscriptionCategoryState }) =>
  state.subscriptionCategory;

export const selectSubcategories = createSelector(
  [selectSubscriptionCategoryState],
  (subscriptionCategory) => subscriptionCategory.subcategories
);

export const selectQuestions = createSelector(
  [selectSubscriptionCategoryState],
  (subscriptionCategory) => subscriptionCategory.questions
);

export const selectUserInputs = createSelector(
  [selectSubscriptionCategoryState],
  (subscriptionCategory) => subscriptionCategory.userInputs
);

export const selectFormValues = createSelector(
  [selectSubscriptionCategoryState],
  (subscriptionCategory) => subscriptionCategory.formValues
);

export const selectLoading = createSelector(
  [selectSubscriptionCategoryState],
  (subscriptionCategory) => subscriptionCategory.loading
);

export const selectError = createSelector(
  [selectSubscriptionCategoryState],
  (subscriptionCategory) => subscriptionCategory.error
);

export const selectProgressStats = createSelector(
  [selectSubscriptionCategoryState],
  (subscriptionCategory) => subscriptionCategory.progressStats
);

// Advanced selectors
export const selectQuestionsBySubcategoryId = (subcategoryId: string) =>
  createSelector([selectQuestions], (questions) => {
    return questions[subcategoryId] || [];
  });

export const selectUserInputsBySubcategoryId = (subcategoryId: string) =>
  createSelector([selectUserInputs], (userInputs) => {
    return userInputs.filter(input => input.originalSubCategoryId === subcategoryId);
  });

// Dynamic overall progress selector
export const selectDynamicOverallProgress = createSelector(
  [selectQuestions, selectUserInputs],
  (questionsBySub, userInputs) => {
    let totalQuestions = 0;
    let answeredQuestions = 0;

    // Map question group keys to section IDs
    const keyToSectionMap: Record<string, string> = {
      '1101': '1101A',
      '1102': '1102A',
      '1103': '1103A',
      '1104': '1104A'
    };

    // For each subcategory
    Object.entries(questionsBySub).forEach(([subId, questions]) => {
      // Map the question group key to the section ID used in user inputs
      const sectionId = keyToSectionMap[subId];
      if (!sectionId) return;

      // Find user answers for this subcategory using the correct section ID
      const userInput = userInputs.find(input => input.originalSubCategoryId === sectionId);
      const savedAnswers = userInput ? convertUserInputToFormValues(userInput) : {};

      // Use the same progress logic as the UI
      const progress = calculateProgress(
        questions as import('@/web/components/Category/Subscription/FormFields').Question[],
        savedAnswers
      );
      totalQuestions += progress.totalQuestions;
      answeredQuestions += progress.answeredQuestions;
    });

    const completionPercentage = totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0;
    return { totalQuestions, answeredQuestions, completionPercentage };
  }
);

export default subscriptionCategorySlice.reducer;
