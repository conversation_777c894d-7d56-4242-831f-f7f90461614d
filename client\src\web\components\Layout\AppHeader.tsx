import logo from '@/assets/global/logo.png'
import { InviteDialog } from "@/components/common/InviteDialog"
import { RequestCategoryDialog } from "@/components/RequestCategories/RequestCategoryDialog"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/contexts/AuthContext"
import { useToast } from "@/contexts/ToastContext"
import { Bell, LogOut, User } from "lucide-react"
import { Link, useNavigate } from "react-router-dom"
import { useAccessControl } from '@/hooks/useAccessControl';
import { Mail } from 'lucide-react';

export default function AppHeader() {
  const { user, logout, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { userAccess, keyHolders, handleRequestEmergencyAccess } = useAccessControl();

  // Show green dot if owner and any keyHolder has a pending emergency access request (requested, but not granted or denied)
  const showEmergencyDot = userAccess?.isOwner && keyHolders && keyHolders.some(
    kh => kh.emergencyAccessRequested && !kh.emergencyAccessGranted && !kh.emergencyAccessDenied
  );

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out successfully",
        description: "You have been logged out",
        variant: "custom"
      });
      navigate('/auth/login');
    } catch (error) {
      console.error('Logout error:', error);
      toast({
        title: "Logout failed",
        description: "Please try again",
        variant: "custom"
      });
    }
  };

  return (
    <>
      <header className="bg-white text-[#1F4168] shadow-sm fixed top-0 left-0 right-0 z-50 border-b">
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center h-20 justify-between">
          <div className="flex items-center space-x-8">
            <Link to="/">
              <img 
                src={logo} 
                alt="Heirkey Logo" 
                className="h-16 w-auto" 
              />
            </Link>
            <nav className="flex items-center space-x-6">
              <Link to="/dashboard" className="hover:underline font-medium">Dashboard</Link>
              <Link to="/directory" className="hover:underline font-medium">Directory</Link>
              <Link to="/support" className="hover:underline font-medium">Support</Link>
            </nav>
          </div>
          <div className="flex items-center gap-2">
            {isAuthenticated && (
              <>
                {user?.role === 'Owner' && <InviteDialog />}
                {(user?.role === 'Family' || user?.role === 'Nominee')}
                {/* Emergency Access Button for invited/keyholder users with spare_key */}
                {userAccess &&
                  userAccess.subscriptionType === 'spare_key' &&
                  !userAccess.isOwner &&
                  !userAccess.emergencyAccessRequested &&
                  !userAccess.emergencyAccessGranted &&
                  !userAccess.emergencyAccessDenied && (
                    <Button
                      variant="outline"
                      className="font-medium text-blue-700 border-blue-400"
                      onClick={handleRequestEmergencyAccess}
                    >
                      Request Emergency Access
                    </Button>
                  )}
              </>
            )}
            {isAuthenticated ? (
              <Button variant="outline" className="font-medium" onClick={() => navigate('/subscription')}>Subscription</Button>
            ) : (
              <Button variant="outline" className="font-medium" onClick={() => navigate('/auth/subscribe')}>Subscribe</Button>
            )}

            {isAuthenticated && (
              <>
                <div className="relative">
                  <Button
                    variant="ghost"
                    className="flex items-center gap-2 font-medium"
                    onClick={() => navigate('/auth/user-profile')}
                  >
                    <User className="w-4 h-4 mr-1" />
                    Profile
                  </Button>
                  {showEmergencyDot && (
                    <span className="absolute top-1 right-1 block h-3 w-3 rounded-full bg-green-500 border-2 border-white z-10" />
                  )}
                </div>
                <Button
                  variant="ghost"
                  className="flex items-center gap-2 text-red-500 hover:text-red-600 font-medium"
                  onClick={handleLogout}
                >
                  <LogOut className="w-4 h-4 mr-1" /> Logout
                </Button>
              </>
            )}

            <Button variant="ghost" size="icon"><Bell className="w-5 h-5" /></Button>
          </div>
        </div>
      </header>
    </>
  )
}
