import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik, Form, FieldArray } from "formik";
import * as Yup from 'yup';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import subscriptionData from '@/data/subscription.json';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId,
  updateUserInput,
  selectError,
} from '@/store/slices/subscriptionCategorySlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Plus, Trash2 } from 'lucide-react';

const OTHER_SERVICES_SECTION_ID = '1104';
const DROPDOWN_QUESTION_ID = 's10';

interface ServiceEntry {
  serviceName: string;
  customServiceName?: string;
  displayName?: string;
  accounts: AccountEntry[];
}

interface AccountEntry {
  username: string;
  password: string;
}

interface FormValues {
  services: ServiceEntry[];
  [key: string]: any;
}

export default function OtherServicesPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [selectedServices, setSelectedServices] = useState<ServiceEntry[]>([]);
  const [duplicateServiceError, setDuplicateServiceError] = useState<string>('');
  const [formError, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get data from Redux store
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId('1104A')(state));
  const isLoadingRedux = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);
  const storeQuestions = useAppSelector(selectQuestionsBySubcategoryId(OTHER_SERVICES_SECTION_ID));

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');
  const fromReview = queryParams.get('fromReview');
  const serviceIndex = queryParams.get('serviceIndex'); // New parameter for service index

  // Get dropdown options from the question
  const dropdownQuestion = storeQuestions.find(q => q.id === DROPDOWN_QUESTION_ID);
  const dropdownOptions = dropdownQuestion?.options || [];

  const userInput = userInputs.find((input: UserInput) => input.originalSubCategoryId === '1104A');

  // State for highlighting services
  const [highlightedServiceIndex, setHighlightedServiceIndex] = useState<number | null>(null);

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Load saved services from user input
  useEffect(() => {
    if (userInput) {
      const formValues = convertUserInputToFormValues(userInput);
      if (formValues.services && Array.isArray(formValues.services)) {
        setSelectedServices(formValues.services);
      } else {
        setSelectedServices([]);
      }
    }
  }, [userInput]);

  // Handle service highlighting when navigating from review page
  useEffect(() => {
    if (!isLoading && serviceIndex !== null && selectedServices.length > 0) {
      const index = parseInt(serviceIndex);
      if (index >= 0 && index < selectedServices.length) {
        setHighlightedServiceIndex(index);
        
        // Scroll to the specific service after a delay
        setTimeout(() => {
          const serviceElement = document.getElementById(`service-${index}`);
          if (serviceElement) {
            serviceElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            serviceElement.classList.add('bg-blue-50', 'border-blue-300');
            setTimeout(() => {
              serviceElement.classList.remove('bg-blue-50', 'border-blue-300');
              setHighlightedServiceIndex(null);
            }, 3000);
          }
        }, 1000);
      }
    }
  }, [isLoading, serviceIndex, selectedServices]);

  // Handle question highlighting when navigating from review page
  useEffect(() => {
    if (!isLoading && targetQuestionId) {
      setTimeout(() => {
        // Always scroll to the dropdown question when navigating from review page
        const element = document.getElementById(`question-${DROPDOWN_QUESTION_ID}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.classList.add('bg-yellow-100');
          setTimeout(() => {
            element.classList.remove('bg-yellow-100');
          }, 2000);
        }
      }, 500);
    }
  }, [isLoading, targetQuestionId]);

  // Show loading state if data is being fetched
  if (isLoading || isLoadingRedux) {
    return (
      <>
        <GradiantHeader title="Subscription Services" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  const checkDuplicateService = (serviceName: string, customServiceName?: string): boolean => {
    return selectedServices.some(service => {
      if (serviceName === 'Other') {
        // For "Other" services, only check if the exact same custom name exists
        return service.serviceName === 'Other' && 
               service.customServiceName && 
               customServiceName && 
               service.customServiceName.toLowerCase() === customServiceName.toLowerCase();
      }
      // For standard services, check if the service name already exists
      return service.serviceName === serviceName;
    });
  };

  const handleServiceDelete = async (idx: number) => {
    try {
      if (!user || !user.id) {
        throw new Error('You must be logged in to save changes');
      }

      // Remove from local state first
      const newServices = selectedServices.filter((_: ServiceEntry, index: number) => index !== idx);
      setSelectedServices(newServices);

      // Filter out empty services
      const validServices = newServices.filter(service => 
        service.serviceName && (service.accounts.length > 0)
      ).map(service => {
        // For "Other" services, use the custom name if available
        if (service.serviceName === 'Other' && service.customServiceName) {
          return {
            ...service,
            displayName: service.customServiceName
          };
        }
        return service;
      });

      const answersBySection = storeQuestions
        .reduce((sections: Record<string, Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>>, question) => {
          if (!sections[question.sectionId]) {
            sections[question.sectionId] = [];
          }
          
          if (question.id === DROPDOWN_QUESTION_ID) {
            // Save the updated services array as JSON
            sections[question.sectionId].push({
              index: sections[question.sectionId].length,
              originalQuestionId: question.id,
              question: question.text,
              type: 'choice',
              answer: JSON.stringify(validServices)
            });
          }
          return sections;
        }, {});

      const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
        originalSectionId: sectionId,
        isCompleted: true,
        answers: answers as Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>
      }));

      // Save to database
      if (userInput?._id) {
        await dispatch(updateUserInput({
          id: userInput._id,
          userData: {
            userId: user.id,
            categoryId: generateObjectId(),
            originalCategoryId: '11',
            subCategoryId: generateObjectId(),
            originalSubCategoryId: '1104A',
            answersBySection: formattedAnswersBySection
          }
        })).unwrap();
      } else {
        await dispatch(saveUserInput({
          userId: user.id,
          categoryId: generateObjectId(),
          originalCategoryId: '11',
          subCategoryId: generateObjectId(),
          originalSubCategoryId: '1104A',
          answersBySection: formattedAnswersBySection
        })).unwrap();
      }
    } catch (error: any) {
      console.error('Error saving after service deletion:', error);
      // Revert the local state if save fails
      setSelectedServices(selectedServices);
    }
  };

  const handleSubmit = async (values: FormValues, { setSubmitting }: any) => {
    try {
      if (!user || !user.id) {
        setError('You must be logged in to save answers');
        return;
      }

      const ownerId = await getCachedOwnerIdFromUser(user);
      if (!ownerId) {
        setError('Owner ID not found');
        return;
      }

      // Filter out empty services
      const validServices = selectedServices.filter(service => 
        service.serviceName && (service.accounts.length > 0)
      ).map(service => {
        // For "Other" services, use the custom name if available
        if (service.serviceName === 'Other' && service.customServiceName) {
          return {
            ...service,
            displayName: service.customServiceName
          };
        }
        return service;
      });

      const answersBySection = storeQuestions
        .reduce((sections: Record<string, Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>>, question) => {
          if (!sections[question.sectionId]) {
            sections[question.sectionId] = [];
          }
          
          if (question.id === DROPDOWN_QUESTION_ID) {
            // Save the services array as JSON
            sections[question.sectionId].push({
              index: sections[question.sectionId].length,
              originalQuestionId: question.id,
              question: question.text,
              type: 'choice',
              answer: JSON.stringify(validServices)
            });
          }
          return sections;
        }, {});

      const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
        originalSectionId: sectionId,
        isCompleted: true,
        answers: answers as Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>
      }));

      if (userInput?._id) {
        await dispatch(updateUserInput({
          id: userInput._id,
          userData: {
            userId: user.id,
            categoryId: generateObjectId(),
            originalCategoryId: '11',
            subCategoryId: generateObjectId(),
            originalSubCategoryId: '1104A',
            answersBySection: formattedAnswersBySection
          }
        })).unwrap();
      } else {
        await dispatch(saveUserInput({
          userId: user.id,
          categoryId: generateObjectId(),
          originalCategoryId: '11',
          subCategoryId: generateObjectId(),
          originalSubCategoryId: '1104A',
          answersBySection: formattedAnswersBySection
        })).unwrap();
      }

      // Refresh data after saving
      await dispatch(fetchUserInputs(ownerId));
      
      if (fromReview) {
        navigate('/category/subscription/review');
      } else {
        navigate("/category/subscription/review");
      }
      setSubmitting(false);
    } catch (err) {
      console.error('Error saving Other Services data:', err);
      setError("Failed to save your answers. Please try again.");
      setSubmitting(false);
    }
  };

  // Create validation schema for services
  const validationSchema = Yup.object().shape({
    services: Yup.array().of(
      Yup.object().shape({
        serviceName: Yup.string(),
        accounts: Yup.array().of(
          Yup.object().shape({
            username: Yup.string(),
            password: Yup.string()
          })
        )
      })
    )
  });

  const initialValues = {
    services: selectedServices.length > 0 ? selectedServices : []
  };

  return (
    <>
      <GradiantHeader title="Subscription Services" showAvatar={true} />
      <div className="p-4">
        {/* Tab Bar */}
        <div className="flex flex-row flex-nowrap gap-3 mb-4 bg-gray-50 rounded-lg p-1 overflow-x-auto scrollbar-hide">
          {categoryTabsConfig.subscription.map(tab => {
            const isActive = tab.label === "Other Services";
            return (
              <button
                key={tab.label}
                className={`flex-1 py-1 px-1 rounded-md font-medium whitespace-nowrap ${
                  isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500"
                }`}
                disabled={isActive}
                onClick={() => !isActive && navigate(tab.path)}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Error message */}
        {(formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        {/* Form */}
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, isSubmitting, setFieldValue }) => (
            <Form>
              <div className="bg-gray-50 p-5 rounded-xl shadow-sm border mb-4">
                <div className="flex items-center justify-between">
                  <p className="text-lg font-semibold">
                    Subscription Services: <span className="text-[#2BCFD5]">Other Services</span>
                  </p>
                  <CircularProgress
                    value={selectedServices.length > 0 ? 2 : 1}
                    max={2}
                    size={40}
                    stroke={3}
                    color="#2BCFD5"
                  />
                </div>
              </div>

              <div className="bg-gray-50 p-5 rounded-xl shadow-sm border">
                <Label 
                  className="text-sm font-medium text-gray-700 mb-4 block"
                  id={`question-${DROPDOWN_QUESTION_ID}`}
                >
                  {dropdownQuestion?.text || "Do you have other subscription services?"}
                </Label>

                <FieldArray name="services">
                  {({ push, remove, form }) => {
                    const services = Array.isArray(form.values.services) 
                      ? form.values.services 
                      : [];
                    
                    return (
                      <div>
                        {services.length === 0 && (
                          <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg bg-white">
                            <div className="text-gray-500 mb-4">
                              <Plus size={48} className="mx-auto mb-2 text-gray-400" />
                              <p className="text-lg font-medium">No subscription services added yet</p>
                              <p className="text-sm">Click the button below to add your first subscription service</p>
                            </div>
                            <button
                              type="button"
                              className="flex items-center gap-2 mx-auto bg-[#2BCFD5] hover:bg-[#19bbb5] text-white px-6 py-3 rounded-lg font-medium"
                              onClick={() => {
                                const newService = { serviceName: '', accounts: [{ username: '', password: '' }] };
                                push(newService);
                                setSelectedServices([...services, newService]);
                              }}
                            >
                              <Plus size={20} /> Add Your First Service
                            </button>
                          </div>
                        )}

                        {services.map((service: ServiceEntry, idx: number) => (
                          <div 
                            key={idx} 
                            className={`border rounded-lg p-4 mb-4 bg-white ${
                              highlightedServiceIndex === idx ? 'bg-blue-50 border-blue-300' : ''
                            }`}
                            id={`service-${idx}`}
                          >
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="font-medium text-gray-700">Service {idx + 1}</h4>
                              <button
                                type="button"
                                className="text-red-500 hover:text-red-700"
                                onClick={() => {
                                  handleServiceDelete(idx);
                                }}
                                aria-label="Delete service"
                              >
                                <Trash2 size={18} />
                              </button>
                            </div>

                            <div className="space-y-3">
                              <div>
                                <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                  Service Name
                                </Label>
                                <select
                                  value={service.serviceName}
                                  onChange={(e) => {
                                    const value = e.target.value;
                                    
                                    // Check if this service already exists (only for standard services)
                                    if (value !== 'Other') {
                                      const isDuplicate = checkDuplicateService(value);
                                      if (isDuplicate) {
                                        setDuplicateServiceError(`${value} is already added. You can add accounts to that service.`);
                                        setTimeout(() => setDuplicateServiceError(''), 3000);
                                        return;
                                      }
                                    }
                                    
                                    const newServices = [...services];
                                    newServices[idx] = { 
                                      ...service, 
                                      serviceName: value,
                                      customServiceName: value === 'Other' ? '' : undefined,
                                      displayName: value === 'Other' ? '' : value
                                    };
                                    setFieldValue('services', newServices);
                                    setSelectedServices(newServices);
                                    setDuplicateServiceError(''); // Clear any previous error
                                  }}
                                  className="w-full border rounded-lg px-3 py-2"
                                >
                                  <option value="">Select a service</option>
                                  {dropdownOptions.map((option: string) => (
                                    <option key={option} value={option}>
                                      {option}
                                    </option>
                                  ))}
                                </select>
                              </div>
                              
                              {/* Custom service name field for "Other" selection */}
                              {service.serviceName === 'Other' && (
                                <div>
                                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                                    What is your other subscription account?
                                  </Label>
                                  <Input
                                    value={service.customServiceName || ''}
                                    onChange={(e) => {
                                      const customName = e.target.value;
                                      
                                      // Only check for duplicates if a custom name is actually entered
                                      if (customName.trim()) {
                                        const isDuplicate = checkDuplicateService('Other', customName);
                                        if (isDuplicate) {
                                          setDuplicateServiceError(`${customName} is already added. You can add accounts to that service.`);
                                          setTimeout(() => setDuplicateServiceError(''), 3000);
                                          return;
                                        }
                                      }
                                      
                                      const newServices = [...services];
                                      newServices[idx] = { 
                                        ...service, 
                                        customServiceName: customName,
                                        displayName: customName || 'Other'
                                      };
                                      setFieldValue('services', newServices);
                                      setSelectedServices(newServices);
                                      setDuplicateServiceError(''); // Clear any previous error
                                    }}
                                    placeholder="Enter your custom service name"
                                    className="w-full"
                                  />
                                </div>
                              )}
                              
                              {/* Error message for duplicate services */}
                              {duplicateServiceError && (
                                <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                                  <p className="text-sm text-red-700">
                                    ⚠️ {duplicateServiceError}
                                  </p>
                                </div>
                              )}

                              <FieldArray name={`services.${idx}.accounts`}>
                                {({ push: pushAccount, remove: removeAccount, form: accountForm }) => (
                                  <div>
                                    <div className="flex items-center justify-between mb-2">
                                      <Label className="text-sm font-medium text-gray-700">
                                        Accounts
                                      </Label>
                                      <button
                                        type="button"
                                        className="flex items-center gap-1 text-[#2BCFD5] hover:text-[#19bbb5] text-sm"
                                        onClick={() => {
                                          const newAccount = { username: '', password: '' };
                                          pushAccount(newAccount);
                                          const newServices = [...services];
                                          const newAccounts = [...service.accounts];
                                          newAccounts.push(newAccount);
                                          newServices[idx] = { ...service, accounts: newAccounts };
                                          setFieldValue('services', newServices);
                                          setSelectedServices(newServices);
                                        }}
                                      >
                                        <Plus size={16} /> Add Account
                                      </button>
                                    </div>
                                    
                                    {service.accounts.length === 0 && (
                                      <div className="text-gray-400 mb-2">No accounts added for this service.</div>
                                    )}
                                    
                                    {service.accounts.map((account: AccountEntry, accountIdx: number) => (
                                      <div key={accountIdx} className="border rounded-lg p-3 mb-3 bg-gray-50">
                                        <div className="flex items-center justify-between mb-2">
                                          <h5 className="font-medium text-gray-600 text-sm">Account {accountIdx + 1}</h5>
                                          <button
                                            type="button"
                                            className="text-red-500 hover:text-red-700"
                                            onClick={() => removeAccount(accountIdx)}
                                            aria-label="Delete account"
                                          >
                                            <Trash2 size={16} />
                                          </button>
                                        </div>
                                        <div className="space-y-2">
                                          <div>
                                            <Label className="text-xs font-medium text-gray-600 mb-1 block">
                                              Username
                                            </Label>
                                            <Input
                                              value={account.username}
                                              onChange={(e) => {
                                                const newServices = [...services];
                                                const newAccounts = [...service.accounts];
                                                newAccounts[accountIdx] = { ...account, username: e.target.value };
                                                newServices[idx] = { ...service, accounts: newAccounts };
                                                setFieldValue('services', newServices);
                                                setSelectedServices(newServices);
                                              }}
                                              placeholder="Enter username"
                                              className="w-full text-sm"
                                            />
                                          </div>
                                          <div>
                                            <Label className="text-xs font-medium text-gray-600 mb-1 block">
                                              Password
                                            </Label>
                                            <Input
                                              type="password"
                                              value={account.password}
                                              onChange={(e) => {
                                                const newServices = [...services];
                                                const newAccounts = [...service.accounts];
                                                newAccounts[accountIdx] = { ...account, password: e.target.value };
                                                newServices[idx] = { ...service, accounts: newAccounts };
                                                setFieldValue('services', newServices);
                                                setSelectedServices(newServices);
                                              }}
                                              placeholder="Enter password"
                                              className="w-full text-sm"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </FieldArray>
                            </div>
                          </div>
                        ))}

                        {services.length > 0 && (
                          <button
                            type="button"
                            className="flex items-center gap-1 text-[#2BCFD5] hover:text-[#19bbb5] font-medium mt-2"
                            onClick={() => {
                              const newService = { serviceName: '', accounts: [{ username: '', password: '' }] };
                              push(newService);
                              setSelectedServices([...services, newService]);
                            }}
                          >
                            <Plus size={18} /> Add Service
                          </button>
                        )}
                        
                        {/* Helpful message about multiple "Other" services */}
                        {services.length > 0 && (
                          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <p className="text-sm text-blue-700">
                              💡 <strong>Tip:</strong> You can add multiple "Other" services. Each service can have its own custom name and multiple accounts.
                            </p>
                          </div>
                        )}
                      </div>
                    );
                  }}
                </FieldArray>
              </div>

              <div className="mt-6 flex justify-between items-center">
                <div className="text-sm text-gray-600">
                  {selectedServices.length > 0 && (
                    <span>
                      {selectedServices.length} service{selectedServices.length !== 1 ? 's' : ''} added
                      {selectedServices.reduce((total, service) => total + service.accounts.length, 0) > 0 && (
                        <span className="ml-2">
                          • {selectedServices.reduce((total, service) => total + service.accounts.length, 0)} account{selectedServices.reduce((total, service) => total + service.accounts.length, 0) !== 1 ? 's' : ''}
                        </span>
                      )}
                    </span>
                  )}
                </div>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                >
                  {isSubmitting ? 'Saving...' : 'Save & Continue'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      <Footer />
    </>
  );
} 