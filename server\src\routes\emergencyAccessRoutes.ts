import express from 'express';
import {
  requestEmergencyAccess,
  grantEmergencyAccess,
  denyEmergencyAccess,
  getEmergencyAccessStatus,
  getKeyHoldersForOwner
} from '../controller/emergencyAccessController';
import { combinedAuth } from '../middleware/authMiddleware';
import { accessControlMiddleware } from '../middleware/accessControlMiddleware';

const router = express.Router();

// Apply authentication and access control middleware to all routes
router.use(combinedAuth);
router.use(accessControlMiddleware);

// Request emergency access (for spare_key key holders)
router.post('/request', requestEmergencyAccess);

// Grant emergency access (for owners)
router.post('/grant', grantEmergencyAccess);

// Deny emergency access (for owners)
router.post('/deny', denyEmergencyAccess);

// Get emergency access status
router.get('/status', getEmergencyAccessStatus);

// Get all key holders for an owner
router.get('/key-holders', getKeyHoldersForOwner);

export default router; 