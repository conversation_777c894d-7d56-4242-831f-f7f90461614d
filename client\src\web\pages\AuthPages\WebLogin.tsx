import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FcGoogle } from "react-icons/fc";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { useToast } from "@/contexts/ToastContext";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Input } from "@/components/ui/input";
import authService from "@/services/authService";

// Define the login form schema with zod
const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
}); 

type LoginFormValues = z.infer<typeof loginSchema>;

export default function WebLogin() {
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const { login, isLoading, user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  // Check for success message from navigation state
  useEffect(() => {
    if (location.state?.message) {
      setSuccessMessage(location.state.message);
      // Clear the message from location state
      navigate(location.pathname, { replace: true });
    }
  }, [location.state, navigate, location.pathname]);

  // Initialize react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
    mode: "onBlur", // Validate on blur for better UX
  });

  useEffect(() => {
    if (isAuthenticated && !user?.pricingPlan && location.pathname !== "/auth/subscribe") {
      navigate("/auth/subscribe");
    }
  }, [isAuthenticated, user, location, navigate]);

  const handleToggle = (mode: 'register' | 'login') => {
    navigate(mode === 'register' ? '/auth/register' : '/auth/login');
  };

  const onSubmit = async (data: LoginFormValues) => {
    setError(null);
    try {
      await login({ email: data.email, password: data.password });
      
      // Show success toast
      toast({
        title: "Login successful",
        description: "You have been logged in successfully",
        variant: "custom"
      });
      
      // Add a longer delay before navigation to ensure toast is visible
      setTimeout(async () => {
        // Fetch latest user profile
        const latestUser = await authService.getProfile();
        if (!latestUser.pricingPlan) {
          navigate("/auth/subscribe");
        } else {
          navigate("/dashboard");
        }
      }, 2000);
    } catch (err: any) {
      // Check if error is about email verification with auto-resend
      if (err.response?.data?.requiresVerification && err.response?.data?.otpSent) {
        toast({
          title: "Email verification required",
          description: "We've sent a new verification code to your email",
          variant: "custom"
        });
        setTimeout(() => {
          navigate("/auth/verify", {
            state: {
              email: err.response.data.email || data.email,
              message: err.response.data.message || "Please verify your email to continue.",
              fromLogin: true
            }
          });
        }, 1000);
        return;
      }

      setError(err.message || "Login failed. Please try again.");
      toast({
        title: "Login failed",
        description: "Please try again.",
        variant: "custom"
      });
    }
  };

  const handleGoogleLogin = () => {
    toast({
      title: "Redirecting to Google",
      description: "Please complete the Google login process",
      variant: "custom"
    });
    setTimeout(() => {
      window.location.href = `${import.meta.env.VITE_BACKEND_URL}/v1/auth/google/login`;
    }, 1000);
  };

  return (
    <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
      {successMessage && (
        <Alert className="border-green-200 bg-green-50">
          <AlertDescription className="text-green-700">{successMessage}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700">Email</label>
        <Input
          type="email"
          placeholder="Enter your email"
          className={`w-full mt-1 p-2 text-sm ${errors.email ? 'border-red-500' : ''}`}
          {...register("email")}
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Password</label>
        <Input
          type="password"
          placeholder="Enter your password"
          className={`w-full mt-1 p-2 text-sm ${errors.password ? 'border-red-500' : ''}`}
          {...register("password")}
        />
        {errors.password && (
          <p className="mt-1 text-sm text-red-500">{errors.password.message}</p>
        )}
        <div className="mt-2 text-right">
          <Link
            to="/auth/forgetpassword"
            className="text-sm text-[#2BCFD5] hover:text-[#22BBCC] hover:underline transition-colors duration-200"
          >
            Forgot your password?
          </Link>
        </div>
      </div>

      <Button
        type="submit"
        className="w-full bg-[#2BCFD5] hover:bg-[#25b6ba] text-white text-sm"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Logging in...
          </>
        ) : (
          "Log in"
        )}
      </Button>

      <button
        type="button"
        className="w-full mt-2 border text-sm py-2 rounded-md flex justify-center items-center gap-2"
        onClick={handleGoogleLogin}
      >
       <FcGoogle className="inline-block w-4 h-4" /> Log in with Google
      </button>

      <p className="text-center text-sm mt-4">
        Don't have an account?{" "}
        <button
          type="button"
          onClick={() => handleToggle('register')}
          className="text-[#2BCFD5] cursor-pointer hover:text-[#22BBCC]"
        >
          Sign up
        </button>
      </p>
    </form>
  );
}
