import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/contexts/AuthContext';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchUserInputs,
  selectError,
  selectLoading,
  selectProgressStats,
  selectUserInputs,
  UserInput,
  Question
} from '@/store/slices/insuranceSlice';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { Avatar } from '@radix-ui/react-avatar';
import { CheckCircle2 } from 'lucide-react';
import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';

const sectionTitles = {
  '401A': 'Home Insurance',
  '401B': 'Medical Insurance',
  '401C': 'Life Insurance',
};

// Use tabs from categoryTabsConfig
const tabs = categoryTabsConfig.insurance;

// Create subcategories based on sections
const insuranceSubcategories = Object.entries(sectionTitles).map(([sectionId, title]) => ({
  id: sectionId,
  title: title,
  questionsCount: 0 // Will be calculated dynamically
}));

const SubCategoryCard = ({ subcategory, questions }: { subcategory: { id: string; title: string; questionsCount: number }, questions: Question[] }) => {
  // Get the user inputs from Redux state
  const userInputs = useAppSelector(selectUserInputs);

  // Calculate total questions for this section
  const sectionQuestions = questions.filter(q => q.sectionId === subcategory.id);
  const totalQuestions = sectionQuestions.length;

  // Calculate completed questions for this section with deduplication
  const answeredQuestionIds = new Set<string>();
  userInputs.forEach((input: UserInput) => {
    // Look for answers in this specific section
    const sectionAnswers = input.answersBySection.find(section => section.originalSectionId === subcategory.id);
    if (sectionAnswers) {
      sectionAnswers.answers.forEach(answer => {
        if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
          answeredQuestionIds.add(answer.originalQuestionId);
        }
      });
    }
  });
  const completedQuestions = answeredQuestionIds.size;

  const completionPercentage = totalQuestions > 0
    ? Math.round((completedQuestions / totalQuestions) * 100)
    : 0;

  return (
    <div className="border rounded-lg overflow-hidden transition-shadow hover:shadow-md">
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium text-[#1F4168]">{subcategory.title}</h3>
          <span className="text-sm text-blue-500">
            {completedQuestions}/{totalQuestions} questions
          </span>
        </div>
        <Progress value={completionPercentage} className="h-1.5 mb-2" />
      </div>
    </div>
  );
};

const Insurance = () => {
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  // Get data from Redux store
  const progressStats = useAppSelector(selectProgressStats);
  const loading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);

  // Get questions data
  const questions = useAppSelector((state: any) => state.insurance.questions['401'] || []);

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            // Fallback to user ID if no owner found
            dispatch(fetchUserInputs(user.id));
          }
        } catch (error) {
          console.error('Error fetching owner ID, falling back to user ID:', error);
          dispatch(fetchUserInputs(user.id));
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: createUserInfo(user).avatar
  };

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      {/* Header with gradient background and user info */}
      <div className="bg-gradient-to-r from-[#1F4168] to-[#2BCFD5] text-white py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-1">Insurance</h1>
              <Link to="/dashboard" className="flex items-center text-sm hover:underline text-[#2BCFD5] font-semibold text-md mt-1 mb-1">
                <span className="mr-1">←</span> Back to Home
              </Link>
            </div>
            <div className="flex items-center">
              <div className="text-right mr-4">
                <div className="font-semibold">{userInfo.name}</div>
                <div className="text-sm opacity-80">{userInfo.email}</div>
              </div>
              <Avatar className="rounded-full w-14 h-14 bg-white overflow-hidden">
                <img
                  src={userInfo.avatar}
                  alt={userInfo.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = avatar; // Fallback to default avatar
                  }}
                />
              </Avatar>
            </div>
          </div>
        </div>
      </div>
      {/* Subcategory Tabs */}
      <SubCategoryTabs tabs={tabs} />
      {/* Main content */}
      {/* Main content */}
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Categories */}
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              {/* Overall progress bar */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium text-gray-700">Overall progress</h3>
                  <span className="text-sm text-gray-500">
                    {progressStats.answeredQuestions}/{progressStats.totalQuestions} questions completed
                  </span>
                </div>
                <Progress
                  value={progressStats.completionPercentage}
                  className="h-2"
                />
                {progressStats.completionPercentage === 100 && (
                  <div className="mt-2 text-center">
                    <span className="inline-flex items-center text-sm text-green-600 font-medium">
                      <CheckCircle2 className="h-4 w-4 mr-1" /> All questions completed!
                    </span>
                  </div>
                )}
              </div>
              {/* Info Box */}
              <h2 className="text-xl font-semibold text-[#1F4168] mb-2">Good to Know: <span className="text-purple-600">How to Understand Topics</span></h2>
              <p className="text-gray-600 mb-6">
                Each topic below is a part of your insurance information, with questions to help you provide important
                details for you and your loved ones. Click on a category to answer questions at your own pace—
                we'll save everything for you.
              </p>
              {/* Loading state */}
              {loading && (
                <div className="text-center py-4">
                  <p className="text-gray-500">Loading your insurance information...</p>
                </div>
              )}

              {/* Error state */}
              {error && (
                <div className="text-center py-4">
                  <p className="text-red-500">{error}</p>
                </div>
              )}

              {/* Subcategory cards */}
              {!loading && !error && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-6">
                  {insuranceSubcategories.map((subcategory) => {
                    // Map section IDs to correct paths
                    const pathMap: Record<string, string> = {
                      '401A': '/category/insurance/home',
                      '401B': '/category/insurance/medical',
                      '401C': '/category/insurance/life'
                    };

                    return (
                      <Link
                        key={subcategory.id}
                        to={pathMap[subcategory.id] || `/category/insurance/${subcategory.title.toLowerCase().replace(/\s/g, '')}`}
                        className="block"
                      >
                        <SubCategoryCard subcategory={subcategory} questions={questions} />
                      </Link>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
          {/* Right column - Search panel */}
          <div>
            <SearchPanel />
            {/* Still have questions box */}
            <div className="bg-[#223a5f] text-white rounded-lg p-4 mt-6">
              <div className="mb-2 font-semibold">Still have questions?</div>
              <div className="mb-4 text-sm opacity-80">
                Can't find the answer you're looking for? Please chat to our friendly team.
              </div>
              <button className="bg-[#2BCFD5] text-white px-4 py-2 rounded font-semibold">
                Get in touch
              </button>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Insurance;
