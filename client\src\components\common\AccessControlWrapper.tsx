import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Lock, ArrowUpRight, Eye, Edit } from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';
import { useAuth } from '@/contexts/AuthContext';

interface AccessControlWrapperProps {
  categoryId: string;
  children: React.ReactNode;
  requireEdit?: boolean;
  fallback?: React.ReactNode;
  showUpgradeCTA?: boolean;
  className?: string;
}

export const AccessControlWrapper: React.FC<AccessControlWrapperProps> = ({
  categoryId,
  children,
  requireEdit = false,
  fallback,
  showUpgradeCTA = true,
  className
}) => {
  const { isAuthenticated } = useAuth();
  const { 
    userAccess, 
    canAccessCategory, 
    canEditCategory, 
    getAccessRestrictionReason, 
    getEditRestrictionReason 
  } = useAccessControl();

  // Don't render anything if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  if (!userAccess) {
    return (
      <div className={className}>
        <Alert>
          <AlertDescription>Loading access permissions...</AlertDescription>
        </Alert>
      </div>
    );
  }

  const hasAccess = requireEdit ? canEditCategory(categoryId) : canAccessCategory(categoryId);
  const restrictionReason = requireEdit 
    ? getEditRestrictionReason(categoryId) 
    : getAccessRestrictionReason(categoryId);

  if (hasAccess) {
    return <div className={className}>{children}</div>;
  }

  if (fallback) {
    return <div className={className}>{fallback}</div>;
  }

  const renderUpgradeCTA = () => {
    if (!showUpgradeCTA) return null;

    const getUpgradeMessage = () => {
      switch (userAccess.subscriptionType) {
        case 'temporary_key':
          return 'Upgrade to Spare Key or All Access Key for more categories';
        case 'spare_key':
          return 'Upgrade to All Access Key for full access';
        default:
          return 'Upgrade your plan for better access';
      }
    };

    return (
      <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-2 mb-2">
            <ArrowUpRight className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-800">Upgrade Your Plan</span>
          </div>
        <p className="text-sm text-blue-700 mb-3">{getUpgradeMessage()}</p>
        <Button 
          variant="outline" 
          size="sm"
          className="border-blue-300 text-blue-700 hover:bg-blue-50"
          onClick={() => {
            // Navigate to subscription page
            window.location.href = '/subscription';
          }}
        >
          View Plans
        </Button>
      </div>
    );
  };

  return (
    <div className={className}>
      <Card className="border-orange-200 bg-orange-50">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-orange-800">
            {requireEdit ? <Edit className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            {requireEdit ? 'Edit Access Restricted' : 'Access Restricted'}
          </CardTitle>
          <CardDescription className="text-orange-700">
            {requireEdit ? 'You cannot edit this information' : 'You cannot access this information'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert className="border-orange-200 bg-orange-100">
            <Lock className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              {restrictionReason}
            </AlertDescription>
          </Alert>
          
          {renderUpgradeCTA()}
        </CardContent>
      </Card>
    </div>
  );
}; 