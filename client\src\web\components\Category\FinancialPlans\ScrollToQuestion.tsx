import { useRef, useEffect, ReactNode } from 'react';
import { Question } from './FormFields';

interface ScrollToQuestionProps {
  questions: Question[];
  children: (refs: Record<string, HTMLDivElement | null>) => ReactNode;
}

const ScrollToQuestion = ({ questions, children }: ScrollToQuestionProps) => {
  const refs = useRef<Record<string, HTMLDivElement | null>>({});

  useEffect(() => {
    // Initialize refs for all questions
    questions.forEach(question => {
      if (!refs.current[question.id]) {
        refs.current[question.id] = null;
      }
    });
  }, [questions]);

  const scrollToQuestion = (questionId: string) => {
    const element = refs.current[questionId];
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      });
    }
  };

  // Expose scroll function globally if needed
  useEffect(() => {
    (window as any).scrollToFinancialPlansQuestion = scrollToQuestion;
    return () => {
      delete (window as any).scrollToFinancialPlansQuestion;
    };
  }, []);

  return <>{children(refs.current)}</>;
};

export default ScrollToQuestion;
