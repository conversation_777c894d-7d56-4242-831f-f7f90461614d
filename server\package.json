{"name": "heirkey-backend", "version": "1.0.0", "description": "", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "update-stripe-ids": "ts-node src/utils/updateStripePriceIds.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/cookie-parser": "^1.4.8", "@types/multer": "^1.4.12", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "stripe": "^18.2.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/express-session": "^1.18.1", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/node": "^22.13.16", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "cross-env": "^7.0.3", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsx": "^4.19.3", "typescript": "^5.8.2"}}