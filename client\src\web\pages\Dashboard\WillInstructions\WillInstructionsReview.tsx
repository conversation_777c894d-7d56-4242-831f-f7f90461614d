import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CategoryReviewPage from '@/web/components/Category/CategoryReviewPage';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { fetchUserInputs } from '@/store/slices/willInstructionsSlice';
import { createUserInfo } from '@/utils/avatarUtils';

// We're using 'any' type for API responses with detailed console logging

// No need for mapping tables as we're using direct navigation based on section IDs

export default function WillInstructionsReview() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [topics, setTopics] = useState<Array<{
    id: string;
    title: string;
    subtitle?: string;
    data: string;
    onEdit: () => void;
  }>>([]);

  // Get data from Redux store using selectors
  const userInputs = useAppSelector((state: any) => state.willInstructions.userInputs);
  const loading = useAppSelector((state: any) => state.willInstructions.loading);
  const error = useAppSelector((state: any) => state.willInstructions.error);

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: createUserInfo(user).avatar
  };

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            dispatch(fetchUserInputs(user.id));
          }
        } catch (error) {
          console.error('Error fetching owner ID in WillInstructionsReview:', error);
        }
      }
    };
    fetchData();
  }, [dispatch, user]);

  // Process user inputs to create topics for review
  useEffect(() => {
    if (!user?.id) {
      return;
    }

    if (userInputs.length > 0 && !loading) {
      // Transform the data for the review page
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      // Process all user inputs
      userInputs.forEach((userInput: any) => {
        userInput.answersBySection.forEach((section: any) => {
          section.answers.forEach((answer: any) => {
            allTopics.push({
              id: answer.originalQuestionId,
              title: answer.question, // Use the question text directly from the answer
              subtitle: `Section: ${section.originalSectionId}`,
              data: answer.answer,
              onEdit: () => {
                // Determine which subcategory to navigate to
                let route = '/category/willinstructions';
                // Location sections
                if (section.originalSectionId === '105A' || section.originalSectionId === '105B') {
                  route = '/category/willinstructions/location';
                }
                // Legal section
                else if (section.originalSectionId === '105B') {
                  route = '/category/willinstructions/legal';
                }
                navigate(`${route}?questionId=${answer.originalQuestionId}`);
              }
            });
          });
        });
      });
      setTopics(allTopics);
    }
  }, [userInputs, loading, navigate, user]);

  if (loading) {
    return <div className="flex justify-center items-center h-screen">Loading your answers...</div>;
  }

  if (error) {
    return <div className="flex justify-center items-center h-screen text-red-500">{error}</div>;
  }

  if (!user?.id) {
    return <div className="flex justify-center items-center h-screen text-red-500">You must be logged in to view your answers</div>;
  }

  return (
    <div className="flex flex-col items-center">
      <CategoryReviewPage
        categoryTitle="Will & Testament"
        infoTitle="How to edit your information"
        infoDescription="Now, you are about to enter details about your will, legal representation, and essential information to be passed on to your family members. Each section has several questions. Fill out as much as you can/like. You can always come back to fill out more information later."
        topics={topics}
        user={userInfo}
        onPrint={() => window.print()}
        afterTopics={
          <button
            onClick={() => navigate('/category/funeralarrangements')}
            className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
          >
            Continue to Funeral Arrangements
          </button>
        }
      />
    </div>
  );
}