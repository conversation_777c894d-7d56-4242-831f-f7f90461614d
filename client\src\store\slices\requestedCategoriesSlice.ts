import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RequestedCategoriesState, RequestCategoryData, RequestedCategory } from '../../types/requestedCategories';
import * as requestedCategoriesService from '../../services/requestedCategoriesService';

const initialState: RequestedCategoriesState = {
  requests: [],
  loading: false,
  error: null,
  success: null
};

// Add response types
interface RequestCategoriesResponse {
  message: string;
}
interface RequestsListResponse {
  requests: RequestedCategory[];
}

// Async thunks
export const requestCategories = createAsyncThunk<
  RequestCategoriesResponse,
  RequestCategoryData,
  { rejectValue: string }
>(
  'requestedCategories/request',
  async (data, { rejectWithValue }) => {
    try {
      const response = await requestedCategoriesService.requestCategories(data);
      return response as RequestCategoriesResponse;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to request categories');
    }
  }
);

export const fetchMyRequests = createAsyncThunk<
  RequestedCategory[],
  void,
  { rejectValue: string }
>(
  'requestedCategories/fetchMyRequests',
  async (_, { rejectWithValue }) => {
    try {
      const response = await requestedCategoriesService.getMyRequests();
      return (response as RequestsListResponse).requests;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch requests');
    }
  }
);

export const fetchOwnerRequests = createAsyncThunk<
  RequestedCategory[],
  void,
  { rejectValue: string }
>(
  'requestedCategories/fetchOwnerRequests',
  async (_, { rejectWithValue }) => {
    try {
      const response = await requestedCategoriesService.getOwnerRequests();
      return (response as RequestsListResponse).requests;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch owner requests');
    }
  }
);

export const handleRequestApproval = createAsyncThunk<
  RequestCategoriesResponse,
  { token: string; action: 'approve' | 'reject' },
  { rejectValue: string }
>(
  'requestedCategories/handleApproval',
  async ({ token, action }, { rejectWithValue }) => {
    try {
      const response = await requestedCategoriesService.approveRequest(token, action);
      return response as RequestCategoriesResponse;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to process request');
    }
  }
);

export const handleDashboardApproval = createAsyncThunk<
  RequestCategoriesResponse,
  { requestId: string; action: 'approve' | 'reject' },
  { rejectValue: string }
>(
  'requestedCategories/handleDashboardApproval',
  async ({ requestId, action }, { rejectWithValue }) => {
    try {
      const response = await requestedCategoriesService.approveRequestFromDashboard(requestId, action);
      return response as RequestCategoriesResponse;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to process request');
    }
  }
);

const requestedCategoriesSlice = createSlice({
  name: 'requestedCategories',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSuccess: (state) => {
      state.success = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Request Categories
      .addCase(requestCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(requestCategories.fulfilled, (state, action) => {
        state.loading = false;
        state.success = action.payload.message;
      })
      .addCase(requestCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Fetch My Requests
      .addCase(fetchMyRequests.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMyRequests.fulfilled, (state, action) => {
        state.loading = false;
        state.requests = action.payload;
      })
      .addCase(fetchMyRequests.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Fetch Owner Requests
      .addCase(fetchOwnerRequests.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOwnerRequests.fulfilled, (state, action) => {
        state.loading = false;
        state.requests = action.payload;
      })
      .addCase(fetchOwnerRequests.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Handle Request Approval
      .addCase(handleRequestApproval.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(handleRequestApproval.fulfilled, (state, action) => {
        state.loading = false;
        state.success = action.payload.message;
      })
      .addCase(handleRequestApproval.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Handle Dashboard Approval
      .addCase(handleDashboardApproval.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(handleDashboardApproval.fulfilled, (state, action) => {
        state.loading = false;
        state.success = action.payload.message;
      })
      .addCase(handleDashboardApproval.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const { clearError, clearSuccess } = requestedCategoriesSlice.actions;
export default requestedCategoriesSlice.reducer; 