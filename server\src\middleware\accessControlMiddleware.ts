import { Request, Response, NextFunction } from 'express';
import User from '../models/User';
import Owner from '../models/Owner';
import Role from '../models/Role';
import InvitedUser from '../models/InvitedUser';
import { AuthRequest } from './authMiddleware';
import { RoleType } from '../types/Role';
import { sendEmergencyAccessAutoGrantEmail } from '../utils/email';

export interface AccessControlRequest extends AuthRequest {
  userAccess?: {
    subscriptionType: string;
    allowedCategoryId?: string;
    isOwner: boolean;
    userRole?: RoleType;
    userPermissions?: string[];
    emergencyAccessGranted: boolean;
    canAccessCategory: (categoryId: string) => boolean;
    canEditCategory: (categoryId: string) => boolean;
  };
}

// Emergency access wait time in milliseconds (24 hours)
const EMERGENCY_ACCESS_WAIT_TIME = 24 * 60 * 60 * 1000;

// Check if emergency access should be auto-granted
const shouldAutoGrantEmergencyAccess = (user: any): boolean => {
  if (!user.emergencyAccessRequested || user.emergencyAccessRequestedAt === null) {
    return false;
  }

  const requestTime = new Date(user.emergencyAccessRequestedAt).getTime();
  const currentTime = Date.now();
  const timeElapsed = currentTime - requestTime;

  return timeElapsed >= EMERGENCY_ACCESS_WAIT_TIME && !user.emergencyAccessDenied;
};

// Auto-grant emergency access if wait time has passed
const autoGrantEmergencyAccess = async (userId: string): Promise<void> => {
  try {
    const user = await User.findById(userId);
    if (user && shouldAutoGrantEmergencyAccess(user)) {
      user.emergencyAccessGranted = true;
      user.emergencyAccessGrantedAt = new Date();
      await user.save();

      // Send email notification to key holder
      if (user.ownerId) {
        const ownerRecord = await Owner.findById(user.ownerId);
        const ownerName = ownerRecord?.firstName || ownerRecord?.username || 'Owner';
        const keyHolderName = user.firstName || user.username || user.email || 'Key Holder';
        sendEmergencyAccessAutoGrantEmail(user.email, keyHolderName, ownerName)
          .then(() => console.log('Emergency access auto-grant email sent to key holder.'))
          .catch((err) => console.error('Failed to send emergency access auto-grant email:', err));
      }
    }
  } catch (error) {
    console.error('Error auto-granting emergency access:', error);
  }
};

// Get user role and permissions
const getUserRoleAndPermissions = async (userId: string): Promise<{ role?: RoleType; permissions: string[] }> => {
  try {
    const user = await User.findById(userId).populate('roleId');
    if (!user || !user.roleId) {
      return { permissions: [] };
    }

    const role = user.roleId as any;
    return {
      role: role.name,
      permissions: role.permissions || []
    };
  } catch (error) {
    console.error('Error getting user role and permissions:', error);
    return { permissions: [] };
  }
};

// Check if user is an owner
const isUserOwner = async (userId: string): Promise<boolean> => {
  try {
    const owner = await Owner.findOne({ userId });
    return !!owner;
  } catch (error) {
    console.error('Error checking if user is owner:', error);
    return false;
  }
};

// Check if user is invited by an owner
const isUserInvited = async (userId: string): Promise<boolean> => {
  try {
    const invitedUser = await InvitedUser.findOne({ 
      invitedUserId: userId,
      status: 'ACCEPTED' // Only consider accepted invitations
    });
    return !!invitedUser;
  } catch (error) {
    console.error('Error checking if user is invited:', error);
    return false;
  }
};

// Main access control middleware
export const accessControlMiddleware = async (
  req: AccessControlRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({ message: 'Authentication required' });
      return;
    }

    const user = await User.findById(req.user._id).populate('roleId');
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Get user role and permissions
    const { role: userRole, permissions: userPermissions } = await getUserRoleAndPermissions(req.user._id.toString());
    
    // Check if user is an owner
    const isOwner = await isUserOwner(req.user._id.toString());
    
    // Check if user is invited
    const isInvited = await isUserInvited(req.user._id.toString());

    // Auto-grant emergency access if needed (for spare_key users)
    if (user.subscriptionType === 'spare_key' && !isOwner) {
      await autoGrantEmergencyAccess(user._id.toString());
      // Refresh user data after potential update
      const updatedUser = await User.findById(user._id);
      if (updatedUser) {
        Object.assign(user, updatedUser);
      }
    }

    // Determine access permissions based on subscription type, role, and owner status
    let canAccessCategory: (categoryId: string) => boolean;
    let canEditCategory: (categoryId: string) => boolean;

    // First check subscription-based restrictions
    switch (user.subscriptionType) {
      case 'temporary_key':
        canAccessCategory = (categoryId: string) => {
          // Temporary Key users can access Home Instructions and Quick Start
          return categoryId === '1' || categoryId === '7';
        };
        canEditCategory = (categoryId: string) => {
          // Temporary Key users can edit Home Instructions and Quick Start
          return categoryId === '1' || categoryId === '7';
        };
        break;

      case 'spare_key':
        if (isOwner) {
          // Owner has full access regardless of role
          canAccessCategory = () => true;
          canEditCategory = () => true;
        } else {
          // Key holder needs emergency access
          const hasEmergencyAccess = user.emergencyAccessGranted || shouldAutoGrantEmergencyAccess(user);
          canAccessCategory = () => hasEmergencyAccess;
          canEditCategory = () => false; // Key holders can only view, not edit
        }
        break;

      case 'all_access_key':
        // Check role-based permissions for all_access_key users
        if (userRole === RoleType.OWNER) {
          canAccessCategory = () => true;
          canEditCategory = () => true;
        } else if (userRole === RoleType.NOMINEE) {
          canAccessCategory = () => userPermissions.includes('canViewAll');
          canEditCategory = () => userPermissions.includes('canEditAll');
        } else if (userRole === RoleType.FAMILY) {
          canAccessCategory = () => userPermissions.includes('canViewAll');
          canEditCategory = () => userPermissions.includes('canEditAll');
        } else {
          // Default for all_access_key without specific role
          canAccessCategory = () => true;
          canEditCategory = () => true;
        }
        break;

      default:
        // Default to no access
        canAccessCategory = () => false;
        canEditCategory = () => false;
    }

    // Attach access control information to request
    req.userAccess = {
      subscriptionType: user.subscriptionType || 'temporary_key',
      allowedCategoryId: user.allowedCategoryId,
      isOwner,
      userRole,
      userPermissions,
      emergencyAccessGranted: user.emergencyAccessGranted || false,
      canAccessCategory,
      canEditCategory
    };

    next();
  } catch (error) {
    console.error('Access control middleware error:', error);
    res.status(500).json({ message: 'Access control error' });
  }
};

// Middleware to check category access
export const requireCategoryAccess = (categoryIdParam: string = 'categoryId') => {
  return (req: AccessControlRequest, res: Response, next: NextFunction): void => {
    if (!req.userAccess) {
      res.status(401).json({ message: 'Access control not initialized' });
      return;
    }

    const categoryId = req.params[categoryIdParam] || req.body[categoryIdParam];
    if (!categoryId) {
      res.status(400).json({ message: 'Category ID required' });
      return;
    }

    if (!req.userAccess.canAccessCategory(categoryId)) {
      res.status(403).json({ 
        message: 'Access denied for this category',
        subscriptionType: req.userAccess.subscriptionType,
        userRole: req.userAccess.userRole,
        isOwner: req.userAccess.isOwner
      });
      return;
    }

    next();
  };
};

// Middleware to check category edit permissions
export const requireCategoryEditAccess = (categoryIdParam: string = 'categoryId') => {
  return (req: AccessControlRequest, res: Response, next: NextFunction): void => {
    if (!req.userAccess) {
      res.status(401).json({ message: 'Access control not initialized' });
      return;
    }

    const categoryId = req.params[categoryIdParam] || req.body[categoryIdParam];
    if (!categoryId) {
      res.status(400).json({ message: 'Category ID required' });
      return;
    }

    if (!req.userAccess.canEditCategory(categoryId)) {
      res.status(403).json({ 
        message: 'Edit access denied for this category',
        subscriptionType: req.userAccess.subscriptionType,
        userRole: req.userAccess.userRole,
        isOwner: req.userAccess.isOwner
      });
      return;
    }

    next();
  };
};

// Middleware to check if user is owner
export const requireOwnerAccess = (req: AccessControlRequest, res: Response, next: NextFunction): void => {
  if (!req.userAccess) {
    res.status(401).json({ message: 'Access control not initialized' });
    return;
  }

  if (!req.userAccess.isOwner) {
    res.status(403).json({ message: 'Owner access required' });
    return;
  }

  next();
};

// Middleware to check specific role
export const requireRole = (requiredRole: RoleType) => {
  return (req: AccessControlRequest, res: Response, next: NextFunction): void => {
    if (!req.userAccess) {
      res.status(401).json({ message: 'Access control not initialized' });
      return;
    }

    if (req.userAccess.userRole !== requiredRole) {
      res.status(403).json({ 
        message: `Role ${requiredRole} required for this operation`,
        currentRole: req.userAccess.userRole
      });
      return;
    }

    next();
  };
};

// Middleware to check specific permission
export const requirePermission = (requiredPermission: string) => {
  return (req: AccessControlRequest, res: Response, next: NextFunction): void => {
    if (!req.userAccess) {
      res.status(401).json({ message: 'Access control not initialized' });
      return;
    }

    if (!req.userAccess.userPermissions?.includes(requiredPermission)) {
      res.status(403).json({ 
        message: `Permission ${requiredPermission} required for this operation`,
        currentPermissions: req.userAccess.userPermissions
      });
      return;
    }

    next();
  };
};

// Middleware to check subscription type
export const requireSubscriptionType = (allowedTypes: string[]) => {
  return (req: AccessControlRequest, res: Response, next: NextFunction): void => {
    if (!req.userAccess) {
      res.status(401).json({ message: 'Access control not initialized' });
      return;
    }

    if (!allowedTypes.includes(req.userAccess.subscriptionType)) {
      res.status(403).json({ 
        message: `Subscription type ${req.userAccess.subscriptionType} not allowed for this operation`,
        requiredTypes: allowedTypes
      });
      return;
    }

    next();
  };
}; 