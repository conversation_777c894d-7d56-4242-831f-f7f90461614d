import { Alert, AlertDescription } from '@/components/ui/alert';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import defaultAvatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { useAuth } from '@/contexts/AuthContext';
import authService from '@/services/authService';
import { UserPen, Loader2, AlertTriangle, CheckCircle, XCircle, Clock } from 'lucide-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import { useNavigate } from 'react-router-dom';
import Select from 'react-select';
import AuthHeader from '../header/gradiantHeader';
// Import the country list package
import countryList from 'react-select-country-list';
import { useDispatch, useSelector } from "react-redux";
import { fetchOwnerSubscription, fetchPricingPlans } from "@/store/slices/subscriptionSlice";
import { RootState, AppDispatch } from "@/store";
import ownerService from '@/services/ownerService';
import { useAccessControl } from '@/hooks/useAccessControl';
import { MobileEmergencyAccessRequest } from '../common/MobileEmergencyAccessRequest';
import { MobileEmergencyAccessManagement } from '../common/MobileEmergencyAccessManagement';
import ImageCropper from '@/components/common/ImageCropper';

// Define the option type for react-select
interface CountryOption {
  value: string;
  label: string;
}

export default function UserProfile() {
  const { user, logout, isLoading, setUser } = useAuth();
  const navigate = useNavigate();
  const { userAccess, handleRequestEmergencyAccess, emergencyAccessStatus } = useAccessControl();
  const [isEditing, setIsEditing] = useState(false);
  const [firstName, setFirstName] = useState(user?.firstName || '');
  const [lastName, setLastName] = useState(user?.lastName || '');
  const [email, setEmail] = useState(user?.email || '');
  const [username, setUsername] = useState(user?.username || '');
  const [phone, setPhone] = useState(user?.phone || '');
  const [address, setAddress] = useState(user?.address || '');
  const [zipCode, setZipCode] = useState(user?.zipCode || '');
  const [country, setCountry] = useState(user?.country || '');

  // Get country options using the countryList package
  const countryOptions = useMemo(() => countryList().getData(), []);
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Image cropper states
  const [showCropper, setShowCropper] = useState(false);
  const [originalImageSrc, setOriginalImageSrc] = useState<string>('');

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // For real-time validation
  const [phoneError, setPhoneError] = useState<string | null>(null);
  const [zipCodeError, setZipCodeError] = useState<string | null>(null);

  const dispatch = useDispatch<AppDispatch>();
  const { currentSubscription, plans, loading: subLoading } = useSelector((state: RootState) => state.subscription);

  const [ownerId, setOwnerId] = useState<string | null>(null);
  const [ownerLoading, setOwnerLoading] = useState(true);

  useEffect(() => {
    if (user) {
      setFirstName(user.firstName || '');
      setLastName(user.lastName || '');
      setEmail(user.email || '');
      setUsername(user.username || '');
      setPhone(user.phone || '');
      setAddress(user.address || '');
      setZipCode(user.zipCode || '');
      setCountry(user.country || '');
      if (user.image) {
        // Construct the full URL for the uploaded image
        const imageUrl = user.image.startsWith('http')
          ? user.image
          : `${import.meta.env.VITE_API_URL}/uploads/${user.image}`;
        setImagePreview(imageUrl);
      } else {
        // Explicitly set to null when no image so defaultAvatar is used
        setImagePreview(null);
      }
    }
  }, [user]);

  // Cleanup timeouts on component unmount
  useEffect(() => {
    return () => {
      // Clear all pending timeouts when component unmounts
      Object.values(timeoutRef.current).forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (5MB = 5 * 1024 * 1024 bytes)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        setError(`Upload image less than 5MB. Current file size: ${formatFileSize(file.size)}`);
        setSuccess(null);
        // Clear the input
        e.target.value = '';
        return;
      }

      // Check file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setError('Please upload a valid image file (JPEG, PNG, GIF, WebP)');
        setSuccess(null);
        // Clear the input
        e.target.value = '';
        return;
      }

      // Clear any previous errors
      setError(null);
      setSuccess(null);

      // Create a preview for cropping
      const reader = new FileReader();
      reader.onloadend = () => {
        setOriginalImageSrc(reader.result as string);
        setShowCropper(true);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCropComplete = (croppedImageBlob: Blob) => {
    // Convert blob to file
    const croppedFile = new File([croppedImageBlob], 'profile-image.jpg', {
      type: 'image/jpeg',
    });

    setProfileImage(croppedFile);

    // Create preview URL for the cropped image
    const previewUrl = URL.createObjectURL(croppedImageBlob);
    setImagePreview(previewUrl);
    setShowCropper(false);
  };

  // Validation functions
  const validatePhone = (value: string): boolean => {
    // With react-phone-input-2, the validation is handled by the component
    // We just need to check if the phone number has a minimum length
    const isValid = value && value.length >= 8 ? true : false;
    setPhoneError(isValid ? null : 'Please enter a valid phone number');
    return isValid;
  };

  const validateZipCode = (value: string): boolean => {
    // Basic ZIP code validation - can be enhanced based on country
    const zipRegex = /^[0-9a-zA-Z\s\-]{3,10}$/;
    const isValid = zipRegex.test(value);
    setZipCodeError(isValid ? null : 'Please enter a valid ZIP/postal code');
    return isValid;
  };

  // Create a reference for timeout IDs
  const timeoutRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Debounced update function for real-time syncing
  const debouncedUpdate = (field: string, value: string) => {
    // Clear any existing timeout for this field
    if (timeoutRef.current[field]) {
      clearTimeout(timeoutRef.current[field]);
    }

    // Set a new timeout (700ms delay)
    timeoutRef.current[field] = setTimeout(async () => {
      try {
        const updateData: Record<string, string> = { [field]: value };
        const updatedUser = await authService.updateProfile(updateData);
        setUser(updatedUser);
      } catch (err) {
        console.error(`Error updating ${field}:`, err);
      }
    }, 700); // Wait for 700ms of inactivity before making the API call
  };

  // Handle field changes with validation and real-time update
  const handleFieldChange = (field: string, value: string) => {
    switch (field) {
      case 'phone':
        setPhone(value);
        validatePhone(value);
        break;
      case 'zipCode':
        setZipCode(value);
        validateZipCode(value);
        break;
      case 'address':
        setAddress(value);
        break;
      case 'country':
        setCountry(value);
        break;
      default:
        break;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEditing) return;
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    // Validate fields
    const isPhoneValid = phone ? validatePhone(phone) : true;
    const isZipValid = zipCode ? validateZipCode(zipCode) : true;

    if (!isPhoneValid || !isZipValid) {
      setIsSubmitting(false);
      setError('Please correct the errors before submitting');
      return;
    }

    try {
      // First update the profile image if changed
      if (profileImage) {
        const formData = new FormData();
        formData.append('image', profileImage);
        const updatedUser = await authService.updateProfileImage(formData);
        setUser(updatedUser);
        // Update the image preview with the new image URL
        if (updatedUser.image) {
          const imageUrl = updatedUser.image.startsWith('http') 
            ? updatedUser.image 
            : `${import.meta.env.VITE_API_URL}/uploads/${updatedUser.image}`;
          setImagePreview(imageUrl);
        }
      }

      // Then update the profile information
      const updatedUser = await authService.updateProfile({
        firstName: firstName || undefined,
        lastName: lastName || undefined,
        username: username || undefined,
        phone: phone || undefined,
        address: address || undefined,
        zipCode: zipCode || undefined,
        country: country || undefined
      });

      setUser(updatedUser);
      setSuccess('Profile updated successfully!');
      setIsEditing(false);
      navigate('/dashboard');
    } catch (err: any) {
      console.error('Profile update error:', err);

      // Handle specific error messages from backend
      let errorMessage = 'Failed to update profile';

      if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err?.message) {
        errorMessage = err.message;
      }

      // Handle specific file upload errors
      if (err?.response?.data?.error === 'FILE_TOO_LARGE') {
        errorMessage = 'Upload image less than 5MB';
      } else if (err?.response?.data?.error === 'INVALID_FILE_TYPE') {
        errorMessage = 'Please upload a valid image file (JPEG, PNG, GIF, WebP)';
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/auth/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Get initials for avatar fallback
  const getInitials = () => {
    if (firstName && lastName) {
      return `${firstName[0]}${lastName[0]}`.toUpperCase();
    } else if (username) {
      return username.substring(0, 2).toUpperCase();
    }
    return 'UN';
  };

  // Add useEffect for fetching owner and subscription data
  useEffect(() => {
    const fetchOwner = async () => {
      if (user?.id) {
        setOwnerLoading(true);
        try {
          // If user has an ownerId, use it directly (for nominees/keyholders)
          if (user.ownerId) {
            setOwnerId(user.ownerId);
          } else {
            // Otherwise, fetch the owner by userId (for owners)
            const owner = await ownerService.getOwnerByUserId(user.id);
            setOwnerId(owner._id);
          }
        } catch (err) {
          setOwnerId(null);
        } finally {
          setOwnerLoading(false);
        }
      }
    };
    fetchOwner();
  }, [user]);

  useEffect(() => {
    if (ownerId) {
      dispatch(fetchOwnerSubscription(ownerId));
    }
    dispatch(fetchPricingPlans());
  }, [dispatch, ownerId]);

  const subscribedPlan = plans.find(plan => plan._id === currentSubscription?.planId);

  const formatTimeRemaining = (milliseconds: number): string => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const renderEmergencyAccessStatus = () => {
    if (!userAccess || userAccess.subscriptionType !== 'spare_key') {
      return null;
    }

    if (userAccess.isOwner) {
      return null; // Owners don't need to see their own emergency access status
    }

    return (
      <Card className="w-full max-w-md shadow mb-4">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Emergency Access Status
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-2">
          {userAccess.emergencyAccessGranted && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                Emergency access has been granted. You can now view the information.
              </AlertDescription>
            </Alert>
          )}

          {userAccess.emergencyAccessDenied && (
            <Alert className="border-red-200 bg-red-50">
              <XCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                Emergency access was denied by the owner.
              </AlertDescription>
            </Alert>
          )}

          {userAccess.emergencyAccessRequested && 
           !userAccess.emergencyAccessGranted && 
           !userAccess.emergencyAccessDenied && (
            <Alert className="border-yellow-200 bg-yellow-50">
              <Clock className="h-4 w-4 text-yellow-600" />
              <AlertDescription className="text-yellow-800">
                <div className="space-y-2">
                  <div>Emergency access request is pending approval.</div>
                  {emergencyAccessStatus?.timeRemaining && emergencyAccessStatus.timeRemaining > 0 && (
                    <div className="text-sm">
                      Auto-grant in: {formatTimeRemaining(emergencyAccessStatus.timeRemaining)}
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {!userAccess.emergencyAccessRequested && 
           !userAccess.emergencyAccessGranted && 
           !userAccess.emergencyAccessDenied && (
            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                You need to request emergency access to view information.
              </p>
              <Button 
                onClick={handleRequestEmergencyAccess}
                className="w-full bg-orange-600 hover:bg-orange-700"
              >
                Request Emergency Access
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <>
      <AuthHeader title={`Welcome, ${firstName || username || 'User'}`} />

      <div className="min-h-screen py-4 px-3 flex flex-col items-center gap-4">
        {/* Emergency Access Request for Key Holders */}
        {/* {userAccess && !userAccess.isOwner && userAccess.subscriptionType === 'spare_key' && (
          <MobileEmergencyAccessRequest />
        )} */}

        {/* Emergency Access Management for Owners */}
        {userAccess?.isOwner && userAccess.subscriptionType === 'spare_key' && (
          <MobileEmergencyAccessManagement />
        )}

        {/* Emergency Access Status for Key Holders */}
        {renderEmergencyAccessStatus()}

        <Card className="w-full max-w-md shadow">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg text-gray-900">Profile Settings</CardTitle>
              {!isEditing && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1 h-8 px-2"
                  onClick={() => setIsEditing(true)}
                  disabled={isLoading}
                >
                  <UserPen size={14} /> Edit
                </Button>
              )}
            </div>
          </CardHeader>

          <CardContent className="pt-2">
            {error && (
              <Alert variant="destructive" className="mb-3 py-2">
                <AlertDescription className="text-sm">{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="mb-3 py-2 bg-green-50 border-green-200">
                <AlertDescription className="text-sm text-green-700">{success}</AlertDescription>
              </Alert>
            )}

            <form className="space-y-3" onSubmit={handleSubmit}>
              {/* Profile Image and Basic Info */}
              <div className="flex items-center gap-4 mb-1">
                <div className="relative group">
                  <div className="h-16 w-16 border-2 border-[#22BBCC] shadow rounded-full overflow-hidden bg-white">
                    <img
                      src={imagePreview || defaultAvatar}
                      alt="Profile"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = defaultAvatar;
                      }}
                    />
                  </div>
                  {isEditing && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <Label htmlFor="profileImage" className="cursor-pointer text-white text-xs">
                        Change
                      </Label>
                    </div>
                  )}
                </div>
                <div>
                  <h3 className="font-medium">{firstName} {lastName}</h3>
                  <p className="text-xs text-gray-500">{email}</p>
                  
                  {isEditing && (
                    <>
                      <Label
                        htmlFor="profileImage"
                        className="cursor-pointer text-xs text-[#22BBCC] hover:text-[#22BBCA]"
                      >
                        Change Profile Picture
                      </Label>
                      <Input
                        id="profileImage"
                        name="profileImage"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleImageChange}
                      />
                      {/* <p className="text-xs text-gray-500 mt-1">
                        Max file size: 5MB. Supported formats: JPEG, PNG, GIF, WebP
                      </p> */}
                    </>
                  )}
                </div>
              </div>

              {/* Form Fields */}
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <Label htmlFor="firstName" className="text-xs font-medium text-gray-700">
                    First Name
                  </Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    type="text"
                    placeholder="First name"
                    className={`h-9 text-sm ${!isEditing ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    readOnly={!isEditing}
                    disabled={!isEditing}
                  />
                </div>

                <div className="space-y-1">
                  <Label htmlFor="lastName" className="text-xs font-medium text-gray-700">
                    Last Name
                  </Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    placeholder="Last name"
                    className={`h-9 text-sm ${!isEditing ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    readOnly={!isEditing}
                    disabled={!isEditing}
                  />
                </div>
              </div>

              <div className="space-y-1">
                <Label htmlFor="phone" className="text-xs font-medium text-gray-700">
                  Phone Number
                </Label>
                <PhoneInput
                  country={'us'}
                  value={phone}
                  onChange={(value) => isEditing && handleFieldChange('phone', value)}
                  inputProps={{
                    name: 'phone',
                    id: 'phone',
                    required: true,
                    readOnly: !isEditing,
                    disabled: !isEditing
                  }}
                  containerClass={`${phoneError ? 'border-red-500' : ''} ${!isEditing ? 'opacity-60' : ''}`}
                  inputClass="w-full text-sm"
                  buttonClass="h-9"
                  containerStyle={{ 
                    height: '36px'
                  }}
                  disabled={!isEditing}
                />
                {phoneError && <p className="text-xs text-red-500">{phoneError}</p>}
              </div>

              <div className="space-y-1">
                <Label htmlFor="address" className="text-xs font-medium text-gray-700">
                  Address
                </Label>
                <Input
                  id="address"
                  name="address"
                  type="text"
                  placeholder="Full address"
                  className={`h-9 text-sm ${!isEditing ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                  value={address}
                  onChange={(e) => handleFieldChange('address', e.target.value)}
                  readOnly={!isEditing}
                  disabled={!isEditing}
                />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <Label htmlFor="zipCode" className="text-xs font-medium text-gray-700">
                    ZIP/Postal Code
                  </Label>
                  <Input
                    id="zipCode"
                    name="zipCode"
                    type="text"
                    placeholder="ZIP code"
                    className={`h-9 text-sm ${zipCodeError ? 'border-red-500' : ''} ${!isEditing ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                    value={zipCode}
                    onChange={(e) => handleFieldChange('zipCode', e.target.value)}
                    onBlur={(e) => validateZipCode(e.target.value)}
                    readOnly={!isEditing}
                    disabled={!isEditing}
                  />
                  {zipCodeError && <p className="text-xs text-red-500">{zipCodeError}</p>}
                </div>

                <div className="space-y-1">
                  <Label htmlFor="country" className="text-xs font-medium text-gray-700">
                    Country
                  </Label>
                  <Select<CountryOption>
                    id="country"
                    name="country"
                    options={countryOptions}
                    placeholder="Select country"
                    className="text-sm"
                    classNamePrefix="select"
                    value={countryOptions.find((option: CountryOption) => option.label === country) || null}
                    onChange={isEditing ? (selectedOption: CountryOption | null) => {
                      if (selectedOption) {
                        handleFieldChange('country', selectedOption.label);
                      }
                    } : undefined}
                    isDisabled={!isEditing}
                    styles={{
                      control: (baseStyles: any) => ({
                        ...baseStyles,
                        minHeight: '36px',
                        height: '36px',
                        borderColor: '#e2e8f0',
                        backgroundColor: !isEditing ? '#f3f4f6' : 'white',
                        opacity: !isEditing ? 0.6 : 1,
                        cursor: !isEditing ? 'not-allowed' : 'default',
                      }),
                      valueContainer: (base: any) => ({
                        ...base,
                        padding: '0 8px',
                      }),
                      input: (base: any) => ({
                        ...base,
                        margin: '0',
                        padding: '0',
                      }),
                      menu: (baseStyles: any) => ({
                        ...baseStyles,
                        zIndex: 50,
                      }),
                    }}
                  />
                </div>
              </div>

              <div className="flex justify-end pt-2">
                {isEditing && (
                  <div className="flex gap-2 w-full">
                    <Button
                      type="button"
                      variant="outline"
                      className="h-9 px-4 w-1/2"
                      onClick={() => {
                        // Clear any pending timeouts
                        Object.values(timeoutRef.current).forEach(timeout => clearTimeout(timeout));
                        
                        setIsEditing(false);
                        setFirstName(user?.firstName || '');
                        setLastName(user?.lastName || '');
                        setEmail(user?.email || '');
                        setUsername(user?.username || '');
                        setPhone(user?.phone || '');
                        setAddress(user?.address || '');
                        setZipCode(user?.zipCode || '');
                        setCountry(user?.country || '');
                        // Reset image preview to user's current image or null
                        if (user?.image) {
                          const imageUrl = user.image.startsWith('http')
                            ? user.image
                            : `${import.meta.env.VITE_API_URL}/uploads/${user.image}`;
                          setImagePreview(imageUrl);
                        } else {
                          setImagePreview(null);
                        }
                        setProfileImage(null);
                        setError(null);
                        setSuccess(null);
                        setPhoneError(null);
                        setZipCodeError(null);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="h-9 px-4 w-1/2 bg-[#22BBCC] text-white hover:bg-[#22BBCA]"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-1 h-3 w-3 animate-spin" /> Saving...
                        </>
                      ) : (
                        "Update Profile"
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </form>
          </CardContent>
        </Card>
      </div>

      {/* Image Cropper Modal */}
      <ImageCropper
        isOpen={showCropper}
        onClose={() => setShowCropper(false)}
        imageSrc={originalImageSrc}
        onCropComplete={handleCropComplete}
        aspectRatio={1}
      />
    </>
  );
}
