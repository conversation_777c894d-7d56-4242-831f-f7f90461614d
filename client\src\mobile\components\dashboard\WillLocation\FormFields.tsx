import React from 'react';
import { Field } from 'formik';

export interface Question {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  dependsOn?: {
    questionId: string;
    value: string;
  };
  placeholder?: string;
  [key: string]: any;
}

export const buildInitialValues = (questions: Question[]) =>
  questions.reduce((acc, q) => ({ ...acc, [q.id]: "" }), {});

export const validate = (questions: Question[]) => (values: Record<string, string>) => {
  const errors: Record<string, string> = {};
  // Removed required field validation - all fields are now optional
  return errors;
};

export function isQuestionVisible(q: Question, values: Record<string, string>) {
  // Add null check for question object
  if (!q) return false;
  if (!q.dependsOn) return true;

  // For questions that depend on w1 (Do you have a will?)
  if (q.dependsOn.questionId === "w1") {
    return values[q.dependsOn.questionId] === q.dependsOn.value;
  }

  // For questions that depend on w2 (Is your will located in your house?)
  if (q.dependsOn.questionId === "w2") {
    // First check if w1 is "Yes"
    if (values["w1"] !== "Yes") {
      return false;
    }
    // Then check if w2 matches the dependency value
    return values[q.dependsOn.questionId] === q.dependsOn.value;
  }

  // For questions that depend on w5 (Do you have an attorney to contact about your will?)
  if (q.dependsOn.questionId === "w5") {
    return values[q.dependsOn.questionId] === q.dependsOn.value;
  }

  // For any other dependencies
  return values[q.dependsOn.questionId] === q.dependsOn.value;
}

interface QuestionItemProps {
  question: Question;
  values: Record<string, any>;
}

export const QuestionItem: React.FC<QuestionItemProps> = ({ question, values }) => {
  // Show all questions without dependency filtering

  return (
    <div className="mb-6">
      <label className="block font-medium text-gray-700 mb-2">
        {question.text}
      </label>

      {question.type === "boolean" ? (
        <div className="flex space-x-4">
          <label className={`flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer 
            ${values[question.id] === 'Yes'
              ? 'bg-[#2BCFD5] text-white border-[#2BCFD5]'
              : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-[#25b6bb] hover:text-white'}
          `}>
            <Field type="radio" name={question.id} value="Yes" className="hidden" />
            Yes
          </label>
          <label className={`flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer 
            ${values[question.id] === 'No'
              ? 'bg-[#2BCFD5] text-white border-[#2BCFD5]'
              : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-[#25b6bb] hover:text-white'}
          `}>
            <Field type="radio" name={question.id} value="No" className="hidden" />
            No
          </label>
        </div>
      ) : (
        <Field
          name={question.id}
          as={question.type === "text" ? "textarea" : "input"}
          type={question.type === "number" ? "number" : "text"}
          className="w-full border rounded-lg px-3 py-2"
          rows={question.type === "text" ? 3 : undefined}
          placeholder={question.placeholder || ""}
        />
      )}
    </div>
  );
};
