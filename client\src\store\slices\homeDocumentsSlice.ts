import { createSlice, createAsyncThunk, PayloadAction, createSelector } from '@reduxjs/toolkit';
import userInputService from '@/services/userInputService';
import homeDocumentsData from '../../data/homeDocuments.json';
import { Question } from '@/web/components/Category/HomeDocuments/FormFields';
import type { EncryptableUserInput, EncryptableSection, EncryptableAnswer } from '@/utils/encryptionUtils';

// Types
export interface SubCategory {
  id: string;
  title: string;
  questionsCount: number;
}

export interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

export interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

// Using imported types from encryptionUtils.ts

export interface UserInput {
  _id?: string;
  userId: string;
  categoryId?: string;
  originalCategoryId: string;
  subCategoryId?: string;
  originalSubCategoryId: string;
  answersBySection: {
    sectionId?: string;
    originalSectionId: string;
    isCompleted: boolean;
    answers: {
      index: number;
      originalQuestionId: string;
      question: string;
      type: string;
      answer: string;
    }[];
  }[];
}

// Helper function to convert EncryptableUserInput to UserInput
const convertToUserInput = (input: EncryptableUserInput): UserInput => {
  return {
    ...input,
    answersBySection: input.answersBySection.map(section => ({
      ...section,
      isCompleted: section.isCompleted ?? false,
      answers: section.answers.map(answer => ({
        ...answer,
        question: answer.question ?? '' // Provide default empty string if question is undefined
      }))
    }))
  };
};

interface HomeDocumentsState {
  subcategories: SubCategory[];
  questions: Record<string, Question[]>;
  userInputs: UserInput[];
  loading: boolean;
  error: string | null;
  progressStats: {
    totalQuestions: number;
    answeredQuestions: number;
    completionPercentage: number;
  };
}

// Normalize questions to ensure all 'choice' questions have an options array
function normalizeQuestions(data: Record<string, any[]>): Record<string, Question[]> {
  const result: Record<string, Question[]> = {};
  for (const [key, questions] of Object.entries(data)) {
    result[key] = questions.map((q) => {
      if (q.type === 'choice') {
        return { ...q, options: q.options || [] };
      }
      return q;
    });
  }
  return result;
}

// Initial state
const initialState: HomeDocumentsState = {
  subcategories: [
    { id: '301', title: 'Utility', questionsCount: (homeDocumentsData['301'] || []).length },
    { id: '302', title: 'Gas', questionsCount: (homeDocumentsData['302'] || []).length },
    { id: '303', title: 'Water', questionsCount: (homeDocumentsData['303'] || []).length },
    { id: '304', title: 'Trash Collection', questionsCount: (homeDocumentsData['304'] || []).length },
    { id: '305', title: 'HVAC', questionsCount: (homeDocumentsData['305'] || []).length },
    { id: '306', title: 'Pest', questionsCount: (homeDocumentsData['306'] || []).length },
    { id: '307', title: 'Lawn', questionsCount: (homeDocumentsData['307'] || []).length },
    { id: '308', title: 'Cable', questionsCount: (homeDocumentsData['308'] || []).length },
    { id: '309', title: 'Internet', questionsCount: (homeDocumentsData['309'] || []).length },
  ],
  questions: normalizeQuestions(homeDocumentsData),
  userInputs: [],
  loading: false,
  error: null,
  progressStats: {
    totalQuestions: Object.values(homeDocumentsData).reduce(
      (sum, questions) => sum + questions.length, 0
    ),
    answeredQuestions: 0,
    completionPercentage: 0
  }
};

// Async thunks
export const fetchUserInputs = createAsyncThunk(
  'homeDocuments/fetchUserInputs',
  async (userOrOwnerId: string) => {
    // Try owner-based fetching first, fallback to user-based
    return await userInputService.getUserInputsByCategory(userOrOwnerId, '2', true);
  }
);

export const saveUserInput = createAsyncThunk(
  'homeDocuments/saveUserInput',
  async (userData: Omit<UserInput, '_id'>) => {
    return await userInputService.saveUserInput(userData);
  }
);

export const updateUserInput = createAsyncThunk(
  'homeDocuments/updateUserInput',
  async ({ id, userData }: { id: string; userData: Omit<UserInput, '_id'> }) => {
    return await userInputService.updateUserInput(id, userData);
  }
);

// Slice
const homeDocumentsSlice = createSlice({
  name: 'homeDocuments',
  initialState,
  reducers: {
    updateProgressStats: (state) => {
      const totalQuestions = Object.values(state.questions).reduce(
        (sum, questions) => sum + questions.length, 0
      );

      // Calculate answered questions with deduplication
      const answeredQuestionIds = new Set<string>();
      state.userInputs.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
              answeredQuestionIds.add(answer.originalQuestionId);
            }
          });
        });
      });

      const answeredQuestions = answeredQuestionIds.size;

      const completionPercentage = totalQuestions > 0
        ? Math.round((answeredQuestions / totalQuestions) * 100)
        : 0;
      state.progressStats = {
        totalQuestions,
        answeredQuestions,
        completionPercentage
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserInputs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserInputs.fulfilled, (state, action: PayloadAction<EncryptableUserInput[]>) => {
        state.loading = false;
        state.userInputs = action.payload.map(convertToUserInput);
        // Update progress stats after fetching user inputs
        const totalQuestions = state.progressStats.totalQuestions;
        let answeredQuestions = 0;
        state.userInputs.forEach(userInput => {
          userInput.answersBySection.forEach((section: SectionAnswers) => {
            answeredQuestions += section.answers.length;
          });
        });
        const completionPercentage = totalQuestions > 0
          ? Math.round((answeredQuestions / totalQuestions) * 100)
          : 0;
        state.progressStats = {
          totalQuestions,
          answeredQuestions,
          completionPercentage
        };
      })
      .addCase(fetchUserInputs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error?.message || 'Failed to fetch user inputs';
      })
      .addCase(saveUserInput.fulfilled, (state, action: PayloadAction<EncryptableUserInput>) => {
        const userInput = convertToUserInput(action.payload);
        state.userInputs.push(userInput);
        // Update progress stats after saving
        state.progressStats.answeredQuestions += userInput.answersBySection.reduce(
          (sum: number, section: SectionAnswers) => sum + section.answers.length, 0
        );
        state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
          ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
          : 0;
      })
      .addCase(saveUserInput.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error?.message || 'Failed to save user input';
      })
      .addCase(updateUserInput.fulfilled, (state, action: PayloadAction<EncryptableUserInput>) => {
        const userInput = convertToUserInput(action.payload);
        const index = state.userInputs.findIndex(input => input._id === userInput._id);
        if (index !== -1) {
          state.userInputs[index] = userInput;
        }
        // Recalculate progress stats
        state.progressStats.answeredQuestions = state.userInputs.reduce(
          (sum: number, input: UserInput) => sum + input.answersBySection.reduce(
            (sectionSum: number, section: SectionAnswers) => sectionSum + section.answers.length, 0
          ), 0
        );
        state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
          ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
          : 0;
      })
      .addCase(updateUserInput.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error?.message || 'Failed to update user input';
      });
  },
});

export const { updateProgressStats } = homeDocumentsSlice.actions;

// Basic selectors
export const selectHomeDocumentsState = (state: { homeDocuments: HomeDocumentsState }) =>
  state.homeDocuments;

export const selectSubcategories = createSelector(
  [selectHomeDocumentsState],
  (homeDocuments) => homeDocuments.subcategories
);

export const selectQuestions = createSelector(
  [selectHomeDocumentsState],
  (homeDocuments) => homeDocuments.questions
);

export const selectUserInputs = createSelector(
  [selectHomeDocumentsState],
  (homeDocuments) => homeDocuments.userInputs
);

export const selectProgressStats = createSelector(
  [selectHomeDocumentsState],
  (homeDocuments) => homeDocuments.progressStats
);

export const selectLoading = createSelector(
  [selectHomeDocumentsState],
  (homeDocuments) => homeDocuments.loading
);

export const selectError = createSelector(
  [selectHomeDocumentsState],
  (homeDocuments) => homeDocuments.error
);

// Memoized selectors with parameters
export const selectSubcategoryById = (subcategoryId: string) =>
  createSelector(
    [selectSubcategories],
    (subcategories) => subcategories.find(subcategory => subcategory.id === subcategoryId)
  );

export const selectQuestionsBySubcategoryId = (subcategoryId: string) =>
  createSelector(
    [selectQuestions],
    (questions) => questions[subcategoryId] || []
  );

export const selectUserInputsBySubcategoryId = (subcategoryId: string) => (state: any) =>
  state.homeDocuments.userInputs.filter((input: UserInput) =>
    input.originalSubCategoryId === subcategoryId
  );

export default homeDocumentsSlice.reducer;
