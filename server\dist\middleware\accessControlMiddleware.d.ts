import { Response, NextFunction } from 'express';
import { AuthRequest } from './authMiddleware';
import { RoleType } from '../types/Role';
export interface AccessControlRequest extends AuthRequest {
    userAccess?: {
        subscriptionType: string;
        allowedCategoryId?: string;
        isOwner: boolean;
        userRole?: RoleType;
        userPermissions?: string[];
        emergencyAccessGranted: boolean;
        canAccessCategory: (categoryId: string) => boolean;
        canEditCategory: (categoryId: string) => boolean;
    };
}
export declare const accessControlMiddleware: (req: AccessControlRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const requireCategoryAccess: (categoryIdParam?: string) => (req: AccessControlRequest, res: Response, next: NextFunction) => void;
export declare const requireCategoryEditAccess: (categoryIdParam?: string) => (req: AccessControlRequest, res: Response, next: NextFunction) => void;
export declare const requireOwnerAccess: (req: AccessControlRequest, res: Response, next: NextFunction) => void;
export declare const requireRole: (requiredRole: RoleType) => (req: AccessControlRequest, res: Response, next: NextFunction) => void;
export declare const requirePermission: (requiredPermission: string) => (req: AccessControlRequest, res: Response, next: NextFunction) => void;
export declare const requireSubscriptionType: (allowedTypes: string[]) => (req: AccessControlRequest, res: Response, next: NextFunction) => void;
