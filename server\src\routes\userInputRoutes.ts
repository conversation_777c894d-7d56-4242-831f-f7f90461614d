import express from 'express';
import {
  createUserInput,
  getUserInput,
  getUserInputByUserId,
  getUserInputByOwnerId,
  updateUserInput,
  getDashboardStats
} from '../controller/userInputController';

const router = express.Router();

// Define the dashboard stats route (now owner-based only)
router.get('/dashboard/stats', getDashboardStats);

// Define the owner-based routes
router.get('/by-owner', getUserInputByOwnerId);

// Then define the other routes
router.post('/', createUserInput);
router.get('/:id', getUserInput);
router.get('/', getUserInputByUserId);
router.patch('/:id', updateUserInput);

export default router;
