export interface PricingPlan {
  _id: string;
  type: string;
  displayPrice: string;
  tagline: string;
  features: string[];
  price: number;
  billingCycle?: 'monthly' | 'annual' | 'other';
  isActive?: boolean;
  createdAt: string;
  updatedAt: string;
  duration?: number;
  active?: boolean;
}

export interface Subscription {
  _id: string;
  userId?: string;
  planId: string;
  status?: 'active' | 'cancelled' | 'expired';
  startDate?: string;
  endDate?: string;
  autoRenew?: boolean;
  createdAt: string;
  updatedAt: string;
  isActive?: boolean;
  expiryAt?: string;
}

export interface SubscriptionState {
  plans: PricingPlan[];
  currentSubscription: Subscription | null;
  loading: boolean;
  error: string | null;
} 