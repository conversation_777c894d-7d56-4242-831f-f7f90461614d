export declare const generateEmailVerificationTemplate: (username: string, otp: string) => {
    subject: string;
    text: string;
    html: string;
};
export declare const generatePasswordResetTemplate: (username: string, resetUrl: string) => {
    subject: string;
    text: string;
    html: string;
};
export declare const generatePasswordResetSuccessTemplate: (username: string) => {
    subject: string;
    text: string;
    html: string;
};
export declare const generateEmergencyAccessRequestTemplate: (ownerName: string, keyHolderName: string) => {
    subject: string;
    text: string;
    html: string;
};
export declare const generateEmergencyAccessApprovalTemplate: (keyHolderName: string, ownerName: string) => {
    subject: string;
    text: string;
    html: string;
};
export declare const generateEmergencyAccessRejectionTemplate: (keyHolderName: string, ownerName: string) => {
    subject: string;
    text: string;
    html: string;
};
export declare const generateEmergencyAccessAutoGrantTemplate: (keyHolderName: string, ownerName: string) => {
    subject: string;
    text: string;
    html: string;
};
