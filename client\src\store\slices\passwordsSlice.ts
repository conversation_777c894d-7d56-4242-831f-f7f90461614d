import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import userInputService from '@/services/userInputService';
import passwordsData from '@/data/Passwords.json';
import type { EncryptableUserInput } from '@/utils/encryptionUtils';

// Types
export interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

export interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

export interface UserInput {
  _id?: string;
  userId: string;
  categoryId?: string;
  originalCategoryId: string;
  subCategoryId?: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
}

export interface Subcategory {
  id: string;
  title: string;
  questionsCount: number;
}

export interface ProgressStats {
  totalQuestions: number;
  answeredQuestions: number;
  completionPercentage: number;
}

export interface PasswordsState {
  subcategories: Subcategory[];
  questions: Record<string, any[]>;
  userInputs: UserInput[];
  formValues: Record<string, any>;
  loading: boolean;
  error: string | null;
  progressStats: ProgressStats;
}

// Helper function to convert EncryptableUserInput to UserInput
const convertToUserInput = (input: EncryptableUserInput): UserInput => {
  return {
    ...input,
    answersBySection: input.answersBySection.map(section => ({
      ...section,
      isCompleted: section.isCompleted ?? false,
      answers: section.answers.map(answer => ({
        ...answer,
        question: answer.question ?? '',
        type: answer.type ?? 'text'
      }))
    }))
  };
};

const initialState: PasswordsState = {
  subcategories: [
    {
      id: '1204A',
      title: 'Other Passwords',
      questionsCount: 2,
    },
  ],
  questions: passwordsData,
  userInputs: [],
  formValues: {},
  loading: false,
  error: null,
  progressStats: {
    totalQuestions: 0,
    answeredQuestions: 0,
    completionPercentage: 0,
  },
};

// Async thunks
export const fetchUserInputs = createAsyncThunk(
  'passwords/fetchUserInputs',
  async (ownerId: string, { rejectWithValue }) => {
    try {
      const response = await userInputService.getUserInputsByCategory(ownerId, '12', true); // Category ID 12 for Passwords, useOwnerId: true
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch user inputs');
    }
  }
);

export const saveUserInput = createAsyncThunk(
  'passwords/saveUserInput',
  async (userData: any, { rejectWithValue }) => {
    try {
      const response = await userInputService.saveUserInput(userData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to save user input');
    }
  }
);

export const updateUserInput = createAsyncThunk(
  'passwords/updateUserInput',
  async ({ id, userData }: { id: string; userData: any }, { rejectWithValue }) => {
    try {
      const response = await userInputService.updateUserInput(id, userData);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update user input');
    }
  }
);

// Slice
const passwordsSlice = createSlice({
  name: 'passwords',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateFormValues: (state, action) => {
      state.formValues = { ...state.formValues, ...action.payload };
    },
    resetFormValues: (state) => {
      state.formValues = {};
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch user inputs
      .addCase(fetchUserInputs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserInputs.fulfilled, (state, action) => {
        state.loading = false;
        state.userInputs = action.payload.map(convertToUserInput);

        // Calculate progress stats
        const totalQuestions = Object.values(state.questions).flat().length;
        const answeredQuestions = state.userInputs.reduce((count: number, input: UserInput) => {
          return count + input.answersBySection.reduce((sectionCount: number, section: SectionAnswers) => {
            return sectionCount + section.answers.filter(answer => answer.answer && answer.answer.trim() !== '').length;
          }, 0);
        }, 0);

        state.progressStats = {
          totalQuestions,
          answeredQuestions,
          completionPercentage: totalQuestions > 0 ? Math.round((answeredQuestions / totalQuestions) * 100) : 0,
        };
      })
      .addCase(fetchUserInputs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Save user input
      .addCase(saveUserInput.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(saveUserInput.fulfilled, (state, action) => {
        state.loading = false;
        state.userInputs.push(convertToUserInput(action.payload));
      })
      .addCase(saveUserInput.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update user input
      .addCase(updateUserInput.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserInput.fulfilled, (state, action) => {
        state.loading = false;
        const convertedInput = convertToUserInput(action.payload);
        const index = state.userInputs.findIndex(input => input._id === convertedInput._id);
        if (index !== -1) {
          state.userInputs[index] = convertedInput;
        }
      })
      .addCase(updateUserInput.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Selectors
export const selectSubcategories = (state: { passwords: PasswordsState }) => state.passwords.subcategories;
export const selectUserInputs = (state: { passwords: PasswordsState }) => state.passwords.userInputs;
export const selectLoading = (state: { passwords: PasswordsState }) => state.passwords.loading;
export const selectError = (state: { passwords: PasswordsState }) => state.passwords.error;
export const selectProgressStats = (state: { passwords: PasswordsState }) => state.passwords.progressStats;
export const selectFormValues = (state: { passwords: PasswordsState }) => state.passwords.formValues;
export const selectQuestions = (state: { passwords: PasswordsState }) => state.passwords.questions;

// Selector for questions by subcategory ID
export const selectQuestionsBySubcategoryId = (subcategoryId: string) =>
  createSelector([selectQuestions], (questions) => {
    return questions[subcategoryId] || [];
  });

// Selector for user inputs by subcategory ID
export const selectUserInputsBySubcategoryId = (subcategoryId: string) =>
  createSelector([selectUserInputs], (userInputs) => {
    return userInputs.filter(input => input.originalSubCategoryId === subcategoryId);
  });

export const { clearError, updateFormValues, resetFormValues } = passwordsSlice.actions;
export default passwordsSlice.reducer;
