import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/User';
import { IUser } from '../types/User';
import mongoose from 'mongoose';

// Define a more compatible AuthRequest interface
export interface AuthRequest extends Request {
    user?: IUser | any;
}

// Combined middleware that handles both JWT and Passport authentication
export const combinedAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        // First check if user is authenticated via Passport
        if (req.isAuthenticated()) {
            console.log('User authenticated via Passport session');
            next();
            return;
        }

        // If not authenticated via Passport, check JWT token
        const token = req.header('Authorization')?.split(' ')[1];
        if (!token) {
            console.log('No token provided in Authorization header');

            // Check if userId is provided in the request body as a fallback
            if (req.body && req.body.userId) {
                console.log('Using userId from request body:', req.body.userId);
                try {
                    const user = await User.findById(req.body.userId);
                    if (user) {
                        console.log('User found by ID from request body');
                        (req as any).user = user;
                        next();
                        return;
                    }
                } catch (err) {
                    console.error('Error finding user by ID from request body:', err);
                }
            }

            res.status(401).json({ message: 'Access Denied - No token provided' });
            return;
        }

        try {
            // Try to decode the token
            let decoded;
            try {
                // First try to decode as { userId: string }
                decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { userId: string };
                console.log('Token decoded with userId format');
            } catch (e) {
                // If that fails, try to decode as { id: string }
                try {
                    decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { id: string };
                    console.log('Token decoded with id format');
                } catch (e2) {
                    throw new Error('Invalid token format');
                }
            }

            // Fix the decoded token property access
            const userId = 'userId' in decoded ? decoded.userId : 'id' in decoded ? decoded.id : undefined;

            if (!userId) {
                res.status(401).json({ message: 'Invalid token format' });
                return;
            }

            console.log('Looking up user with ID:', userId);

            const user = await User.findById(userId);

            if (!user) {
                console.log('User not found with ID:', userId);
                res.status(401).json({ message: 'User not found' });
                return;
            }

            console.log('User authenticated via JWT token');
            // Attach user to request
            (req as any).user = user;
            next();
        } catch (error) {
            console.error('Token verification error:', error);
            res.status(401).json({ message: 'Invalid token' });
            return;
        }
    } catch (error) {
        res.status(500).json({ message: 'Authentication error' });
        return;
    }
};

// Keep the original middleware for specific use cases
export const authMiddleware = (req: AuthRequest, res: Response, next: NextFunction): void => {
    try {
        const token = req.header('Authorization')?.split(' ')[1];
        if (!token) {
            res.status(401).json({ message: 'Access Denied' });
            return;
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { userId: string };
        req.user = { _id: new mongoose.Types.ObjectId(decoded.userId) } as IUser;
        next();
    } catch (error) {
        res.status(401).json({ message: 'Invalid Token' });
    }
};

export const ensureAuth = (req: Request, res: Response, next: NextFunction): void => {
    if (req.isAuthenticated()) {
        next();
        return;
    }
    res.status(401).json({ status: 'fail', message: 'You are not logged in' });
};
