import { Link, useParams } from 'react-router-dom';
import { Avatar } from '@radix-ui/react-avatar';
import { Progress } from '@/components/ui/progress';
import { CheckCircle2 } from 'lucide-react';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { useAuth } from '@/contexts/AuthContext';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  SubCategory,
  UserInput,
  selectSubcategories,
  selectDynamicOverallProgress,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId
} from '../../../../store/slices/homeInstructionsSlice';
import { useEffect } from 'react';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { calculateProgress } from '@/web/components/Category/HomeInstructions/FormFields';
import { convertUserInputToFormValues } from '@/services/userInputService';
import { createUserInfo } from '@/utils/avatarUtils';


const SubCategoryCard = ({ subcategory }: { subcategory: SubCategory }) => {
  // Get the questions and user answers for this subcategory
  const questions = useAppSelector(selectQuestionsBySubcategoryId(subcategory.id));
  const userInputs = useAppSelector(selectUserInputsBySubcategoryId(subcategory.id));
  const savedAnswers = userInputs[0] ? convertUserInputToFormValues(userInputs[0]) : {};

  // Use the same progress logic as your forms
  const progress = calculateProgress(questions as import('@/web/components/Category/HomeInstructions/FormFields').Question[], savedAnswers);

  return (
    <div className="border rounded-lg overflow-hidden transition-shadow hover:shadow-md">
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium text-[#1F4168]">{subcategory.title}</h3>
          <span className="text-sm text-blue-500">
            {progress.answeredQuestions}/{progress.totalQuestions} questions
          </span>
        </div>
        <Progress
          value={progress.completionPercentage}
          className="h-1.5 mb-2"
        />
      </div>
    </div>
  );
};

const HomeInstructions = ({ category }: { category?: string }) => {
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const params = useParams();
  const categoryName = category || params.categoryName;

  // Get data from Redux store
  const subcategories = useAppSelector(selectSubcategories);
  const overallProgress = useAppSelector(selectDynamicOverallProgress);

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            // Fallback to user ID if no owner found
            dispatch(fetchUserInputs(user.id));
          }
        } catch (error) {
          console.error('Error fetching owner ID, falling back to user ID:', error);
          dispatch(fetchUserInputs(user.id));
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Fallback user info if not authenticated
  const userInfo = createUserInfo(user);

  const tabs = categoryTabsConfig[categoryName as keyof typeof categoryTabsConfig] || categoryTabsConfig['homeinstructions'];

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />

      {/* Header with gradient background */}
      <div className="bg-gradient-to-r from-[#1F4168] to-[#2BCFD5] text-white py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-1">Home Instructions</h1>
              <Link to="/dashboard" className="flex items-center text-sm hover:underline text-[#2BCFD5] font-semibold text-md mt-1 mb-1">
                <span className="mr-1">←</span> Back to Home
              </Link>
            </div>
            <div className="flex items-center">
              <div className="text-right mr-4">
                <div className="font-semibold">{userInfo.name}</div>
                <div className="text-sm opacity-80">{userInfo.email}</div>
              </div>
              <Avatar className="rounded-full w-14 h-14 bg-white overflow-hidden">
                <img
                  src={userInfo.avatar}
                  alt={userInfo.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = avatar; // Fallback to default avatar
                  }}
                />
              </Avatar>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <SubCategoryTabs tabs={tabs} />
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Categories */}
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              {/* Overall progress bar */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium text-gray-700">Overall progress</h3>
                  <span className="text-sm text-gray-500">
                    {overallProgress.answeredQuestions}/{overallProgress.totalQuestions} questions completed
                  </span>
                </div>
                <Progress
                  value={overallProgress.completionPercentage}
                  className="h-2"
                />
                {overallProgress.completionPercentage === 100 && (
                  <div className="mt-2 text-center">
                    <span className="inline-flex items-center text-sm text-green-600 font-medium">
                      <CheckCircle2 className="h-4 w-4 mr-1" /> All questions completed!
                    </span>
                  </div>
                )}
              </div>

              <h2 className="text-xl font-semibold text-[#1F4168] mb-2">Good to Know: <span className="text-purple-600">How to Understand Topics</span></h2>
              <p className="text-gray-600 mb-6">
                Each topic below is a part of your home documents, with questions to help you provide important
                information for you and your loved ones. Click on a category to answer questions at your own pace—
                we'll save everything for you.
              </p>

              {/* Subcategory cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-6">
                {(() => {
                  const subcategoryPaths: Record<string, string> = {
                    '105': 'homelocation',
                    '101': 'pets',
                    '102': 'trash',
                    '104': 'security',
                    '103': 'other',
                  };
                  return ['105', '101', '102', '104', '103']
                    .map(id => subcategories.find((subcategory: SubCategory) => subcategory.id === id))
                    .filter(Boolean)
                    .map(subcategory => {
                      const sub = subcategory as SubCategory;
                      return (
                        <Link key={sub.id} to={`/category/${categoryName}/${subcategoryPaths[sub.id]}`} className="block">
                          <SubCategoryCard subcategory={sub} />
                        </Link>
                      );
                    });
                })()}
              </div>
            </div>
          </div>

          {/* Right column - Search panel */}
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default HomeInstructions;