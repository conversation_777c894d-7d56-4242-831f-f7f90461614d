import React, { useEffect, useRef } from 'react';
import { Question } from './FormFields';

interface ScrollToQuestionProps {
  questions: Question[];
  children: (refs: Record<string, HTMLDivElement | null>) => React.ReactNode;
}

const ScrollToQuestion: React.FC<ScrollToQuestionProps> = ({ questions, children }) => {
  const refs = useRef<Record<string, HTMLDivElement | null>>({});

  useEffect(() => {
    // Get questionId from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const questionId = urlParams.get('questionId');
    
    if (questionId && refs.current[questionId]) {
      // Small delay to ensure the component is fully rendered
      setTimeout(() => {
        const element = refs.current[questionId];
        if (element) {
          element.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
          });
          
          // Add a highlight effect
          element.style.backgroundColor = '#fef3c7';
          element.style.transition = 'background-color 0.3s ease';
          
          // Remove highlight after 2 seconds
          setTimeout(() => {
            element.style.backgroundColor = '';
          }, 2000);
        }
      }, 100);
    }
  }, [questions]);

  return (
    <>
      {children(refs.current)}
    </>
  );
};

export default ScrollToQuestion;
