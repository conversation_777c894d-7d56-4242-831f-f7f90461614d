import { createSlice, createAsyncThunk, createSelector } from '@reduxjs/toolkit';
import userInputService, { generateObjectId } from '@/services/userInputService';
import bankingInformationData from '@/data/banking.json';

// Types
export interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

export interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

export interface UserInput {
  _id: string;
  userId: string;
  categoryId: string;
  originalCategoryId: string;
  subCategoryId: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
}

export interface Subcategory {
  id: string;
  title: string;
  questionsCount: number;
}

export interface ProgressStats {
  totalQuestions: number;
  answeredQuestions: number;
  completionPercentage: number;
}

export interface BankingInformationState {
  subcategories: Subcategory[];
  questions: Record<string, any[]>;
  userInputs: UserInput[];
  formValues: Record<string, any>;
  loading: boolean;
  error: string | null;
  progressStats: ProgressStats;
}

// Async thunks
export const fetchUserInputs = createAsyncThunk<UserInput[], string>(
  'bankingInformation/fetchUserInputs',
  async (userOrOwnerId: string, { rejectWithValue }) => {
    try {
      const response = await userInputService.getUserInputsByCategory(userOrOwnerId, '13', true);
      return response as UserInput[];
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to fetch user inputs');
    }
  }
);

export const saveUserInput = createAsyncThunk<UserInput, Omit<UserInput, '_id'>>(
  'bankingInformation/saveUserInput',
  async (userData: Omit<UserInput, '_id'>, { rejectWithValue }) => {
    try {
      // Ensure originalCategoryId is '13' for banking information
      const dataToSave = { ...userData, originalCategoryId: '13' };
      const response = await userInputService.saveUserInput(dataToSave);
      return response as UserInput;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to save user input');
    }
  }
);

export const updateUserInput = createAsyncThunk<
  UserInput,
  { id: string, userData: Omit<UserInput, '_id'> }
>(
  'bankingInformation/updateUserInput',
  async ({ id, userData }: { id: string, userData: Omit<UserInput, '_id'> }, { rejectWithValue }) => {
    try {
      const response = await userInputService.updateUserInput(id, userData);
      return response as UserInput;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || error.message || 'Failed to update user input');
    }
  }
);

// Define initial state
const initialState: BankingInformationState = {
  subcategories: [
    {
      id: '405A',
      title: 'Bank Details',
      questionsCount: bankingInformationData['405']?.filter(q => q.sectionId === '405A')?.length || 0
    }
  ],
  questions: bankingInformationData,
  userInputs: [],
  formValues: {},
  loading: false,
  error: null,
  progressStats: {
    totalQuestions: Object.values(bankingInformationData).reduce(
      (sum, questions) => sum + questions.length, 0
    ),
    answeredQuestions: 0,
    completionPercentage: 0
  }
};

// Create slice
const bankingInformationSlice = createSlice({
  name: 'bankingInformation',
  initialState,
  reducers: {
    updateFormValues: (state, action) => {
      state.formValues = { ...state.formValues, ...action.payload };
    },
    updateProgressStats: (state) => {
      // Calculate total questions
      const totalQuestions = Object.values(state.questions).reduce(
        (sum, questions) => sum + questions.length, 0
      );

      // Calculate answered questions with deduplication
      const answeredQuestionIds = new Set<string>();
      state.userInputs.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
              answeredQuestionIds.add(answer.originalQuestionId);
            }
          });
        });
      });

      const answeredQuestions = answeredQuestionIds.size;

      // Calculate completion percentage
      const completionPercentage = totalQuestions > 0
        ? Math.round((answeredQuestions / totalQuestions) * 100)
        : 0;

      state.progressStats = {
        totalQuestions,
        answeredQuestions,
        completionPercentage
      };
    },
  },
  extraReducers: (builder) => {
    // Fetch user inputs
    builder.addCase(fetchUserInputs.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchUserInputs.fulfilled, (state, action) => {
      state.loading = false;
      state.userInputs = action.payload;
      bankingInformationSlice.caseReducers.updateProgressStats(state);
    });
    builder.addCase(fetchUserInputs.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Save user input
    builder.addCase(saveUserInput.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(saveUserInput.fulfilled, (state, action) => {
      state.loading = false;
      state.userInputs.push(action.payload);
      bankingInformationSlice.caseReducers.updateProgressStats(state);
    });
    builder.addCase(saveUserInput.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Update user input
    builder.addCase(updateUserInput.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateUserInput.fulfilled, (state, action) => {
      state.loading = false;
      const index = state.userInputs.findIndex(input => input._id === action.payload._id);
      if (index !== -1) {
        state.userInputs[index] = action.payload;
      }
      bankingInformationSlice.caseReducers.updateProgressStats(state);
    });
    builder.addCase(updateUserInput.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
  },
});

export const { updateFormValues, updateProgressStats } = bankingInformationSlice.actions;

// Basic selectors
export const selectBankingInformationState = (state: { bankingInformation: BankingInformationState }) =>
  state.bankingInformation;

export const selectSubcategories = createSelector(
  [selectBankingInformationState],
  (bankingInformation) => bankingInformation.subcategories
);

export const selectQuestions = createSelector(
  [selectBankingInformationState],
  (bankingInformation) => bankingInformation.questions
);

export const selectUserInputs = createSelector(
  [selectBankingInformationState],
  (bankingInformation) => bankingInformation.userInputs
);

export const selectFormValues = createSelector(
  [selectBankingInformationState],
  (bankingInformation) => bankingInformation.formValues
);

export const selectLoading = createSelector(
  [selectBankingInformationState],
  (bankingInformation) => bankingInformation.loading
);

export const selectError = createSelector(
  [selectBankingInformationState],
  (bankingInformation) => bankingInformation.error
);

export const selectProgressStats = createSelector(
  [selectBankingInformationState],
  (bankingInformation) => bankingInformation.progressStats
);

// Advanced selectors
export const selectQuestionsBySubcategoryId = (subcategoryId: string) =>
  createSelector([selectQuestions], (questions) => {
    const allQuestions = Object.values(questions).flat();
    return allQuestions.filter((q: any) => q.sectionId === subcategoryId);
  });

export const selectUserInputsBySubcategoryId = (subcategoryId: string) =>
  createSelector([selectUserInputs], (userInputs) => {
    return userInputs.filter(input => 
      input.answersBySection.some(section => section.originalSectionId === subcategoryId)
    );
  });

export default bankingInformationSlice.reducer;
