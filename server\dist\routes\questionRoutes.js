"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const questionController_1 = require("../controller/questionController");
const authMiddleware_1 = require("../middleware/authMiddleware");
const router = (0, express_1.Router)();
router.post('/create', questionController_1.createQuestion);
router.get('/', questionController_1.getQuestions);
router.get('/:id', questionController_1.getQuestionById);
router.put('/:id', questionController_1.updateQuestion);
router.delete('/:id', questionController_1.deleteQuestion);
// New routes for category-based questions and saving answers
router.get('/category/:categoryId', questionController_1.getQuestionsByCategory);
router.get('/subcategory/:subCategoryId', questionController_1.getQuestionsBySubCategory);
router.post('/answers', authMiddleware_1.combinedAuth, questionController_1.saveAnswers);
exports.default = router;
//# sourceMappingURL=questionRoutes.js.map