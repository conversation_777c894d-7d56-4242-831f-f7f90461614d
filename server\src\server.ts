import dotenv from 'dotenv';
import connectDB from './config/db';
import app from './app';
import User from './models/User';
import Owner from './models/Owner';
import { sendEmergencyAccessAutoGrantEmail } from './utils/email';


dotenv.config();

const PORT = process.env.PORT || 3000;
// const PUBLIC_IP = '*************';
const PUBLIC_IP = '*************';

connectDB();

// Auto-grant emergency access after 20 minutes
setInterval(async () => {
  try {
    const now = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000;
    const usersToAutoGrant = await User.find({
      emergencyAccessRequested: true,
      emergencyAccessGranted: false,
      emergencyAccessDenied: false,
      emergencyAccessRequestedAt: { $lte: new Date(now - twentyFourHours) }
    });
    for (const user of usersToAutoGrant) {
      user.emergencyAccessGranted = true;
      user.emergencyAccessGrantedAt = new Date();
      await user.save();
      // Find owner for email
      const owner = await Owner.findById(user.ownerId);
      const ownerName = owner?.firstName || owner?.username || 'Owner';
      const keyHolderName = user.firstName || user.username || user.email || 'Key Holder';
      try {
        await sendEmergencyAccessAutoGrantEmail(user.email, keyHolderName, ownerName);
        console.log(`Auto-granted emergency access for ${user.email}`);
      } catch (err) {
        console.error('Failed to send auto-grant email:', err);
      }
    }
  } catch (err) {
    console.error('Auto-grant interval error:', err);
  }
}, 60 * 1000); // Check every minute

app.listen(Number(PORT), '0.0.0.0', () => {
  console.log(`Server is running on http://localhost:${PORT}`);
  console.log(`Server is running on http://0.0.0.0:${PORT}`);
  console.log(`You can access it at http://${PUBLIC_IP}:${PORT}`);
});
