import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId,
  updateUserInput,
} from '@/store/slices/subscriptionCategorySlice';
import {
  QuestionItem,
  buildValidationSchema,
  generateInitialValues,
} from '@/web/components/Category/Subscription/FormFields';
import ScrollToQuestion from '@/web/components/Category/Subscription/ScrollToQuestion';
import GoodToKnowBox from '@/web/components/Global/GoodToKnowBox';
import SubCategoryFooterNav from '@/web/components/Global/SubCategoryFooterNav';
import SubCategoryHeader from '@/web/components/Global/SubCategoryHeader';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import SubCategoryTitle from '@/web/components/Global/SubCategoryTitle';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { Form, Formik, FormikHelpers } from 'formik';
import { useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import * as Yup from 'yup';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';

const AMAZON_PRIME_SECTION_ID = '1103';

const AmazonPrime = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  const storeQuestions = useAppSelector(selectQuestionsBySubcategoryId(AMAZON_PRIME_SECTION_ID));
  const loading = useAppSelector(selectLoading);
  const userInputs = useAppSelector(selectUserInputsBySubcategoryId('1103A'));

  const userInput = useMemo(
    () => userInputs.find((input: UserInput) => input.originalSubCategoryId === '1103A'),
    [userInputs]
  );

  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');

  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in Amazon Prime component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in Amazon Prime component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  useEffect(() => {
    if (!loading && targetQuestionId) {
      setTimeout(() => {
        const element = document.getElementById(`question-${targetQuestionId}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.classList.add('bg-yellow-100');
          setTimeout(() => {
            element.classList.remove('bg-yellow-100');
          }, 2000);
        }
      }, 500);
    }
  }, [loading, targetQuestionId]);

  const savedAnswers = userInput ? convertUserInputToFormValues(userInput) : {};
  const existingInputId = userInput?._id || null;
  const validationSchema = buildValidationSchema(storeQuestions);
  const baseInitialValues = generateInitialValues(storeQuestions);
  const initialValues = { ...baseInitialValues, ...savedAnswers };

  const handleSubmit = async (values: Record<string, string>, { setSubmitting }: FormikHelpers<Record<string, string>>) => {
    try {
      if (!user || !user.id) {
        throw new Error('You must be logged in to save answers');
      }

      // Check if user has Amazon Prime (s7 question)
      const hasAmazonPrime = values['s7'] === 'yes';

      const answersBySection = storeQuestions
        .reduce((sections: Record<string, Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>>, question) => {
          if (!sections[question.sectionId]) {
            sections[question.sectionId] = [];
          }
          const answer = values[question.id];
          
          // Only save the answer if:
          // 1. It's the main question (s7 - "Do you have Amazon Prime?")
          // 2. OR it's a credential field (s8, s9) AND user has Amazon Prime
          if (answer !== undefined && answer !== '') {
            if (question.id === 's7' || (hasAmazonPrime && (question.id === 's8' || question.id === 's9'))) {
              // Map question types to backend-compatible types
              let answerType = question.type;
              if (question.type === 'password') {
                answerType = 'text';
              } else if (question.type === 'dropdown') {
                answerType = 'choice';
              }
              
              sections[question.sectionId].push({
                index: sections[question.sectionId].length,
                originalQuestionId: question.id,
                question: question.text,
                type: answerType,
                answer
              });
            }
          }
          return sections;
        }, {});

      const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
        originalSectionId: sectionId,
        isCompleted: true,
        answers: answers as Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: string;
        }>
      }));

      const hasAnswers = formattedAnswersBySection.some(section => section.answers.length > 0);
      if (!hasAnswers) {
        setSubmitting(false);
        return;
      }

      const userData = {
        userId: user.id,
        categoryId: generateObjectId(),
        originalCategoryId: '11',
        subCategoryId: generateObjectId(),
        originalSubCategoryId: '1103A',
        answersBySection: formattedAnswersBySection
      };

      if (existingInputId) {
        await dispatch(updateUserInput({
          id: existingInputId,
          userData
        })).unwrap();
      } else {
        await dispatch(saveUserInput(userData)).unwrap();
      }
      setSubmitting(false);
      navigate('/category/subscription/otherservices');
    } catch (error: any) {
      setSubmitting(false);
    }
  };

  if (storeQuestions.length === 0 || loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      <SubCategoryHeader
        title="Subscription"
        backTo="/dashboard"
        user={{
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
          email: user?.email || '<EMAIL>',
          avatar: createUserInfo(user).avatar
        }}
      />
      <SubCategoryTabs tabs={categoryTabsConfig.subscription} />
      <div className="container mx-auto px-6">
        <SubCategoryTitle
          mainCategory="Subscription"
          category="Amazon Prime"
          description="These files contain questions to help you record your subscription details so they're easy to find later."
        />
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
              >
                {({ values, isSubmitting, isValid, dirty }) => (
                  <Form>
                    <ScrollToQuestion questions={storeQuestions}>
                      {(refs) => (
                        <>
                          {[...storeQuestions]
                            .sort((a, b) => a.order - b.order)
                            .map((question) => (
                              <div
                                key={question.id}
                                id={`question-${question.id}`}
                                ref={(el) => {
                                  if (el) refs[question.id] = el;
                                }}
                              >
                                <QuestionItem question={question} values={values} />
                              </div>
                            ))}
                        </>
                      )}
                    </ScrollToQuestion>
                    <div className="mt-8 flex justify-end">
                      <Button
                        type="submit"
                        disabled={isSubmitting || !isValid || !dirty}
                        className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                      >
                        Save & Continue
                      </Button>
                    </div>
                    <GoodToKnowBox
                      title="Editing my Answers"
                      description="Each topic below is a part of your subscription services, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you."
                    />
                    <SubCategoryFooterNav
                      leftLabel="Apple TV"
                      leftTo="/category/subscription/appletv"
                      rightLabel="Other Services"
                      rightTo="/category/subscription/otherservices"
                    />
                  </Form>
                )}
              </Formik>
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default AmazonPrime;
