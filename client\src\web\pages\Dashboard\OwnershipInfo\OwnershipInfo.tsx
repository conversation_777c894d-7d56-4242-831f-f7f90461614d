import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/contexts/AuthContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchUserInputs,
  selectProgressStats,
  selectSubcategories,
  selectUserInputs,
  selectLoading,
  selectError,
  SubCategory,
  UserInput
} from '@/store/slices/ownershipInfoSlice';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { Avatar } from '@radix-ui/react-avatar';
import { CheckCircle2 } from 'lucide-react';
import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';

const sectionTitles = {
  '402A': 'Property Information',
  '402B': 'Vehicle Information',
  '402C': 'Driver Information',
};

// Create tabs array with proper structure
const tabs = Object.entries(sectionTitles).map(([, title]) => ({
  label: title,
  path: `/category/ownershipinfo/${title.toLowerCase().replace(/\s/g, '')}`
}));

const SubCategoryCard = ({ subcategory }: { subcategory: SubCategory }) => {
  // Get the user inputs from Redux state
  const userInputs = useAppSelector(selectUserInputs);

  // Calculate completed questions for this subcategory
  const completedQuestions = userInputs.reduce((count: number, input: UserInput) => {
    // Match the first 3 characters of originalSubCategoryId to subcategory.id
    if (input.originalSubCategoryId && input.originalSubCategoryId.startsWith(subcategory.id)) {
      return count + input.answersBySection.reduce(
        (sectionCount: number, section) => sectionCount + section.answers.length, 0
      );
    }
    return count;
  }, 0);

  const completionPercentage = subcategory.questionsCount > 0
    ? Math.round((completedQuestions / subcategory.questionsCount) * 100)
    : 0;

  return (
    <div className="border rounded-lg overflow-hidden transition-shadow hover:shadow-md">
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium text-[#1F4168]">{subcategory.title}</h3>
          <span className="text-sm text-blue-500">
            {completedQuestions}/{subcategory.questionsCount} questions
          </span>
        </div>
        <Progress value={completionPercentage} className="h-1.5 mb-2" />
      </div>
    </div>
  );
};

const OwnershipInfo = () => {
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  // Get data from Redux store
  const subcategories = useAppSelector(selectSubcategories);
  const progressStats = useAppSelector(selectProgressStats);
  const loading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            // Fallback to user ID if no owner found
            dispatch(fetchUserInputs(user.id));
          }
        } catch (error) {
          console.error('Error fetching owner ID, falling back to user ID:', error);
          dispatch(fetchUserInputs(user.id));
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: createUserInfo(user).avatar
  };

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      {/* Header with gradient background and user info */}
      <div className="bg-gradient-to-r from-[#1F4168] to-[#2BCFD5] text-white py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-1">Ownership Information</h1>
              <Link to="/dashboard" className="flex items-center text-sm hover:underline text-[#2BCFD5] font-semibold text-md mt-1 mb-1">
                <span className="mr-1">←</span> Back to Home
              </Link>
            </div>
            <div className="flex items-center">
              <div className="text-right mr-4">
                <div className="font-semibold">{userInfo.name}</div>
                <div className="text-sm opacity-80">{userInfo.email}</div>
              </div>
              <Avatar className="rounded-full w-14 h-14 bg-white overflow-hidden">
                <img
                  src={userInfo.avatar}
                  alt={userInfo.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = avatar; // Fallback to default avatar
                  }}
                />
              </Avatar>
            </div>
          </div>
        </div>
      </div>
      {/* Subcategory Tabs */}
      <SubCategoryTabs tabs={tabs} />
      {/* Main content */}
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Categories */}
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              {/* Overall progress bar */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium text-gray-700">Overall progress</h3>
                  <span className="text-sm text-gray-500">
                    {progressStats.answeredQuestions}/{progressStats.totalQuestions} questions completed
                  </span>
                </div>
                <Progress
                  value={progressStats.completionPercentage}
                  className="h-2"
                />
                {progressStats.completionPercentage === 100 && (
                  <div className="mt-2 text-center">
                    <span className="inline-flex items-center text-sm text-green-600 font-medium">
                      <CheckCircle2 className="h-4 w-4 mr-1" /> All questions completed!
                    </span>
                  </div>
                )}
              </div>
              {/* Info Box */}
              <h2 className="text-xl font-semibold text-[#1F4168] mb-2">Good to Know: <span className="text-purple-600">How to Understand Topics</span></h2>
              <p className="text-gray-600 mb-6">
                Each topic below is a part of your ownership information, with questions to help you provide important
                information for you and your loved ones. Click on a category to answer questions at your own pace—
                we'll save everything for you.
              </p>
              {/* Loading state */}
              {loading && (
                <div className="text-center py-4">
                  <p className="text-gray-500">Loading your ownership information...</p>
                </div>
              )}

              {/* Error state */}
              {error && (
                <div className="text-center py-4">
                  <p className="text-red-500">{error}</p>
                </div>
              )}

              {/* Subcategory cards */}
              {!loading && !error && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-6">
                  {subcategories.map((subcategory: SubCategory) => (
                    <Link key={subcategory.id} to={`/category/ownershipinfo/${subcategory.title.toLowerCase().replace(/\s/g, '')}`} className="block">
                      <SubCategoryCard subcategory={subcategory} />
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
          {/* Right column - Search panel */}
          <div>
            <SearchPanel />
            {/* Still have questions box */}
            <div className="bg-[#223a5f] text-white rounded-lg p-4 mt-6">
              <div className="mb-2 font-semibold">Still have questions?</div>
              <div className="mb-4 text-sm opacity-80">
                Can't find the answer you're looking for? Please chat to our friendly team.
              </div>
              <button className="bg-[#2BCFD5] text-white px-4 py-2 rounded font-semibold">
                Get in touch
              </button>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default OwnershipInfo;
