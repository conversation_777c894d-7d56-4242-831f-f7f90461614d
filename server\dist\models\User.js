"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const crypto_1 = __importDefault(require("crypto"));
const userSchema = new mongoose_1.default.Schema({
    username: { type: String },
    email: { type: String },
    password: { type: String },
    firstName: { type: String },
    lastName: { type: String },
    image: { type: String },
    phone: { type: String },
    address: { type: String },
    zipCode: { type: String },
    country: { type: String },
    googleId: { type: String },
    externalUser: { type: Boolean, default: false },
    isEmailVerified: { type: Boolean, default: false },
    emailVerificationToken: String,
    emailVerificationExpire: Date,
    resetPasswordToken: String,
    resetPasswordExpire: Date,
    roleId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'Role',
        default: null
    },
    ownerId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'Owner',
        default: null
    },
    // New fields for category access control
    subscriptionType: {
        type: String,
        enum: ['temporary_key', 'spare_key', 'all_access_key'],
        default: 'temporary_key'
    },
    allowedCategoryId: {
        type: String,
        default: null // Only for temporary_key users
    },
    emergencyAccessRequested: {
        type: Boolean,
        default: false
    },
    emergencyAccessRequestedAt: {
        type: Date,
        default: null
    },
    emergencyAccessGranted: {
        type: Boolean,
        default: false
    },
    emergencyAccessGrantedAt: {
        type: Date,
        default: null
    },
    emergencyAccessDenied: {
        type: Boolean,
        default: false
    },
    emergencyAccessDeniedAt: {
        type: Date,
        default: null
    }
}, { timestamps: true });
userSchema.pre('save', function (next) {
    return __awaiter(this, void 0, void 0, function* () {
        // Skip password hashing for external users (Google, etc.)
        if (this.externalUser)
            return next();
        // Only hash password if it's been modified and is not empty
        if (!this.isModified('password') || !this.password)
            return next();
        try {
            if (typeof this.password !== 'string') {
                return next(new Error('Password must be a string'));
            }
            const salt = yield bcryptjs_1.default.genSalt(10);
            this.password = yield bcryptjs_1.default.hash(this.password, salt);
            next();
        }
        catch (error) {
            next(error);
        }
    });
});
userSchema.methods.comparePassword = function (candidatePassword) {
    return __awaiter(this, void 0, void 0, function* () {
        // For external users, always return false for password comparison
        if (this.externalUser)
            return false;
        // If no password is set, return false
        if (!this.password)
            return false;
        return bcryptjs_1.default.compare(candidatePassword, this.password);
    });
};
userSchema.methods.generateResetToken = function () {
    const token = crypto_1.default.randomBytes(32).toString('hex');
    const hashedToken = crypto_1.default.createHash('sha256').update(token).digest('hex');
    this.resetPasswordToken = hashedToken;
    this.resetPasswordExpire = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    return token; // return the raw token (unhashed) to be sent via email
};
userSchema.methods.generateEmailVerificationToken = function () {
    // Generate a 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const hashedOtp = crypto_1.default.createHash('sha256').update(otp).digest('hex');
    this.emailVerificationToken = hashedOtp;
    this.emailVerificationExpire = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    return otp; // return the raw OTP to be sent via email
};
const User = mongoose_1.default.model('User', userSchema);
exports.default = User;
//# sourceMappingURL=User.js.map