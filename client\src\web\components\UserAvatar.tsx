import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { User } from 'lucide-react';
import defaultAvatar from '@/assets/global/defaultAvatar/defaultImage.jpg';

interface UserAvatarProps {
  user: {
    image?: string;
    profileImage?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
  } | null;
  className?: string;
}

export function UserAvatar({ user, className }: UserAvatarProps) {
  if (!user) {
    return (
      <Avatar className={className}>
        <AvatarImage src={defaultAvatar} alt="Default avatar" />
        <AvatarFallback className="bg-[#22BBCC] text-white">
          <User className="h-6 w-6" />
        </AvatarFallback>
      </Avatar>
    );
  }

  const getInitials = () => {
    if (user.firstName && user.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
    }
    if (user.email) {
      return user.email[0].toUpperCase();
    }
    return '?';
  };

  const getImagePath = () => {
    const imagePath = user.image || user.profileImage;
    if (!imagePath || imagePath.includes('defaultImage.jpg')) {
      return defaultAvatar; // Return default avatar directly instead of undefined
    }
    // Check if the path already includes the full URL
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    // Construct the full URL for the uploaded image
    return `${import.meta.env.VITE_API_URL}/uploads/${imagePath}`;
  };

  const imageSrc = getImagePath();

  return (
    <Avatar className={className}>
      <AvatarImage
        key={imageSrc} // Force re-render when image source changes
        src={imageSrc}
        alt={`${user.firstName || 'User'}'s avatar`}
        className="object-cover rounded-full"
        onError={(e) => {
          const target = e.target as HTMLImageElement;
          target.src = defaultAvatar;
        }}
        onLoad={(e) => {
          // Ensure the image is visible when loaded
          const target = e.target as HTMLImageElement;
          target.style.opacity = '1';
        }}
      />
      <AvatarFallback className="rounded-full bg-[#22BBCC] text-white font-semibold">{getInitials()}</AvatarFallback>
    </Avatar>
  );
} 