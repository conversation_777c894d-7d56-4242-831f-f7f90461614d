import mongoose from 'mongoose';
export declare enum RequestStatus {
    PENDING = "pending",
    APPROVED = "approved",
    REJECTED = "rejected"
}
export interface IRequestedCategories extends mongoose.Document {
    _id: mongoose.Types.ObjectId;
    ownerId: mongoose.Types.ObjectId;
    requestedUserId: mongoose.Types.ObjectId;
    categoryIds: string[];
    status: RequestStatus;
    requestMessage?: string;
    approvalToken?: string;
    approvalTokenExpire?: Date;
    createdAt: Date;
    updatedAt: Date;
    owner?: any;
    requestedUser?: any;
    generateApprovalToken(): string;
}
export interface IRequestedCategoriesModel extends mongoose.Model<IRequestedCategories> {
    findByOwnerId(ownerId: mongoose.Types.ObjectId): Promise<IRequestedCategories[]>;
    findByUserId(userId: mongoose.Types.ObjectId): Promise<IRequestedCategories[]>;
    findPendingRequests(ownerId?: mongoose.Types.ObjectId): Promise<IRequestedCategories[]>;
    findByOwnerAndCategories(ownerId: mongoose.Types.ObjectId, categoryIds: string[]): Promise<IRequestedCategories[]>;
}
export interface ICreateRequestedCategoriesData {
    ownerId: mongoose.Types.ObjectId;
    requestedUserId: mongoose.Types.ObjectId;
    categoryIds: string[];
    status?: RequestStatus;
    requestMessage?: string;
    approvalToken?: string;
    approvalTokenExpire?: Date;
}
export interface IRequestCategoryData {
    categoryIds: string[];
    message?: string;
}
export interface IUpdateRequestedCategoriesData {
    status?: RequestStatus;
}
