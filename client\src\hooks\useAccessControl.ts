import { useAppSelector, useAppDispatch } from '@/store';
import {
  fetchUserAccessInfo,
  fetchEmergencyAccessStatus,
  fetchKeyHoldersForOwner,
  requestEmergencyAccess,
  grantEmergencyAccess,
  denyEmergencyAccess,
  updateUserSubscription
} from '@/store/slices/accessControlSlice';
import { SubscriptionType } from '@/types/accessControl';
import { useEffect } from 'react';
import { CATEGORIES } from '@/constants/categories';
import { useAuth } from '@/contexts/AuthContext';

// Global flags to prevent duplicate API calls across multiple hook instances
let isUserAccessFetching = false;
let isEmergencyAccessFetching = false;
let isKeyHoldersFetching = false;

export const useAccessControl = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAuth();
  const {
    userAccess,
    emergencyAccessStatus,
    keyHolders,
    loading,
    error,
    userAccessLoading,
    emergencyAccessLoading,
    keyHoldersLoading
  } = useAppSelector((state) => state.accessControl);

  // Load user access info on mount only if authenticated and not already loading/loaded
  useEffect(() => {
    if (isAuthenticated && !userAccess && !userAccessLoading && !isUserAccessFetching) {
      isUserAccessFetching = true;
      dispatch(fetchUserAccessInfo()).finally(() => {
        isUserAccessFetching = false;
      });
    }
  }, [dispatch, userAccess, isAuthenticated, userAccessLoading]);

  // Load emergency access status for spare_key users only if authenticated and not already loading/loaded
  useEffect(() => {
    if (isAuthenticated &&
        userAccess?.subscriptionType === 'spare_key' &&
        !emergencyAccessStatus &&
        !emergencyAccessLoading &&
        !isEmergencyAccessFetching) {
      isEmergencyAccessFetching = true;
      dispatch(fetchEmergencyAccessStatus()).finally(() => {
        isEmergencyAccessFetching = false;
      });
    }
  }, [dispatch, userAccess?.subscriptionType, emergencyAccessStatus, isAuthenticated, emergencyAccessLoading]);

  // Load key holders for owners only if authenticated and not already loading/loaded
  useEffect(() => {
    if (isAuthenticated &&
        userAccess?.isOwner &&
        keyHolders.length === 0 &&
        !keyHoldersLoading &&
        !isKeyHoldersFetching) {
      isKeyHoldersFetching = true;
      dispatch(fetchKeyHoldersForOwner()).finally(() => {
        isKeyHoldersFetching = false;
      });
    }
  }, [dispatch, userAccess?.isOwner, keyHolders.length, isAuthenticated, keyHoldersLoading]);

  const canAccessCategory = (categoryId: string): boolean => {
    if (!userAccess) return false;

    switch (userAccess.subscriptionType) {
      case 'temporary_key':
        // Temporary Key users can access Home Instructions and Quick Start
        return categoryId === CATEGORIES.HOME_INSTRUCTIONS || categoryId === CATEGORIES.QUICK_START;
      
      case 'spare_key':
        if (userAccess.isOwner) {
          return true; // Owner has full access
        } else {
          return userAccess.emergencyAccessGranted || false;
        }
      
      case 'all_access_key':
        // Check role-based permissions for all_access_key users
        if (userAccess.isOwner) {
          return true; // Owner has full access
        } else if (userAccess.userRole === 'Nominee') {
          return userAccess.userPermissions?.includes('canViewAll') || false;
        } else if (userAccess.userRole === 'Family') {
          return userAccess.userPermissions?.includes('canViewAll') || false;
        } else {
          // Default for all_access_key without specific role
          return true;
        }
      
      default:
        return false;
    }
  };

  const canEditCategory = (categoryId: string): boolean => {
    if (!userAccess) return false;

    switch (userAccess.subscriptionType) {
      case 'temporary_key':
        // Temporary Key users can edit Home Instructions and Quick Start
        return categoryId === CATEGORIES.HOME_INSTRUCTIONS || categoryId === CATEGORIES.QUICK_START;
      
      case 'spare_key':
        if (userAccess.isOwner) {
          return true; // Owner can edit
        } else {
          return false; // Key holders can only view
        }
      
      case 'all_access_key':
        // Check role-based permissions for all_access_key users
        if (userAccess.isOwner) {
          return true; // Owner has full access
        } else if (userAccess.userRole === 'Nominee') {
          return userAccess.userPermissions?.includes('canEditAll') || false;
        } else if (userAccess.userRole === 'Family') {
          return userAccess.userPermissions?.includes('canEditAll') || false;
        } else {
          // Default for all_access_key without specific role
          return true;
        }
      
      default:
        return false;
    }
  };

  const handleRequestEmergencyAccess = () => {
    dispatch(requestEmergencyAccess());
  };

  const handleGrantEmergencyAccess = (keyHolderId: string) => {
    dispatch(grantEmergencyAccess(keyHolderId));
  };

  const handleDenyEmergencyAccess = (keyHolderId: string) => {
    dispatch(denyEmergencyAccess(keyHolderId));
  };

  const handleUpdateSubscription = (subscriptionType: SubscriptionType, allowedCategoryId?: string) => {
    dispatch(updateUserSubscription({ subscriptionType, allowedCategoryId }));
  };

  const getAccessRestrictionReason = (categoryId: string): string | null => {
    if (!userAccess) return 'User access not loaded';

    if (canAccessCategory(categoryId)) return null;

    switch (userAccess.subscriptionType) {
      case 'temporary_key':
        return 'Temporary Key users can only access Home Instructions and Quick Start categories';
      
      case 'spare_key':
        if (userAccess.isOwner) {
          return 'Unexpected access restriction for owner';
        } else {
          if (userAccess.emergencyAccessDenied) {
            return 'Emergency access was denied by the owner';
          } else if (userAccess.emergencyAccessRequested) {
            return 'Emergency access requested, waiting for owner approval or auto-grant (24 hours)';
          } else {
            return 'Emergency access not requested. Request access to view information.';
          }
        }
      
      default:
        return 'Access denied for this subscription type';
    }
  };

  const getEditRestrictionReason = (categoryId: string): string | null => {
    if (!userAccess) return 'User access not loaded';

    if (canEditCategory(categoryId)) return null;

    switch (userAccess.subscriptionType) {
      case 'temporary_key':
        return 'Temporary Key users can only edit Home Instructions and Quick Start categories';
      
      case 'spare_key':
        if (userAccess.isOwner) {
          return 'Unexpected edit restriction for owner';
        } else {
          return 'Key holders can only view information, not edit';
        }
      
      default:
        return 'Edit access denied for this subscription type';
    }
  };

  return {
    userAccess,
    emergencyAccessStatus,
    keyHolders,
    loading,
    error,
    canAccessCategory,
    canEditCategory,
    handleRequestEmergencyAccess,
    handleGrantEmergencyAccess,
    handleDenyEmergencyAccess,
    handleUpdateSubscription,
    getAccessRestrictionReason,
    getEditRestrictionReason,
  };
}; 