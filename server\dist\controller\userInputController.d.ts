import { Request, Response, RequestHandler } from 'express';
export declare const createUserInput: (req: Request, res: Response) => Promise<void>;
export declare const getUserInput: (req: Request, res: Response) => Promise<void>;
export declare const updateUserInput: (req: Request, res: Response) => Promise<void>;
export declare const getUserInputByUserId: (req: Request, res: Response) => Promise<void>;
/**
 * Get dashboard stats for a user
 * This endpoint returns the count of answered questions for each category
 * without fetching all the actual question data
 */
export declare const getUserInputByOwnerId: (req: Request, res: Response) => Promise<void>;
/**
 * Get dashboard stats by owner ID
 * This endpoint returns the count of answered questions for each category for an owner
 * Replaces the old user_id based dashboard stats
 */
export declare const getDashboardStats: RequestHandler;
