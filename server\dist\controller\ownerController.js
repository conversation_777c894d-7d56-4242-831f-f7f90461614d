"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOwnerByEmail = exports.deleteOwner = exports.updateMyOwnerProfile = exports.updateOwner = exports.getMyOwnerProfile = exports.getOwnerByUserId = exports.getOwnerById = exports.getAllOwners = void 0;
const Owner_1 = __importDefault(require("../models/Owner"));
const User_1 = __importDefault(require("../models/User"));
const mongoose_1 = __importDefault(require("mongoose"));
// Get all owners
const getAllOwners = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const owners = yield Owner_1.default.find()
            .populate('user', 'username email roleId')
            .sort({ createdAt: -1 });
        res.status(200).json({
            status: 'success',
            results: owners.length,
            data: { owners }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error fetching owners',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getAllOwners = getAllOwners;
// Get owner by ID
const getOwnerById = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const owner = yield Owner_1.default.findById(id).populate('user', 'username email roleId');
        if (!owner) {
            res.status(404).json({
                status: 'fail',
                message: 'Owner not found'
            });
            return;
        }
        res.status(200).json({
            status: 'success',
            data: { owner }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error fetching owner',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getOwnerById = getOwnerById;
// Get owner by user ID
const getOwnerByUserId = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId } = req.params;
        const owner = yield Owner_1.default.findByUserId(new mongoose_1.default.Types.ObjectId(userId));
        if (!owner) {
            res.status(404).json({
                status: 'fail',
                message: 'Owner not found for this user'
            });
            return;
        }
        res.status(200).json({
            status: 'success',
            data: { owner }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error fetching owner by user ID',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getOwnerByUserId = getOwnerByUserId;
// Get current user's owner profile
const getMyOwnerProfile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        if (!userId) {
            res.status(401).json({
                status: 'fail',
                message: 'User not authenticated'
            });
            return;
        }
        const owner = yield Owner_1.default.findByUserId(userId);
        if (!owner) {
            res.status(404).json({
                status: 'fail',
                message: 'Owner profile not found'
            });
            return;
        }
        res.status(200).json({
            status: 'success',
            data: { owner }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error fetching owner profile',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getMyOwnerProfile = getMyOwnerProfile;
// Update owner
const updateOwner = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const owner = yield Owner_1.default.findById(id);
        if (!owner) {
            res.status(404).json({
                status: 'fail',
                message: 'Owner not found'
            });
            return;
        }
        // Update only provided fields
        Object.keys(updateData).forEach(key => {
            if (updateData[key] !== undefined) {
                owner[key] = updateData[key];
            }
        });
        yield owner.save();
        res.status(200).json({
            status: 'success',
            message: 'Owner updated successfully',
            data: { owner }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error updating owner',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.updateOwner = updateOwner;
// Update current user's owner profile
const updateMyOwnerProfile = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a._id;
        const updateData = req.body;
        if (!userId) {
            res.status(401).json({
                status: 'fail',
                message: 'User not authenticated'
            });
            return;
        }
        const owner = yield Owner_1.default.findOne({ userId });
        if (!owner) {
            res.status(404).json({
                status: 'fail',
                message: 'Owner profile not found'
            });
            return;
        }
        // Update only provided fields
        Object.keys(updateData).forEach(key => {
            if (updateData[key] !== undefined) {
                owner[key] = updateData[key];
            }
        });
        yield owner.save();
        res.status(200).json({
            status: 'success',
            message: 'Owner profile updated successfully',
            data: { owner }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error updating owner profile',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.updateMyOwnerProfile = updateMyOwnerProfile;
// Delete owner (soft delete by removing the relationship)
const deleteOwner = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const owner = yield Owner_1.default.findById(id);
        if (!owner) {
            res.status(404).json({
                status: 'fail',
                message: 'Owner not found'
            });
            return;
        }
        // Remove owner reference from user
        yield User_1.default.findByIdAndUpdate(owner.userId, { $unset: { ownerId: 1 } });
        // Delete the owner record
        yield Owner_1.default.findByIdAndDelete(id);
        res.status(200).json({
            status: 'success',
            message: 'Owner deleted successfully'
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error deleting owner',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.deleteOwner = deleteOwner;
// Get owner by email
const getOwnerByEmail = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { email } = req.params;
        const owner = yield Owner_1.default.findByEmail(email);
        if (!owner) {
            res.status(404).json({
                status: 'fail',
                message: 'Owner not found with this email'
            });
            return;
        }
        res.status(200).json({
            status: 'success',
            data: { owner }
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            message: 'Error fetching owner by email',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getOwnerByEmail = getOwnerByEmail;
//# sourceMappingURL=ownerController.js.map