import mongoose from 'mongoose';

export enum RequestStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface IRequestedCategories extends mongoose.Document {
  _id: mongoose.Types.ObjectId;
  ownerId: mongoose.Types.ObjectId; // Reference to Owner model
  requestedUserId: mongoose.Types.ObjectId; // Reference to User model (nominee/family)
  categoryIds: string[]; // Array of numeric category IDs (e.g., ["1", "2", "3"])
  status: RequestStatus;
  requestMessage?: string; // Optional message from requester
  approvalToken?: string; // Token for email approval
  approvalTokenExpire?: Date; // Token expiry
  createdAt: Date;
  updatedAt: Date;

  // Virtual fields (populated)
  owner?: any; // Populated Owner data
  requestedUser?: any; // Populated User data

  // Instance methods
  generateApprovalToken(): string;
}

export interface IRequestedCategoriesModel extends mongoose.Model<IRequestedCategories> {
  // Static methods
  findByOwnerId(ownerId: mongoose.Types.ObjectId): Promise<IRequestedCategories[]>;
  findByUserId(userId: mongoose.Types.ObjectId): Promise<IRequestedCategories[]>;
  findPendingRequests(ownerId?: mongoose.Types.ObjectId): Promise<IRequestedCategories[]>;
  findByOwnerAndCategories(ownerId: mongoose.Types.ObjectId, categoryIds: string[]): Promise<IRequestedCategories[]>;
}

export interface ICreateRequestedCategoriesData {
  ownerId: mongoose.Types.ObjectId;
  requestedUserId: mongoose.Types.ObjectId;
  categoryIds: string[]; // Array of numeric category IDs as strings
  status?: RequestStatus;
  requestMessage?: string;
  approvalToken?: string;
  approvalTokenExpire?: Date;
}

export interface IRequestCategoryData {
  categoryIds: string[]; // Array of numeric category IDs (e.g., ["1", "2", "3"])
  message?: string; // Optional message from requester
}

export interface IUpdateRequestedCategoriesData {
  status?: RequestStatus;
}
