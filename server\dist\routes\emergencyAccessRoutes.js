"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const emergencyAccessController_1 = require("../controller/emergencyAccessController");
const authMiddleware_1 = require("../middleware/authMiddleware");
const accessControlMiddleware_1 = require("../middleware/accessControlMiddleware");
const router = express_1.default.Router();
// Apply authentication and access control middleware to all routes
router.use(authMiddleware_1.combinedAuth);
router.use(accessControlMiddleware_1.accessControlMiddleware);
// Request emergency access (for spare_key key holders)
router.post('/request', emergencyAccessController_1.requestEmergencyAccess);
// Grant emergency access (for owners)
router.post('/grant', emergencyAccessController_1.grantEmergencyAccess);
// Deny emergency access (for owners)
router.post('/deny', emergencyAccessController_1.denyEmergencyAccess);
// Get emergency access status
router.get('/status', emergencyAccessController_1.getEmergencyAccessStatus);
// Get all key holders for an owner
router.get('/key-holders', emergencyAccessController_1.getKeyHoldersForOwner);
exports.default = router;
//# sourceMappingURL=emergencyAccessRoutes.js.map