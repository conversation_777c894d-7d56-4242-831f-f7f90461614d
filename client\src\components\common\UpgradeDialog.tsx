import { Button } from '@/components/ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';

interface UpgradeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpgradeClick: () => void;
  subscriptionType?: string;
  title?: string;
  description?: string;
}

export const UpgradeDialog: React.FC<UpgradeDialogProps> = ({
  open,
  onOpenChange,
  onUpgradeClick,
  subscriptionType = 'temporary_key',
  title = 'Upgrade Required',
  description = 'This feature requires a higher subscription level.'
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>
            {description} Your current plan ({subscriptionType}) doesn't include access to this category.
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-4">
          <p className="text-sm text-gray-600">
            Upgrade to All Access Key to unlock all categories and features.
          </p>
          <div className="flex gap-2">
            <Button 
              onClick={onUpgradeClick}
              className="flex-1 bg-[#2BCFD5] hover:bg-[#19bbb5] text-white"
            >
              Upgrade Now
            </Button>
            <Button 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 