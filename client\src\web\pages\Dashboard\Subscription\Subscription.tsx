import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Avatar } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { CheckCircle2 } from 'lucide-react';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { useAuth } from '@/contexts/AuthContext';
import { useParams } from 'react-router-dom';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchUserInputs,
  selectSubcategories,
  selectProgressStats,
  selectUserInputs,
  selectLoading,
  selectError,
  Subcategory,
  UserInput
} from '@/store/slices/subscriptionCategorySlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';

const sectionTitles = {
  '1101A': 'Netflix',
  '1102A': 'Apple TV',
  '1103A': 'Amazon Prime',
  '1104A': 'Other Services'
};

const SubCategoryCard = ({ subcategory }: { subcategory: Subcategory }) => {
  // Get the user inputs from Redux state
  const userInputs = useAppSelector(selectUserInputs);

  // Calculate completed questions for this subcategory
  const completedQuestions = userInputs.reduce((count: number, input: UserInput) => {
    if (input.originalSubCategoryId === subcategory.id) {
      // Special handling for Other Services (1104A)
      if (subcategory.id === '1104A') {
        return input.answersBySection.reduce((sectionCount: number, section) => {
          // Find the dropdown answer (s10) which contains the services JSON
          const dropdownAnswer = section.answers.find(a => a.originalQuestionId === 's10');
          if (dropdownAnswer?.answer) {
            try {
              const servicesData = JSON.parse(dropdownAnswer.answer);
              if (Array.isArray(servicesData)) {
                // Count only services that have been saved with actual data
                const savedServicesCount = servicesData.filter((service: any) => {
                  // Service must have a name
                  if (!service.serviceName || service.serviceName.trim() === '') {
                    return false;
                  }
                  
                  // Service must have at least one account with username or password
                  if (!service.accounts || !Array.isArray(service.accounts)) {
                    return false;
                  }
                  
                  // Check if any account has username or password filled
                  const hasCredentials = service.accounts.some((account: any) => 
                    (account.username && account.username.trim() !== '') || 
                    (account.password && account.password.trim() !== '')
                  );
                  
                  return hasCredentials;
                }).length;
                
                return sectionCount + savedServicesCount;
              }
            } catch (e) {
              console.error('Error parsing services data:', e);
            }
          }
          return sectionCount + section.answers.length;
        }, 0);
      } else {
        // Default logic for other subcategories
        return count + input.answersBySection.reduce(
          (sectionCount: number, section) => sectionCount + section.answers.length, 0
        );
      }
    }
    return count;
  }, 0);

  // For Other Services, calculate total dynamically based on services added
  let totalQuestions = subcategory.questionsCount;
  if (subcategory.id === '1104A') {
    // Get the actual number of services the user has added
    const userInput = userInputs.find(input => input.originalSubCategoryId === '1104A');
    if (userInput) {
      const section = userInput.answersBySection.find(s => s.originalSectionId === '1104A');
      if (section) {
        const dropdownAnswer = section.answers.find(a => a.originalQuestionId === 's10');
        if (dropdownAnswer?.answer) {
          try {
            const servicesData = JSON.parse(dropdownAnswer.answer);
            if (Array.isArray(servicesData)) {
              const totalServices = servicesData.filter((service: any) => 
                service.serviceName && service.serviceName.trim() !== ''
              ).length;
              // Set total to the number of services added, with a minimum of 1
              totalQuestions = Math.max(totalServices, 1);
            }
          } catch (e) {
            console.error('Error parsing services data for total:', e);
            totalQuestions = 1; // Fallback to 1
          }
        }
      }
    }
    // If no user input yet, set a reasonable default
    if (totalQuestions === subcategory.questionsCount) {
      totalQuestions = 1;
    }
  }

  const completionPercentage = totalQuestions > 0
    ? Math.round((completedQuestions / totalQuestions) * 100)
    : 0;

  return (
    <div className="border rounded-lg overflow-hidden transition-shadow hover:shadow-md">
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium text-[#1F4168]">{subcategory.title}</h3>
          <span className="text-sm text-blue-500">
            {completedQuestions}/{totalQuestions} questions
          </span>
        </div>
        <Progress value={completionPercentage} className="h-1.5 mb-2" />
      </div>
    </div>
  );
};

const Subscription = ({ category }: { category?: string }) => {
  const { user } = useAuth();
  const params = useParams();
  const categoryName = category || params.categoryName;
  const dispatch = useAppDispatch();

  // Get data from Redux store
  const subcategories = useAppSelector(selectSubcategories);
  const userInputs = useAppSelector(selectUserInputs);
  const progressStats = useAppSelector(selectProgressStats);
  const loading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);

  // Calculate dynamic overall progress
  const calculateOverallProgress = () => {
    let totalAnswered = 0;
    let totalQuestions = 0;

    subcategories.forEach((subcategory) => {
      if (subcategory.id === '1104A') {
        // Special handling for Other Services
        const userInput = userInputs.find(input => input.originalSubCategoryId === '1104A');
        if (userInput) {
          const section = userInput.answersBySection.find(s => s.originalSectionId === '1104A');
          if (section) {
            const dropdownAnswer = section.answers.find(a => a.originalQuestionId === 's10');
            if (dropdownAnswer?.answer) {
              try {
                const servicesData = JSON.parse(dropdownAnswer.answer);
                if (Array.isArray(servicesData)) {
                  // Count total services added
                  const totalServices = servicesData.filter((service: any) => 
                    service.serviceName && service.serviceName.trim() !== ''
                  ).length;
                  
                  // Count saved services (with credentials)
                  const savedServices = servicesData.filter((service: any) => {
                    if (!service.serviceName || service.serviceName.trim() === '') {
                      return false;
                    }
                    if (!service.accounts || !Array.isArray(service.accounts)) {
                      return false;
                    }
                    return service.accounts.some((account: any) => 
                      (account.username && account.username.trim() !== '') || 
                      (account.password && account.password.trim() !== '')
                    );
                  }).length;
                  
                  totalAnswered += savedServices;
                  totalQuestions += Math.max(totalServices, 1);
                }
              } catch (e) {
                console.error('Error parsing services data for overall progress:', e);
              }
            }
          }
        } else {
          totalQuestions += 1; // Default minimum
        }
      } else {
        // For other subcategories, use the standard calculation
        const subcategoryAnswered = userInputs.reduce((count: number, input: UserInput) => {
          if (input.originalSubCategoryId === subcategory.id) {
            return count + input.answersBySection.reduce(
              (sectionCount: number, section) => sectionCount + section.answers.length, 0
            );
          }
          return count;
        }, 0);
        
        totalAnswered += subcategoryAnswered;
        totalQuestions += subcategory.questionsCount;
      }
    });

    return {
      answeredQuestions: totalAnswered,
      totalQuestions: totalQuestions,
      completionPercentage: totalQuestions > 0 ? Math.round((totalAnswered / totalQuestions) * 100) : 0
    };
  };

  const dynamicProgressStats = calculateOverallProgress();

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            dispatch(fetchUserInputs(user.id));
          }
        } catch (error) {
          console.error('Error fetching owner ID, falling back to user ID:', error);
          dispatch(fetchUserInputs(user.id));
        }
      }
    };
    fetchData();
  }, [dispatch, user]);

  // Fallback user info if not authenticated
  const userInfo = createUserInfo(user);

  const tabs = categoryTabsConfig[categoryName as keyof typeof categoryTabsConfig] || categoryTabsConfig['subscription'];

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      {/* Header with gradient background */}
      <div className="bg-gradient-to-r from-[#1F4168] to-[#2BCFD5] text-white py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-1">Subscription</h1>
              <Link to="/dashboard" className="flex items-center text-sm hover:underline text-[#2BCFD5] font-semibold text-md mt-1 mb-1">
                <span className="mr-1">←</span> Back to Home
              </Link>
            </div>
            <div className="flex items-center">
              <div className="text-right mr-4">
                <div className="font-semibold">{userInfo.name}</div>
                <div className="text-sm opacity-80">{userInfo.email}</div>
              </div>
              <Avatar className="rounded-full w-14 h-14 bg-white overflow-hidden">
                <img
                  src={userInfo.avatar}
                  alt={userInfo.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = avatar; // Fallback to default avatar
                  }}
                />
              </Avatar>
            </div>
          </div>
        </div>
      </div>
      {/* Main content */}
      <SubCategoryTabs tabs={tabs} />
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Categories */}
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              {/* Overall progress bar */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium text-gray-700">Overall progress</h3>
                  <span className="text-sm text-gray-500">
                    {dynamicProgressStats.answeredQuestions}/{dynamicProgressStats.totalQuestions} questions completed
                  </span>
                </div>
                <Progress
                  value={dynamicProgressStats.completionPercentage}
                  className="h-2"
                />
                {dynamicProgressStats.completionPercentage === 100 && (
                  <div className="mt-2 text-center">
                    <span className="inline-flex items-center text-sm text-green-600 font-medium">
                      <CheckCircle2 className="h-4 w-4 mr-1" /> All questions completed!
                    </span>
                  </div>
                )}
              </div>
              <h2 className="text-xl font-semibold text-[#1F4168] mb-2">Good to Know: <span className="text-purple-600">How to Understand Topics</span></h2>
              <p className="text-gray-600 mb-6">
                Each topic below is a part of your subscription services, with questions to help you provide important
                information for you and your loved ones. Click any topic to answer the questions at your own pace—
                we'll save everything for you.
              </p>
              {/* Loading state */}
              {loading && (
                <div className="text-center py-4">
                  <p className="text-gray-500">Loading your subscription information...</p>
                </div>
              )}

              {/* Error state */}
              {error && (
                <div className="text-center py-4">
                  <p className="text-red-500">{error}</p>
                </div>
              )}

              {/* Subcategory cards */}
              {!loading && !error && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-6">
                  {subcategories.map((subcategory: Subcategory) => (
                    <Link
                      key={subcategory.id}
                      to={`/category/${categoryName}/${sectionTitles[subcategory.id as keyof typeof sectionTitles]?.toLowerCase().replace(/\s/g, '')}`}
                      className="block"
                    >
                      <SubCategoryCard subcategory={subcategory} />
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
          {/* Right column - Search panel */}
          <div>
            <SearchPanel />
            {/* Still have questions box */}
            <div className="bg-[#223a5f] text-white rounded-lg p-4 mt-6">
              <div className="mb-2 font-semibold">Still have questions?</div>
              <div className="mb-4 text-sm opacity-80">
                Can't find the answer you're looking for? Please chat to our friendly team.
              </div>
              <button className="bg-[#2BCFD5] text-white px-4 py-2 rounded font-semibold">
                Get in touch
              </button>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Subscription;