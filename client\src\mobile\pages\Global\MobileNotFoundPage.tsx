import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';

const MobileNotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Auto-redirect to dashboard after 5 seconds
    const timer = setTimeout(() => {
      navigate('/dashboard');
    }, 5000);

    return () => clearTimeout(timer);
  }, [navigate]);

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <GradiantHeader title="Page Not Found" showAvatar={false} />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center text-center space-y-6">
          {/* Error Icon */}
          <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-12 h-12 text-red-500" />
          </div>

          {/* Error Message */}
          <div className="space-y-2">
            <h1 className="text-2xl font-bold text-gray-900">
              Oops! Page Not Found
            </h1>
            <p className="text-gray-600 max-w-md">
              The page you're looking for doesn't exist or has been moved. 
              You'll be redirected to your dashboard shortly.
            </p>
          </div>

          {/* Countdown */}
          <div className="text-sm text-gray-500">
            Redirecting to dashboard in 5 seconds...
          </div>

          {/* Action Button */}
          <Button 
            onClick={handleGoToDashboard}
            className="bg-[#1F4168] hover:bg-[#1F4168]/90 text-white px-8 py-3 rounded-lg flex items-center gap-2"
          >
            <Home className="w-4 h-4" />
            Go to Dashboard Now
          </Button>

          {/* Additional Help */}
          <div className="text-xs text-gray-400 max-w-sm">
            If you continue to experience issues, please contact our support team.
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileNotFoundPage;
