import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/contexts/AuthContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchUserInputs,
  selectError,
  selectLoading,
  selectProgressStats,
  selectSubcategories,
  selectUserInputs,
  Subcategory,
  UserInput
} from '@/store/slices/bankingInformationSlice';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { Avatar } from '@/components/ui/avatar';
import { useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { createUserInfo } from '@/utils/avatarUtils';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';



const SubCategoryCard = ({ subcategory }: { subcategory: Subcategory }) => {
  // Get the user inputs from Redux state
  const userInputs = useAppSelector(selectUserInputs);

  // Calculate completed questions for this subcategory
  const completedQuestions = userInputs.reduce((count: number, input: UserInput) => {
    // Match the first 3 characters of originalSubCategoryId to subcategory.id
    if (input.originalSubCategoryId?.startsWith(subcategory.id)) {
      return count + input.answersBySection.reduce(
        (sectionCount: number, section: any) => sectionCount + section.answers.length, 0
      );
    }
    return count;
  }, 0);

  const completionPercentage = subcategory.questionsCount > 0
    ? Math.round((completedQuestions / subcategory.questionsCount) * 100)
    : 0;

  return (
    <div className="border rounded-lg overflow-hidden transition-shadow hover:shadow-md">
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium text-[#1F4168]">{subcategory.title}</h3>
          <span className="text-sm text-blue-500">
            {completedQuestions}/{subcategory.questionsCount} questions
          </span>
        </div>
        <Progress
          value={completionPercentage}
          className="h-1.5 mb-2"
        />
      </div>
    </div>
  );
};

const BankingInformation = () => {
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  // Get data from Redux store
  const subcategories = useAppSelector(selectSubcategories);
  const progressStats = useAppSelector(selectProgressStats);
  const loading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);

  const tabs = categoryTabsConfig['bankinginformation'] || [];

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: createUserInfo(user).avatar
  };

  // Load data when component mounts
  useEffect(() => {
    const loadData = async () => {
      if (user) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            await dispatch(fetchUserInputs(ownerId));
          } else {
            console.warn('No owner ID found for user');
          }
        } catch (error) {
          console.error('Error loading banking information data:', error);
        }
      }
    };
    loadData();
  }, [dispatch, user]);

  // Show loading state while fetching data
  if (loading) {
    return (
      <div className="flex flex-col pt-20 min-h-screen">
        <AppHeader />
        <div className="bg-gradient-to-r from-[#1F4168] to-[#2BCFD5] text-white py-4">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold mb-1">Banking Information</h1>
                <Link to="/dashboard" className="flex items-center text-sm hover:underline text-[#2BCFD5] font-semibold text-md mt-1 mb-1">
                  <span className="mr-1">←</span> Back to Home
                </Link>
              </div>
            </div>
          </div>
        </div>
        <SubCategoryTabs tabs={tabs} />
        <div className="flex-1 container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2BCFD5] mx-auto mb-4"></div>
              <p className="text-gray-600">Loading banking information...</p>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      {/* Header with gradient background */}
      <div className="bg-gradient-to-r from-[#1F4168] to-[#2BCFD5] text-white py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-1">Banking Information</h1>
              <Link to="/dashboard" className="flex items-center text-sm hover:underline text-[#2BCFD5] font-semibold text-md mt-1 mb-1">
                <span className="mr-1">←</span> Back to Home
              </Link>
            </div>
            <div className="flex items-center">
              <div className="text-right mr-4">
                <div className="font-semibold">{userInfo.name}</div>
                <div className="text-sm opacity-80">{userInfo.email}</div>
              </div>
              <Avatar className="rounded-full w-14 h-14 bg-white overflow-hidden">
                <img
                  src={userInfo.avatar}
                  alt={userInfo.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = avatar; // Fallback to default avatar
                  }}
                />
              </Avatar>
            </div>
          </div>
        </div>
      </div>
      {/* Main content */}
      <SubCategoryTabs tabs={tabs} />
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Categories */}
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              {/* Overall progress bar */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium text-gray-700">Overall progress</h3>
                  <span className="text-sm text-gray-500">
                    {progressStats.answeredQuestions}/{progressStats.totalQuestions} questions completed
                  </span>
                </div>
                <Progress
                  value={progressStats.completionPercentage}
                  className="h-2"
                />
                {progressStats.completionPercentage === 100 && (
                  <div className="mt-2 text-center">
                    <span className="text-sm text-green-600 font-medium">All questions completed! 🎉</span>
                  </div>
                )}
              </div>
              <h2 className="text-xl font-semibold text-[#1F4168] mb-2">Good to Know: <span className="text-purple-600">How to Understand Topics</span></h2>
              <p className="text-gray-600 mb-6">
                Each topic below is a part of your banking information, with questions to help you provide important
                information for you and your loved ones. Click any topic to answer the questions at your own pace—
                we'll save everything for you.
              </p>
              {/* Subcategory cards */}
              <div className="grid grid-cols-1 gap-4 mt-6">
                {!loading && !error && subcategories.map(subcategory => {
                  // Find the correct tab config by label
                  const tabConfig = categoryTabsConfig.bankinginformation?.find(tab => tab.label.toLowerCase() === subcategory.title.toLowerCase());
                  const path = tabConfig ? tabConfig.path : `/category/bankinginformation/${subcategory.title.toLowerCase().replace(/\s/g, '')}`;
                  return (
                    <Link
                      key={subcategory.id}
                      to={path}
                      className="block"
                    >
                      <SubCategoryCard subcategory={subcategory} />
                    </Link>
                  );
                })}
              </div>

              {/* Error state */}
              {error && (
                <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600">{error}</p>
                </div>
              )}

              {/* Loading state */}
              {loading && (
                <div className="mt-6 text-center">
                  <p className="text-gray-600">Loading banking information...</p>
                </div>
              )}
            </div>
          </div>

          {/* Right column - Search panel */}
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default BankingInformation;
