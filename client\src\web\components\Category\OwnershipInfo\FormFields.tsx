import { useField } from 'formik';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import * as Yup from 'yup';

export interface BaseQuestion {
  id: string;
  text: string;
  type: string;
  required: boolean;
  sectionId: string;
  order: number;
  isAnswered?: boolean;
  answer?: any;
  dependsOn?: {
    questionId: string;
    value: string;
  };
}

export interface TextQuestion extends BaseQuestion {
  type: 'text';
  validationRules?: {
    minLength?: number;
    maxLength?: number;
  };
  placeholder?: string;
}

export interface NumberQuestion extends BaseQuestion {
  type: 'number';
  placeholder?: string;
}

export interface BooleanQuestion extends BaseQuestion {
  type: 'boolean';
}

export interface ChoiceQuestion extends BaseQuestion {
  type: 'choice' | 'dropdown';
  options: string[];
}

export interface TextareaQuestion extends BaseQuestion {
  type: 'textarea';
  placeholder?: string;
}

export interface CurrencyQuestion extends BaseQuestion {
  type: 'currency';
  placeholder?: string;
}

export interface PasswordQuestion extends BaseQuestion {
  type: 'password';
  placeholder?: string;
}

export type Question = TextQuestion | NumberQuestion | BooleanQuestion | ChoiceQuestion | TextareaQuestion | CurrencyQuestion | PasswordQuestion;

// Text Field Component
export const TextField = ({ question }: { question: TextQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Input
        id={question.id}
        type="text"
        placeholder={question.placeholder}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

// Number Field Component
export const NumberField = ({ question }: { question: NumberQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Input
        id={question.id}
        type="number"
        placeholder={question.placeholder}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

// Currency Field Component
export const CurrencyField = ({ question }: { question: CurrencyQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <div className="relative">
        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
        <Input
          id={question.id}
          type="text"
          placeholder={question.placeholder || "0.00"}
          {...field}
          className={`w-full pl-8 ${meta.touched && meta.error ? 'border-red-500' : ''}`}
        />
      </div>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

// Password Field Component
export const PasswordField = ({ question }: { question: PasswordQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Input
        id={question.id}
        type="password"
        placeholder={question.placeholder}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

// Boolean Field Component
export const BooleanField = ({ question }: { question: BooleanQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  return (
    <div className="mb-6">
      <div className="flex flex-col">
        <Label className="font-medium mb-2" htmlFor={question.id}>
          {question.text}
        </Label>
        <div className="flex gap-2">
          <Button
            type="button"
            onClick={() => helpers.setValue('yes')}
            className={field.value === 'yes'
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5]'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          >
            Yes
          </Button>
          <Button
            type="button"
            onClick={() => helpers.setValue('no')}
            className={field.value === 'no'
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5]'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          >
            No
          </Button>
        </div>
        {meta.touched && meta.error ? (
          <div className="text-red-500 text-sm mt-1">{meta.error}</div>
        ) : null}
      </div>
    </div>
  );
};

// Choice Field Component (for dropdown/select)
export const ChoiceField = ({ question }: { question: ChoiceQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Select
        value={field.value || ''}
        onValueChange={(value) => helpers.setValue(value)}
      >
        <SelectTrigger className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}>
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          {question.options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

// Textarea Field Component
export const TextareaField = ({ question }: { question: TextareaQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Textarea
        id={question.id}
        placeholder={question.placeholder}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
        rows={4}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

// Function to check if a question should be shown based on dependencies
export const shouldShowQuestion = (question: Question, formValues: Record<string, any>): boolean => {
  if (!question.dependsOn) return true;
  const { questionId, value } = question.dependsOn;
  return formValues[questionId] === value;
};

// Build validation schema
export const buildValidationSchema = (questions: Question[], Yup: any) => {
  const schemaShape: Record<string, any> = {};
  
  questions.forEach(question => {
    let fieldSchema;

    switch (question.type) {
      case 'text':
      case 'password':
        fieldSchema = Yup.string();
        if ((question as TextQuestion).validationRules?.minLength) {
          fieldSchema = fieldSchema.min(
            (question as TextQuestion).validationRules!.minLength!,
            `Must be at least ${(question as TextQuestion).validationRules!.minLength!} characters`
          );
        }
        if ((question as TextQuestion).validationRules?.maxLength) {
          fieldSchema = fieldSchema.max(
            (question as TextQuestion).validationRules!.maxLength!,
            `Must be at most ${(question as TextQuestion).validationRules!.maxLength!} characters`
          );
        }
        break;

      case 'number':
        fieldSchema = Yup.number();
        break;

      case 'currency':
        fieldSchema = Yup.string();
        break;

      case 'boolean':
        fieldSchema = Yup.string().oneOf(['yes', 'no']);
        break;

      case 'choice':
      case 'dropdown':
        fieldSchema = Yup.string();
        break;

      case 'textarea':
        fieldSchema = Yup.string();
        break;

      default:
        fieldSchema = Yup.string();
    }

    if (question.required) {
      fieldSchema = fieldSchema.required(`${question.text} is required`);
    }

    schemaShape[question.id] = fieldSchema;
  });
  
  return Yup.object().shape(schemaShape);
};

// Generate initial values
export const generateInitialValues = (questions: Question[]) => {
  const initialValues: Record<string, any> = {};
  questions.forEach((question) => {
    initialValues[question.id] = question.answer || '';
  });
  return initialValues;
};

// Handle dependent answers - clear values when dependencies change
export const handleDependentAnswers = (
  values: Record<string, any>,
  questions: Question[],
  setValues: (values: Record<string, any>) => void
) => {
  const newValues = { ...values };

  questions.forEach((question) => {
    if (question.dependsOn) {
      const { questionId, value } = question.dependsOn;
      if (values[questionId] !== value) {
        newValues[question.id] = '';
      }
    }
  });

  setValues(newValues);
};

// Clean up dependent answers before saving - returns cleaned values
export const cleanDependentAnswers = (
  values: Record<string, any>,
  questions: Question[]
): Record<string, any> => {
  const cleanedValues = { ...values };

  questions.forEach((question) => {
    if (question.dependsOn) {
      const { questionId, value } = question.dependsOn;
      if (values[questionId] !== value) {
        cleanedValues[question.id] = '';
      }
    }
  });

  return cleanedValues;
};

// Render question based on type
const renderQuestion = (question: Question) => {
  switch (question.type) {
    case 'text':
      return <TextField question={question as TextQuestion} />;
    case 'number':
      return <NumberField question={question as NumberQuestion} />;
    case 'currency':
      return <CurrencyField question={question as CurrencyQuestion} />;
    case 'password':
      return <PasswordField question={question as PasswordQuestion} />;
    case 'textarea':
      return <TextareaField question={question} />;
    case 'boolean':
      return <BooleanField question={question as BooleanQuestion} />;
    case 'choice':
    case 'dropdown':
      return <ChoiceField question={question as ChoiceQuestion} />;
    default:
      return null;
  }
};

// Question Item Component
export const QuestionItem = ({
  question,
  formValues
}: {
  question: Question;
  formValues: Record<string, any>;
}) => {
  const shouldShow = shouldShowQuestion(question, formValues);

  if (!shouldShow) {
    return null;
  }

  return (
    <div id={`question-${question.id}`} className="scroll-mt-4">
      {renderQuestion(question)}
    </div>
  );
};
