import React from 'react';
import { Field, ErrorMessage } from 'formik';

export interface Question {
  id: string;
  text: string;
  type: 'text' | 'choice' | 'boolean' | 'dropdown';
  required: boolean;
  sectionId: string;
  options?: string[];
  order: number;
  dependsOn?: {
    questionId: string;
    value: string;
  };
  placeholder?: string;
  validationRules?: {
    minLength?: number;
    maxLength?: number;
  };
}

export const buildValidationSchema = (questions: Question[], Yup: any) => {
  const schema: Record<string, any> = {};

  questions.forEach(question => {
    let fieldSchema;

    switch (question.type) {
      case 'text':
        fieldSchema = Yup.string();
        break;
      case 'choice':
      case 'dropdown':
        fieldSchema = Yup.string();
        break;
      case 'boolean':
        fieldSchema = Yup.string();
        break;
      default:
        fieldSchema = Yup.string();
    }

    // All fields are optional
    schema[question.id] = fieldSchema;
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: Question[]) => {
  const values: Record<string, any> = {};
  questions.forEach(question => {
    values[question.id] = '';
  });
  return values;
};

export const handleDependentAnswers = (
  values: Record<string, any>,
  questions: Question[],
  setValues: (values: Record<string, any>) => void
) => {
  const updatedValues = { ...values };
  let hasChanges = false;

  questions.forEach(question => {
    if (question.dependsOn) {
      const dependentValue = values[question.dependsOn.questionId];
      const shouldShow = dependentValue === question.dependsOn.value;
      
      if (!shouldShow && values[question.id]) {
        updatedValues[question.id] = '';
        hasChanges = true;
      }
    }
  });

  if (hasChanges) {
    setValues(updatedValues);
  }
};

interface QuestionItemProps {
  question: Question;
  values: Record<string, any>;
}

export const QuestionItem: React.FC<QuestionItemProps> = ({ question, values }) => {
  // Check if this question should be shown based on dependencies
  const shouldShow = !question.dependsOn || 
    values[question.dependsOn.questionId] === question.dependsOn.value;

  if (!shouldShow) {
    return null;
  }

  const renderField = () => {
    switch (question.type) {
      case 'text':
        return (
          <Field
            name={question.id}
            type="text"
            placeholder={question.placeholder || "Enter your answer"}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
          />
        );

      case 'choice':
      case 'dropdown':
        return (
          <Field
            as="select"
            name={question.id}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
          >
            <option value="">Select an option</option>
            {question.options?.map((option, index) => (
              <option key={index} value={option}>
                {option}
              </option>
            ))}
          </Field>
        );

      case 'boolean':
        return (
          <div className="space-y-2">
            <label className="flex items-center">
              <Field
                type="radio"
                name={question.id}
                value="yes"
                className="mr-2 text-[#2BCFD5] focus:ring-[#2BCFD5]"
              />
              Yes
            </label>
            <label className="flex items-center">
              <Field
                type="radio"
                name={question.id}
                value="no"
                className="mr-2 text-[#2BCFD5] focus:ring-[#2BCFD5]"
              />
              No
            </label>
          </div>
        );

      default:
        return (
          <Field
            name={question.id}
            type="text"
            placeholder={question.placeholder || "Enter your answer"}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
          />
        );
    }
  };

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {renderField()}
      <ErrorMessage
        name={question.id}
        component="div"
        className="text-red-500 text-sm mt-1"
      />
    </div>
  );
};
