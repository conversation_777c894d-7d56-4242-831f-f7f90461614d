import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

const subscribedPlanSchema = Joi.object({
  planId: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .optional()
    .messages({
      'string.pattern.base': 'Plan ID must be a valid MongoDB ObjectId'
    }),
  ownerId: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .required()
    .messages({
      'string.pattern.base': 'Owner ID must be a valid MongoDB ObjectId',
      'any.required': 'Owner ID is required'
    }),
  previousPlans: Joi.array()
    .items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/))
    .optional()
    .default([])
    .messages({
      'string.pattern.base': 'Previous plan IDs must be valid MongoDB ObjectIds'
    }),
  currentPlan: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .optional()
    .messages({
      'string.pattern.base': 'Current plan ID must be a valid MongoDB ObjectId'
    })
}).or('planId', 'currentPlan').messages({
  'object.missing': 'Either planId or currentPlan is required'
});

const updateSubscribedPlanSchema = Joi.object({
  planId: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .optional()
    .messages({
      'string.pattern.base': 'Plan ID must be a valid MongoDB ObjectId'
    }),
  ownerId: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .optional()
    .messages({
      'string.pattern.base': 'Owner ID must be a valid MongoDB ObjectId'
    }),
  previousPlans: Joi.array()
    .items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/))
    .optional()
    .messages({
      'string.pattern.base': 'Previous plan IDs must be valid MongoDB ObjectIds'
    }),
  currentPlan: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .optional()
    .messages({
      'string.pattern.base': 'Current plan ID must be a valid MongoDB ObjectId'
    })
});

const changePlanSchema = Joi.object({
  newPlanId: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .required()
    .messages({
      'string.pattern.base': 'New plan ID must be a valid MongoDB ObjectId',
      'any.required': 'New plan ID is required'
    })
});

export const validateSubscribedPlan = (req: Request, res: Response, next: NextFunction): void => {
  const { error } = subscribedPlanSchema.validate(req.body);

  if (error) {
    res.status(400).json({
      message: 'Validation error',
      details: error.details.map(detail => detail.message)
    });
    return;
  }

  next();
};

export const validateSubscribedPlanUpdate = (req: Request, res: Response, next: NextFunction): void => {
  const { error } = updateSubscribedPlanSchema.validate(req.body);

  if (error) {
    res.status(400).json({
      message: 'Validation error',
      details: error.details.map(detail => detail.message)
    });
    return;
  }

  next();
};

export const validateChangePlan = (req: Request, res: Response, next: NextFunction): void => {
  const { error } = changePlanSchema.validate(req.body);

  if (error) {
    res.status(400).json({
      message: 'Validation error',
      details: error.details.map(detail => detail.message)
    });
    return;
  }

  next();
};
