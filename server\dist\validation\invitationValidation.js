"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateInvitationStatusValidation = exports.acceptInvitationValidation = exports.inviteUserValidation = void 0;
const joi_1 = __importDefault(require("joi"));
const inviteUserSchema = joi_1.default.object({
    name: joi_1.default.string().required().min(2).max(100).trim(),
    email: joi_1.default.string().required().email(),
    relation: joi_1.default.string().required().min(2).max(50).trim(),
    phone: joi_1.default.string().optional().pattern(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/).messages({
        'string.pattern.base': 'Please provide a valid phone number'
    })
});
const acceptInvitationSchema = joi_1.default.object({
    token: joi_1.default.string().required(),
    password: joi_1.default.string()
        .required()
        .min(8)
        .pattern(/[^A-Za-z0-9]/)
        .messages({
        'string.min': 'Password must be at least 8 characters',
        'string.pattern.base': 'Password must contain at least one special character'
    })
});
const updateInvitationStatusSchema = joi_1.default.object({
    status: joi_1.default.string().valid('pending', 'accepted', 'rejected').required()
});
const inviteUserValidation = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield inviteUserSchema.validateAsync(req.body);
        next();
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
});
exports.inviteUserValidation = inviteUserValidation;
const acceptInvitationValidation = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield acceptInvitationSchema.validateAsync(req.body);
        next();
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
});
exports.acceptInvitationValidation = acceptInvitationValidation;
const updateInvitationStatusValidation = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield updateInvitationStatusSchema.validateAsync(req.body);
        next();
    }
    catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
});
exports.updateInvitationStatusValidation = updateInvitationStatusValidation;
//# sourceMappingURL=invitationValidation.js.map