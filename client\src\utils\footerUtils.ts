import { CATEGORIES } from '@/constants/categories';

// Map footer link paths to category IDs
export const getCategoryIdFromPath = (path: string | undefined | null): string | null => {
  // Handle undefined, null, or empty path
  if (!path || typeof path !== 'string') {
    return null;
  }

  const pathSegments = path.split('/');

  // Extract category name from path like "/category/homeinstructions/homelocation"
  if (pathSegments.length >= 3 && pathSegments[1] === 'category') {
    const categoryName = pathSegments[2];

    switch (categoryName) {
      case 'homeinstructions':
        return CATEGORIES.HOME_INSTRUCTIONS;
      case 'homedocuments':
        return CATEGORIES.HOME_DOCUMENTS;
      case 'willinstructions':
        return CATEGORIES.WILL_INSTRUCTIONS;
      case 'funeralarrangements':
        return CATEGORIES.FUNERAL_ARRANGEMENTS;
      case 'importantcontacts':
        return CATEGORIES.IMPORTANT_CONTACTS;
      case 'socialmedia':
        return CATEGORIES.SOCIAL_MEDIA;
      case 'quickstart':
        return CATEGORIES.QUICK_START;
      case 'insurance':
        return CATEGORIES.INSURANCE; // Add insurance category support
      default:
        return null;
    }
  }

  return null;
};

// Mark footer links as disabled based on user access permissions instead of filtering them
export const markFooterLinksAccessibility = (
  footerLinks: Array<{
    heading: string;
    links: Array<{ label: string; href: string; badge?: string }>;
  }>,
  canAccessCategory: (categoryId: string) => boolean
) => {
  return footerLinks.map(section => ({
    ...section,
    links: section.links.map(link => {
      const categoryId = getCategoryIdFromPath(link.href);
      const isAccessible = categoryId ? canAccessCategory(categoryId) : true;
      return {
        ...link,
        disabled: !isAccessible
      };
    })
  }));
}; 