import { Request, Response, NextFunction } from 'express';
export declare const requestCategoriesValidation: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const approveRequestValidation: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const approveRequestFromDashboardValidation: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const updateRequestStatusValidation: (req: Request, res: Response, next: NextFunction) => Promise<void>;
