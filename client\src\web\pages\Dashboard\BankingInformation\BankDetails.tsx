import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectUserInputsBySubcategoryId,
  updateUserInput,
} from '@/store/slices/bankingInformationSlice';
import {
  QuestionItem,
  buildValidationSchema,
  generateInitialValues,
  handleDependentAnswers,
  isQuestionVisible
} from '@/web/components/Category/BankingInformation/FormFields';
import ScrollToQuestion from '@/web/components/Category/BankingInformation/ScrollToQuestion';
import GoodToKnowBox from '@/web/components/Global/GoodToKnowBox';
import SubCategoryFooterNav from '@/web/components/Global/SubCategoryFooterNav';
import SubCategoryHeader from '@/web/components/Global/SubCategoryHeader';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import SubCategoryTitle from '@/web/components/Global/SubCategoryTitle';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { Form, Formik, FormikHelpers } from 'formik';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import bankingData from '@/data/banking.json';
import { createUserInfo } from '@/utils/avatarUtils';

const BankDetails = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [error, setError] = useState<string | null>(null);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const [existingInputId, setExistingInputId] = useState<string | null>(null);

  // Get questions directly from banking.json file
  const questions = (bankingData["405"] || []) as any[];
  const userInputs = useAppSelector(selectUserInputsBySubcategoryId('405A'));
  const loading = useAppSelector(selectLoading);

  useEffect(() => {
    const loadData = async () => {
      if (user) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            await dispatch(fetchUserInputs(ownerId));
          } else {
            console.warn('No owner ID found for user');
          }
        } catch (error) {
          console.error('Error loading banking information data:', error);
          setError('Failed to load banking information. Please try refreshing the page.');
        } finally {
          setIsDataLoaded(true);
        }
      } else {
        setIsDataLoaded(true);
      }
    };
    loadData();
  }, [dispatch, user]);

  // Process existing user inputs
  const existingValues = useMemo(() => {
    if (userInputs && userInputs.length > 0) {
      const userInput = userInputs[0];
      return convertUserInputToFormValues(userInput);
    }
    return {};
  }, [userInputs]);

  // Set existing input ID separately to avoid infinite re-renders
  useEffect(() => {
    if (userInputs && userInputs.length > 0) {
      const userInput = userInputs[0];
      if (userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);
      }
    }
  }, [userInputs, existingInputId]);

  const initialValues = useMemo(() => {
    return generateInitialValues(questions, existingValues);
  }, [questions, existingValues]);

  const validationSchema = useMemo(() => {
    return buildValidationSchema(questions);
  }, [questions]);

  const handleSubmit = async (values: Record<string, any>, { setSubmitting }: FormikHelpers<Record<string, any>>) => {
    try {
      if (!user || !user.id) {
        setError('You must be logged in to save answers');
        return;
      }

      // Group answers by sectionId
      const answersBySection: Record<string, any[]> = {};
      questions.forEach(q => {
        let value = values[q.id];

        // Special handling for online banking dependent fields
        if (q.dependsOn && q.dependsOn.questionId === 'b4' && q.dependsOn.value === 'Yes') {
          // If user selected "No" for online banking, clear username and password fields
          if (values['b4'] === 'No') {
            value = "";
          }
        }

        if (value !== "" && value !== undefined && value !== null) {
          if (!answersBySection[q.sectionId]) answersBySection[q.sectionId] = [];

          // Convert frontend question types to backend-compatible types
          const backendType = q.type === 'password' ? 'text' :
                            q.type === 'dropdown' ? 'choice' :
                            q.type === 'textarea' ? 'text' :
                            q.type;

          answersBySection[q.sectionId].push({
            index: q.order,
            originalQuestionId: q.id,
            question: q.text,
            type: backendType,
            answer: Array.isArray(value) ? value.join(', ') : value
          });
        }
      });



      const formattedAnswersBySection = Object.entries(answersBySection).map(
        ([sectionId, answers]) => ({
          originalSectionId: sectionId,
          isCompleted: true,
          answers
        })
      );

      const userData = {
        userId: user.id,
        categoryId: generateObjectId(),
        originalCategoryId: '13', // Banking Information category ID
        subCategoryId: generateObjectId(),
        originalSubCategoryId: '405A', // Bank Details subcategory ID
        answersBySection: formattedAnswersBySection
      };

      if (existingInputId) {
        await dispatch(updateUserInput({ id: existingInputId, userData: userData })).unwrap();
      } else {
        const result = await dispatch(saveUserInput(userData)).unwrap();
        if (result && result._id) {
          setExistingInputId(result._id);
        }
      }

      const ownerId = await getCachedOwnerIdFromUser(user);
      if (ownerId) {
        await dispatch(fetchUserInputs(ownerId));
      }

      // Navigate to review page
      navigate('/category/bankinginformation/review');
    } catch (err) {
      console.error('Error saving banking information:', err);
      setError('Failed to save your answers. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleFieldChange = (questionId: string, value: string) => {
    // Handle dependent questions
    const resetValues = handleDependentAnswers(questionId, value, questions);
    return resetValues;
  };

  // Create tabs array (memoized to avoid recreating on every render)
  const tabs = useMemo(() => categoryTabsConfig.bankinginformation.map(tab => ({
    label: tab.label,
    path: tab.path
  })), []);

  if (!isDataLoaded || loading) {
    return (
      <div className="flex flex-col pt-20 min-h-screen">
        <AppHeader />
        <div className="flex-1 flex items-center justify-center">
          <p className="text-gray-600">Loading banking information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      <SubCategoryHeader
        title="Banking Information"
        backTo="/dashboard"
        user={{
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
          email: user?.email || '<EMAIL>',
          avatar: createUserInfo(user).avatar
        }}
      />
      <SubCategoryTabs tabs={tabs} />
      <div className="container mx-auto px-6">
        <SubCategoryTitle
          mainCategory="Banking Information"
          category="Bank Details"
          description="Provide your banking information for easy access by your trusted contacts."
        />
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              {error && (
                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {error}
                </div>
              )}
              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize={true}
              >
                {({ values, isSubmitting, setFieldValue }) => {
                  // Filter visible questions based on dependencies
                  const visibleQuestions = questions.filter(question =>
                    isQuestionVisible(question, values)
                  );

                  return (
                    <Form>
                      <div className="mt-4">
                        <ScrollToQuestion questions={visibleQuestions}>
                          {(questionRefs) => (
                            <>
                              {visibleQuestions.map((question) => (
                                <div
                                  key={question.id}
                                  id={`question-${question.id}`}
                                  ref={el => { questionRefs[question.id] = el; }}
                                >
                                  <QuestionItem
                                    question={question}
                                    formValues={values}
                                    onChange={(value) => {
                                      const resetValues = handleFieldChange(question.id, value);
                                      Object.entries(resetValues).forEach(([key, resetValue]) => {
                                        setFieldValue(key, resetValue);
                                      });
                                    }}
                                  />
                                </div>
                              ))}
                            </>
                          )}
                        </ScrollToQuestion>
                        <div className="mt-8 flex justify-end">
                          <Button
                            type="submit"
                            disabled={isSubmitting}
                            className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                          >
                            {isSubmitting ? 'Saving...' : 'Save & Continue'}
                          </Button>
                        </div>
                        <GoodToKnowBox
                          title="Editing my Answers"
                          description="Provide your banking information for easy access by your trusted contacts. Keep your information secure and up-to-date. Only share this information with trusted contacts."
                        />
                        <SubCategoryFooterNav
                          leftLabel="All topics"
                          leftTo="/category/bankinginformation/info"
                          rightLabel="Review"
                          rightTo="/category/bankinginformation/review"
                        />
                      </div>
                    </Form>
                  );
                }}
              </Formik>
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default BankDetails;
