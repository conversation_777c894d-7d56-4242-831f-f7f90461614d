import mongoose from 'mongoose';
export declare enum RoleType {
    OWNER = "Owner",
    NOMINEE = "Nominee",
    FAMILY = "Family"
}
export interface IRole extends mongoose.Document {
    _id: mongoose.Types.ObjectId;
    name: RoleType;
    description: string;
    permissions: string[];
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    hasPermission(permission: string): boolean;
}
export interface IRoleModel extends mongoose.Model<IRole> {
    getDefaultRoles(): Array<{
        name: RoleType;
        description: string;
        permissions: string[];
        isActive: boolean;
    }>;
}
export interface IRolePermissions {
    canViewAll: boolean;
    canEditAll: boolean;
    canDeleteAll: boolean;
    canCreateAll: boolean;
}
