import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { SubscriptionState, PricingPlan, Subscription } from '@/types/subscription';
import subscriptionService from '@/services/subscriptionService';

const initialState: SubscriptionState = {
  plans: [],
  currentSubscription: null,
  loading: false,
  error: null,
};

export const fetchPricingPlans = createAsyncThunk(
  'subscription/fetchPricingPlans',
  async () => {
    return await subscriptionService.getPricingPlans();
  }
);

export const fetchCurrentSubscription = createAsyncThunk(
  'subscription/fetchCurrentSubscription',
  async () => {
    return await subscriptionService.getCurrentSubscription();
  }
);

export const subscribeToPlan = createAsyncThunk(
  'subscription/subscribeToPlan',
  async ({ planId, ownerId }: { planId: string; ownerId: string }) => {
    return await subscriptionService.subscribeToPlan(planId, ownerId);
  }
);

export const cancelSubscription = createAsyncThunk(
  'subscription/cancelSubscription',
  async () => {
    return await subscriptionService.cancelSubscription();
  }
);

export const updateSubscription = createAsyncThunk(
  'subscription/updateSubscription',
  async (planId: string) => {
    return await subscriptionService.updateSubscription(planId);
  }
);

export const fetchOwnerSubscription = createAsyncThunk(
  'subscription/fetchOwnerSubscription',
  async (ownerId: string) => {
    return await subscriptionService.getOwnerSubscription(ownerId);
  }
);

const subscriptionSlice = createSlice({
  name: 'subscription',
  initialState,
  reducers: {
    clearSubscriptionError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Pricing Plans
      .addCase(fetchPricingPlans.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPricingPlans.fulfilled, (state, action) => {
        state.loading = false;
        state.plans = action.payload;
      })
      .addCase(fetchPricingPlans.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch pricing plans';
      })
      // Fetch Current Subscription
      .addCase(fetchCurrentSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCurrentSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSubscription = action.payload;
      })
      .addCase(fetchCurrentSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch current subscription';
      })
      // Subscribe to Plan
      .addCase(subscribeToPlan.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(subscribeToPlan.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSubscription = action.payload;
      })
      .addCase(subscribeToPlan.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to subscribe to plan';
      })
      // Cancel Subscription
      .addCase(cancelSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(cancelSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSubscription = action.payload;
      })
      .addCase(cancelSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to cancel subscription';
      })
      // Update Subscription
      .addCase(updateSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSubscription = action.payload;
      })
      .addCase(updateSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update subscription';
      })
      // Fetch Owner Subscription
      .addCase(fetchOwnerSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOwnerSubscription.fulfilled, (state, action) => {
        state.loading = false;
        state.currentSubscription = action.payload;
      })
      .addCase(fetchOwnerSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch owner subscription';
      });
  },
});

export const { clearSubscriptionError } = subscriptionSlice.actions;
export default subscriptionSlice.reducer; 