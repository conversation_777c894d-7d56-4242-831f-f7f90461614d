import { Request, Response, NextFunction } from 'express';
import Role from '../models/Role';
import { RoleType } from '../types/Role';

export interface AuthRequest extends Request {
  user?: any;
}

// Middleware to check if user has specific permission
export const checkPermission = (permission: string) => {
  return async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          status: 'fail',
          message: 'Authentication required'
        });
        return;
      }

      // If user doesn't have a role, deny access
      if (!req.user.roleId) {
        res.status(403).json({
          status: 'fail',
          message: 'No role assigned to user'
        });
        return;
      }

      // Get user's role
      const role = await Role.findById(req.user.roleId);
      
      if (!role || !role.isActive) {
        res.status(403).json({
          status: 'fail',
          message: 'Invalid or inactive role'
        });
        return;
      }

      // Check if role has the required permission
      if (!role.permissions.includes(permission)) {
        res.status(403).json({
          status: 'fail',
          message: `Access denied. Required permission: ${permission}`
        });
        return;
      }

      next();
    } catch (error) {
      res.status(500).json({
        status: 'error',
        message: 'Error checking permissions',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
};

// Middleware to check if user has specific role
export const checkRole = (roleType: RoleType) => {
  return async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          status: 'fail',
          message: 'Authentication required'
        });
        return;
      }

      // If user doesn't have a role, deny access
      if (!req.user.roleId) {
        res.status(403).json({
          status: 'fail',
          message: 'No role assigned to user'
        });
        return;
      }

      // Get user's role
      const role = await Role.findById(req.user.roleId);
      
      if (!role || !role.isActive) {
        res.status(403).json({
          status: 'fail',
          message: 'Invalid or inactive role'
        });
        return;
      }

      // Check if user has the required role
      if (role.name !== roleType) {
        res.status(403).json({
          status: 'fail',
          message: `Access denied. Required role: ${roleType}`
        });
        return;
      }

      next();
    } catch (error) {
      res.status(500).json({
        status: 'error',
        message: 'Error checking role',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
};

// Middleware to check if user is Owner (highest privilege)
export const requireOwner = checkRole(RoleType.OWNER);

// Middleware to check if user is Owner or Nominee
export const requireOwnerOrNominee = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        status: 'fail',
        message: 'Authentication required'
      });
      return;
    }

    if (!req.user.roleId) {
      res.status(403).json({
        status: 'fail',
        message: 'No role assigned to user'
      });
      return;
    }

    const role = await Role.findById(req.user.roleId);
    
    if (!role || !role.isActive) {
      res.status(403).json({
        status: 'fail',
        message: 'Invalid or inactive role'
      });
      return;
    }

    if (role.name !== RoleType.OWNER && role.name !== RoleType.NOMINEE) {
      res.status(403).json({
        status: 'fail',
        message: 'Access denied. Owner or Nominee role required'
      });
      return;
    }

    next();
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error checking role',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Middleware to attach user's role information to request
export const attachUserRole = async (req: AuthRequest, _res: Response, next: NextFunction): Promise<void> => {
  try {
    if (req.user && req.user.roleId) {
      const role = await Role.findById(req.user.roleId);
      if (role) {
        req.user.role = role;
      }
    }
    next();
  } catch (error) {
    // Don't fail the request if role attachment fails, just continue
    next();
  }
};
