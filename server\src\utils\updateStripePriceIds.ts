import mongoose from 'mongoose';
import PricingPlan from '../models/PricingPlan';
import dotenv from 'dotenv';

dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI as string);
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Update Stripe price IDs for existing pricing plans
const updateStripePriceIds = async (priceIdUpdates?: Array<{type: string, priceIdStripe: string}>) => {
  try {
    // Default price ID updates - replace these with your actual Stripe price IDs
    const defaultUpdates = [
    //   {
    //     type: 'temporary_key',
    //     priceIdStripe: 'price_1OqX8X2eZvKYlo2C9Q9Q9Q9Q' // Replace with your actual Stripe price ID
    //   },
      {
        type: 'spare_key',
        priceIdStripe: 'price_1RdU2KGad8zVVg7iPPG6JqCD' // Replace with your actual Stripe price ID
      },
      {
        type: 'all_access_key',
        priceIdStripe: 'price_1RdU4KGad8zVVg7iL29J0maX' // Replace with your actual Stripe price ID
      }
    ];

    const updates = priceIdUpdates || defaultUpdates;

    console.log('Starting to update Stripe price IDs...');

    // First, let's see what plans currently exist
    const existingPlans = await PricingPlan.find({}, { type: 1, priceIdStripe: 1, price: 1, displayPrice: 1 });
    console.log('\n📋 Current pricing plans before update:');
    existingPlans.forEach(plan => {
      console.log(`- ${plan.type}: $${plan.displayPrice} (Stripe ID: ${plan.priceIdStripe || 'Not set'})`);
    });

    console.log('\n🔄 Updating price IDs...');

    for (const update of updates) {
      // Validate the price ID format (basic validation)
      if (!update.priceIdStripe.startsWith('price_')) {
        console.log(`⚠️  Warning: Price ID for ${update.type} doesn't start with 'price_': ${update.priceIdStripe}`);
      }

      const result = await PricingPlan.updateOne(
        { type: update.type },
        { $set: { priceIdStripe: update.priceIdStripe } }
      );

      if (result.matchedCount > 0) {
        console.log(`✅ Updated ${update.type} with price ID: ${update.priceIdStripe}`);
      } else {
        console.log(`❌ No pricing plan found for type: ${update.type}`);
      }
    }

    // Display updated pricing plans
    console.log('\n📋 Updated pricing plans:');
    const updatedPlans = await PricingPlan.find({}, { type: 1, priceIdStripe: 1, price: 1, displayPrice: 1 });
    
    updatedPlans.forEach(plan => {
      console.log(`- ${plan.type}: $${plan.displayPrice} (Stripe ID: ${plan.priceIdStripe || 'Not set'})`);
    });

    console.log('\n✅ Stripe price ID update completed successfully!');
  } catch (error) {
    console.error('❌ Error updating Stripe price IDs:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('MongoDB disconnected');
  }
};

// Function to update a single plan's price ID
export const updateSinglePlanPriceId = async (planType: string, priceId: string) => {
  await connectDB();
  try {
    const result = await PricingPlan.updateOne(
      { type: planType },
      { $set: { priceIdStripe: priceId } }
    );

    if (result.matchedCount > 0) {
      console.log(`✅ Updated ${planType} with price ID: ${priceId}`);
    } else {
      console.log(`❌ No pricing plan found for type: ${planType}`);
    }
  } catch (error) {
    console.error('❌ Error updating single plan:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
  }
};

// Run the update if this script is executed directly
if (require.main === module) {
  connectDB().then(() => {
    updateStripePriceIds();
  });
}

export default updateStripePriceIds; 