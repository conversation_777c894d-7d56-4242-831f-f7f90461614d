"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.protect = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = __importDefault(require("../models/User"));
const customError_1 = require("../utils/customError");
const protect = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const testToken = req.headers.authorization;
        let token;
        if (testToken && testToken.startsWith("Bearer")) {
            token = testToken.split(" ")[1];
        }
        if (!token) {
            const error = new customError_1.CustomError("You are not logged in", 401);
            return next(error);
        }
        const decodedToken = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        const user = yield User_1.default.findById(decodedToken.userId);
        if (!user) {
            const error = new customError_1.CustomError("The user with the given token does not exist", 401);
            return next(error);
        }
        req.user = user;
        next();
    }
    catch (error) {
        next(error);
    }
});
exports.protect = protect;
//# sourceMappingURL=protectMiddleware.js.map