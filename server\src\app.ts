import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import bodyParser from 'body-parser';
import cookieParser from 'cookie-parser';
import session from 'express-session';
import passport from 'passport';
import path from 'path';
import dotenv from 'dotenv';
import fs from 'fs';


import { CustomError } from './utils/customError';
import './config/passport';

import authRoutes from './routes/authRoutes';
import categoryRoutes from './routes/categoryRoutes';
import subcategoryRoutes from './routes/subcategoryRoutes';
import questionRoutes from './routes/questionRoutes';
import googleAuthRoutes from './routes/googleAuthRoutes';
import userInputRoutes from './routes/userInputRoutes';
import roleRoutes from './routes/roleRoutes';
import ownerRoutes from './routes/ownerRoutes';
import pricingPlanRoutes from './routes/pricingPlanRoutes';
import subscribedPlanRoutes from './routes/subscribedPlanRoutes';
import invitationRoutes from './routes/invitationRoutes';
import requestedCategoriesRoutes from './routes/requestedCategoriesRoutes';
import emergencyAccessRoutes from './routes/emergencyAccessRoutes';
import testingRoutes from './routes/testing';
import StripeRoutes from "./routes/stripeRoutes"
dotenv.config();

const app = express();
const uploadsDir = path.join(__dirname, '../uploads');

if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Mount Stripe webhook route BEFORE bodyParser
app.use('/v1/api/stripe/webhooks', StripeRoutes);

function configureMiddleware() {
  // app.use(cors({
  //   origin: process.env.FRONTEND_URL,
  //   credentials: true,
  //   methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  //   allowedHeaders: ['Content-Type', 'Authorization']
  // }));


  app.use(cors({
    origin: ['https://heirkeyportal.com', 'http://localhost:3000', 'http://localhost:5173'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'stripe-signature'],
    exposedHeaders: ['Content-Length', 'Content-Type']
  }));

  app.use(helmet({
    crossOriginResourcePolicy: { policy: 'cross-origin' }
  }));

  app.use(cookieParser());
  
  // Configure body parser to skip Stripe webhook routes
  app.use(bodyParser.json({
    verify: (req, res, buf) => {
      // Store raw body for Stripe webhook signature verification
      (req as any).rawBody = buf;
    }
  }));
  app.use(bodyParser.urlencoded({ extended: true }));

  app.use(session({
    secret: process.env.SESSION_SECRET || 'default',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 * 1000
    }
  }));

  app.use(passport.initialize());
  app.use(passport.session());
}



function configureRoutes() {

  app.use('/uploads', express.static(uploadsDir));

  app.use('/v1/api/auth', authRoutes);
  app.use('/v1/api/categories', categoryRoutes);
  app.use('/v1/api/subcategories', subcategoryRoutes);
  app.use('/v1/api/questions', questionRoutes);
  app.use('/v1/api/user-inputs', userInputRoutes);
  app.use('/v1/api/roles', roleRoutes);
  app.use('/v1/api/owners', ownerRoutes);
  app.use('/v1/api/pricing-plans', pricingPlanRoutes);
  app.use('/v1/api/subscriptions', subscribedPlanRoutes);
  app.use('/v1/api/invitations', invitationRoutes);
  app.use('/v1/api/category-requests', requestedCategoriesRoutes);
  app.use('/v1/api/emergency-access', emergencyAccessRoutes);
  // Mount the rest of the Stripe routes (except webhooks) AFTER bodyParser
  app.use('/v1/api/stripe', StripeRoutes);
  app.use('/v1/api/testing', testingRoutes);

  // Direct health check route at root level
  app.get('/health', (_req: Request, res: Response) => {
    res.status(200).json({
      status: 'ok',
      message: 'server is running....',
      timestamp: new Date(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime() + ' seconds'
    });
  });

  app.use('/', googleAuthRoutes);

  app.get('/uploads/:filename', (req: Request, res: Response) => {
    const filePath = path.join(uploadsDir, req.params.filename);

    fs.access(filePath, fs.constants.F_OK, (err) => {
      if (err) {
        console.error(`File not found: ${filePath}`);
        return res.status(404).json({ message: 'Image not found' });
      }

      res.sendFile(filePath, (err) => {
        if (err && !res.headersSent) {
          console.error(`Error sending file: ${err.message}`);
          res.status(500).json({ message: 'Failed to send file' });
        }
      });
    });
  });
}

function configureErrorHandling() {
  app.use((err: CustomError, _req: Request, res: Response, _next: NextFunction) => {
    const statusCode = err.statusCode || 500;
    const status = err.status || 'error';

    res.status(statusCode).json({
      status,
      message: err.message,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  });
}

configureMiddleware();
configureRoutes();
configureErrorHandling();

export default app;
