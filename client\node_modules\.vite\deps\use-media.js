import {
  require_react
} from "./chunk-HUL2CLQT.js";
import {
  __commonJS
} from "./chunk-EWTE5DHJ.js";

// node_modules/use-media/lib/utilities/camelToHyphen.js
var require_camelToHyphen = __commonJS({
  "node_modules/use-media/lib/utilities/camelToHyphen.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    function camelToHyphen(camelString) {
      return camelString.replace(/[A-Z]/g, function(string) {
        return "-" + string.toLowerCase();
      }).toLowerCase();
    }
    exports.default = camelToHyphen;
  }
});

// node_modules/use-media/lib/utilities/queryObjectToString.js
var require_queryObjectToString = __commonJS({
  "node_modules/use-media/lib/utilities/queryObjectToString.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    var camelToHyphen_1 = require_camelToHyphen();
    var QUERY_COMBINATOR = " and ";
    function queryObjectToString(query) {
      if (typeof query === "string") {
        return query;
      }
      return Object.entries(query).map(function(_a) {
        var feature = _a[0], value = _a[1];
        var convertedFeature = camelToHyphen_1.default(feature);
        var convertedValue = value;
        if (typeof convertedValue === "boolean") {
          return convertedValue ? convertedFeature : "not " + convertedFeature;
        }
        if (typeof convertedValue === "number" && /[height|width]$/.test(convertedFeature)) {
          convertedValue = convertedValue + "px";
        }
        return "(" + convertedFeature + ": " + convertedValue + ")";
      }).join(QUERY_COMBINATOR);
    }
    exports.default = queryObjectToString;
  }
});

// node_modules/use-media/lib/utilities/noop.js
var require_noop = __commonJS({
  "node_modules/use-media/lib/utilities/noop.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    function noop() {
    }
    exports.default = noop;
  }
});

// node_modules/use-media/lib/utilities/index.js
var require_utilities = __commonJS({
  "node_modules/use-media/lib/utilities/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.noop = exports.queryObjectToString = exports.camelToHyphen = void 0;
    var camelToHyphen_1 = require_camelToHyphen();
    Object.defineProperty(exports, "camelToHyphen", { enumerable: true, get: function() {
      return camelToHyphen_1.default;
    } });
    var queryObjectToString_1 = require_queryObjectToString();
    Object.defineProperty(exports, "queryObjectToString", { enumerable: true, get: function() {
      return queryObjectToString_1.default;
    } });
    var noop_1 = require_noop();
    Object.defineProperty(exports, "noop", { enumerable: true, get: function() {
      return noop_1.default;
    } });
  }
});

// node_modules/use-media/lib/useMedia.js
var require_useMedia = __commonJS({
  "node_modules/use-media/lib/useMedia.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.useMediaLayout = exports.useMedia = exports.mockMediaQueryList = void 0;
    var react_1 = require_react();
    var utilities_1 = require_utilities();
    exports.mockMediaQueryList = {
      media: "",
      matches: false,
      onchange: utilities_1.noop,
      addListener: utilities_1.noop,
      removeListener: utilities_1.noop,
      addEventListener: utilities_1.noop,
      removeEventListener: utilities_1.noop,
      dispatchEvent: function(_) {
        return true;
      }
    };
    var createUseMedia = function(effect) {
      return function(rawQuery, defaultState) {
        if (defaultState === void 0) {
          defaultState = false;
        }
        var _a = react_1.useState(defaultState), state = _a[0], setState = _a[1];
        var query = utilities_1.queryObjectToString(rawQuery);
        effect(function() {
          var mounted = true;
          var mediaQueryList = typeof window === "undefined" ? exports.mockMediaQueryList : window.matchMedia(query);
          var onChange = function() {
            if (!mounted) {
              return;
            }
            setState(Boolean(mediaQueryList.matches));
          };
          mediaQueryList.addListener(onChange);
          setState(mediaQueryList.matches);
          return function() {
            mounted = false;
            mediaQueryList.removeListener(onChange);
          };
        }, [query]);
        return state;
      };
    };
    exports.useMedia = createUseMedia(react_1.useEffect);
    exports.useMediaLayout = createUseMedia(react_1.useLayoutEffect);
    exports.default = exports.useMedia;
  }
});

// node_modules/use-media/lib/index.js
var require_lib = __commonJS({
  "node_modules/use-media/lib/index.js"(exports) {
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.useMediaLayout = exports.useMedia = exports.default = void 0;
    var useMedia_1 = require_useMedia();
    Object.defineProperty(exports, "default", { enumerable: true, get: function() {
      return useMedia_1.default;
    } });
    Object.defineProperty(exports, "useMedia", { enumerable: true, get: function() {
      return useMedia_1.useMedia;
    } });
    Object.defineProperty(exports, "useMediaLayout", { enumerable: true, get: function() {
      return useMedia_1.useMediaLayout;
    } });
  }
});
export default require_lib();
//# sourceMappingURL=use-media.js.map
