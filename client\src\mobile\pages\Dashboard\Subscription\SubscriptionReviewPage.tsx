import { useState, useEffect } from 'react';
import CategoryReviewPage from '@/mobile/components/category/CategoryReviewPage';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  UserInput as ReduxUserInput
} from '@/store/slices/subscriptionCategorySlice';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import subscriptionData from '@/data/subscription.json';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Button } from '@/components/ui/button';

// Define interfaces for the data structure
interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

interface UserInput {
  userId: string;
  categoryId: string;
  originalCategoryId: string;
  subCategoryId: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
}

// Map section IDs to their routes
const subCategoryRoutes: Record<string, string> = {
  '1101A': 'netflix',
  '1102A': 'appletv',
  '1103A': 'amazonprime',
  '1104A': 'otherservices',
};

// Map question IDs to their section IDs
const questionToSubcategoryMap: Record<string, string> = {};

// Initialize the question to section mapping
Object.entries(subscriptionData).forEach(([subcategoryId, questions]) => {
  questions.forEach((question: any) => {
    // Use the sectionId from the question data instead of the subcategoryId
    if (question.sectionId) {
      questionToSubcategoryMap[question.id] = question.sectionId;
    }
  });
});

const SubscriptionReviewPage = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { categoryName } = useParams();
  const { user } = useAuth();
  const [topics, setTopics] = useState<Array<{
  id: string;
  title: string;
  subtitle?: string;
  data: string;
  onEdit: () => void;
  }>>([]);
  const [error, setError] = useState<string | null>(null);

  // Get data from Redux store
  const userInputs = useAppSelector((state: any) => state.subscriptionCategory.userInputs) as ReduxUserInput[];
  const isLoading = useAppSelector((state: any) => state.subscriptionCategory.loading);

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: user?.image || avatar
  };

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs to create topics for review page
  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      // Transform the data for the review page
      const allTopics: Array<{
        id: string;
        title: string;
        subtitle?: string;
        data: string;
        onEdit: () => void;
      }> = [];

      // Process all user inputs
      userInputs.forEach((userInput: ReduxUserInput) => {        
        userInput.answersBySection.forEach((section) => {
          // Special handling for Other Services (section 1104A)
          if (section.originalSectionId === '1104A') {
            // Find the main dropdown selection
            const dropdownAnswer = section.answers.find(a => a.originalQuestionId === 's10');
            
            if (dropdownAnswer?.answer) {
              try {
                // Parse the JSON structure for services
                const servicesData = JSON.parse(dropdownAnswer.answer);
                
                if (Array.isArray(servicesData)) {
                  // Handle new structure with multiple services and accounts
                  servicesData.forEach((service: any, index: number) => {
                    if (service.serviceName && service.accounts && Array.isArray(service.accounts)) {
                      const serviceName = service.serviceName;
                      const accounts = service.accounts.filter((account: any) => 
                        account.username || account.password
                      );
                      
                      if (accounts.length > 0) {
                        // Format accounts for display in the requested format
                        const displayName = service.displayName || service.serviceName;
                        let accountsDisplay = `service name: ${displayName}\n`;
                        accounts.forEach((account: any, accountIndex: number) => {
                          accountsDisplay += `Account ${accountIndex + 1}\n`;
                          const parts = [];
                          if (account.username) {
                            parts.push(`username: ${account.username}`);
                          }
                          if (account.password) {
                            parts.push(`password: ${account.password}`);
                          }
                          accountsDisplay += parts.join('  |  ') + '\n';
                        });
                        
                        allTopics.push({
                          id: `service-${displayName}-${index}`,
                          title: displayName,
                          subtitle: `${accounts.length} account${accounts.length !== 1 ? 's' : ''}`,
                          data: accountsDisplay.trim(),
                          onEdit: () => {
                            const route = subCategoryRoutes['1104A'];
                            if (route) {
                              // Pass the service index for highlighting
                              const basePath = `/category/${categoryName || 'subscription'}/${route}`;
                              navigate(`${basePath}?serviceIndex=${index}`);
                            }
                          }
                        });
                      } else {
                        // Service with no valid accounts
                        const displayName = service.displayName || service.serviceName;
                        allTopics.push({
                          id: `service-${displayName}-${index}`,
                          title: displayName,
                          subtitle: 'Subscription Service',
                          data: 'No credentials provided',
                          onEdit: () => {
                            const route = subCategoryRoutes['1104A'];
                            if (route) {
                              // Pass the service index for highlighting
                              const basePath = `/category/${categoryName || 'subscription'}/${route}`;
                              navigate(`${basePath}?serviceIndex=${index}`);
                            }
                          }
                        });
                      }
                    }
                  });
                } else {
                  // Handle old single service format
                  const selectedService = dropdownAnswer.answer;
                  
                  if (selectedService === 'Other') {
                    // Handle "Other" service with custom name
                    const serviceNameAnswer = section.answers.find(a => a.originalQuestionId === 's11');
                    const serviceName = serviceNameAnswer?.answer || 'Custom Service';
                    
                    allTopics.push({
                      id: 'other-service-custom',
                      title: serviceName,
                      subtitle: 'Custom Service',
                      data: 'Service added manually',
                      onEdit: () => {
                        const route = subCategoryRoutes['1104A'];
                        if (route) {
                          const basePath = `/category/${categoryName || 'subscription'}/${route}`;
                          navigate(`${basePath}`);
                        }
                      }
                    });
                  } else {
                    // Handle specific service selection (old format)
                    const usernameAnswer = section.answers.find(a => 
                      a.originalQuestionId === 's12' || // Disney+
                      a.originalQuestionId === 's14' || // Hulu
                      a.originalQuestionId === 's16' || // Max
                      a.originalQuestionId === 's18' || // Peacock
                      a.originalQuestionId === 's20' || // Paramount+
                      a.originalQuestionId === 's22' || // Apple TV+
                      a.originalQuestionId === 's24' || // YouTube TV
                      a.originalQuestionId === 's26'    // Sling TV
                    );
                    
                    const passwordAnswer = section.answers.find(a => 
                      a.originalQuestionId === 's13' || // Disney+
                      a.originalQuestionId === 's15' || // Hulu
                      a.originalQuestionId === 's17' || // Max
                      a.originalQuestionId === 's19' || // Peacock
                      a.originalQuestionId === 's21' || // Paramount+
                      a.originalQuestionId === 's23' || // Apple TV+
                      a.originalQuestionId === 's25' || // YouTube TV
                      a.originalQuestionId === 's27'    // Sling TV
                    );
                    
                    const credentials = [];
                    if (usernameAnswer?.answer) {
                      credentials.push(`Username: ${usernameAnswer.answer}`);
                    }
                    if (passwordAnswer?.answer) {
                      credentials.push(`Password: ${passwordAnswer.answer}`);
                    }
                    
                    allTopics.push({
                      id: `service-${selectedService}`,
                      title: selectedService,
                      subtitle: 'Subscription Service',
                      data: credentials.length > 0 ? credentials.join(' | ') : 'No credentials provided',
                      onEdit: () => {
                        const route = subCategoryRoutes['1104A'];
                        if (route) {
                          const basePath = `/category/${categoryName || 'subscription'}/${route}`;
                          navigate(`${basePath}`);
                        }
                      }
                    });
                  }
                }
              } catch (error) {
                console.error('Error parsing service data:', error);
                // Fallback to old format
                const selectedService = dropdownAnswer.answer;
                allTopics.push({
                  id: `service-${selectedService}`,
                  title: selectedService,
                  subtitle: 'Subscription Service',
                  data: 'Error parsing service data',
                  onEdit: () => {
                    const route = subCategoryRoutes['1104A'];
                    if (route) {
                      const basePath = `/category/${categoryName || 'subscription'}/${route}`;
                      navigate(`${basePath}`);
                    }
                  }
                });
              }
            }
            
            // Also show any other answers in this section that aren't part of the main service selection
            section.answers.forEach((answer) => {
              if (!['s10', 's11', 's12', 's13', 's14', 's15', 's16', 's17', 's18', 's19', 's20', 's21', 's22', 's23', 's24', 's25', 's26', 's27'].includes(answer.originalQuestionId)) {
                const questionId = answer.originalQuestionId;
                const allQuestions = subscriptionData['1104'];
                const questionData = allQuestions?.find((q: any) => q.id === questionId);
                if (questionData) {
                  allTopics.push({
                    id: questionId,
                    title: questionData.text,
                    subtitle: `Section: ${section.originalSectionId}`,
                    data: answer.answer,
                    onEdit: () => {
                      const route = subCategoryRoutes['1104A'];
                      if (route) {
                        const basePath = `/category/${categoryName || 'subscription'}/${route}`;
                        navigate(`${basePath}?questionId=${questionId}&fromReview=1`);
                      }
                    }
                  });
                }
              }
            });
          } else {
            // Default logic for all other sections (Netflix, Apple TV, Amazon Prime)
            section.answers.forEach((answer) => {
              // Find the original question from our data
              const questionId = answer.originalQuestionId;
              
              // Use the questionToSubcategoryMap to get the section ID
              const sectionId = questionToSubcategoryMap[questionId];
              
              // Map section ID to the correct subcategory key for subscription data
              const sectionToSubcategoryMap: Record<string, string> = {
                '1101A': '1101', // Netflix
                '1102A': '1102', // Apple TV
                '1103A': '1103', // Amazon Prime
                '1104A': '1104'  // Other Services
              };
              
              const subcategoryKey = sectionToSubcategoryMap[section.originalSectionId];
              const allQuestions = subscriptionData[subcategoryKey as keyof typeof subscriptionData];
              const questionData = allQuestions?.find((q: any) => q.id === questionId);

              if (questionData) {
                // Check if this is a main question (s1, s4, s7) or a dependent question
                const isMainQuestion = ['s1', 's4', 's7'].includes(questionId);
                const isDependentQuestion = ['s2', 's3', 's5', 's6', 's8', 's9'].includes(questionId);
                
                // If it's a dependent question (username/password), check if the main question was "Yes"
                if (isDependentQuestion) {
                  const mainQuestionId = questionId === 's2' || questionId === 's3' ? 's1' : // Netflix
                                       questionId === 's5' || questionId === 's6' ? 's4' : // Apple TV
                                       's7'; // Amazon Prime
                  
                  const mainQuestionAnswer = section.answers.find(a => a.originalQuestionId === mainQuestionId);
                  
                  // Only show dependent questions if main question was "Yes"
                  if (mainQuestionAnswer?.answer !== 'yes') {
                    return; // Skip this question
                  }
                }

                let displayAnswer = answer.answer;
                
                // Handle choice type answers
                if (questionData.type === 'choice') {
                  // If the answer is "Other", look for additional details
                  if (answer.answer === "Other") {
                    const otherAnswer = section.answers.find(a => a.originalQuestionId === `${answer.originalQuestionId}_other`);
                    if (otherAnswer) {
                      displayAnswer = `Other: ${otherAnswer.answer}`;
                    }
                  }
                }

                // Get section title for subtitle
    const sectionTitles: Record<string, string> = {
      '1101A': 'Netflix',
      '1102A': 'Apple TV',
      '1103A': 'Amazon Prime',
      '1104A': 'Other Services'
    };
        const sectionTitle = sectionTitles[section.originalSectionId] || section.originalSectionId;
        
                allTopics.push({
                  id: questionId,
                  title: questionData.text,
                  subtitle: `Section: ${sectionTitle}`,
                  data: displayAnswer,
            onEdit: () => {
                    // Navigate to the appropriate section page with question ID as a parameter
                    const route = subCategoryRoutes[section.originalSectionId];
                    if (route) {
                      const basePath = `/category/${categoryName || 'subscription'}/${route}`;
                      navigate(`${basePath}?questionId=${questionId}`);
                    }
                  }
                });
              }
            });
        }
      });
    });

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, navigate, categoryName]);

  if (isLoading) {
    return <div className="p-4 text-center">Loading your answers...</div>;
  }

  if (error) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        </div>
    );
  }

  if (!user || !user.id) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertDescription>You must be logged in to view your answers</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (userInputs.length === 0 && !isLoading) {
    return (
      <div className="p-4">
        <Alert>
          <AlertDescription>No subscription information answers found. Please complete some questions first.</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <CategoryReviewPage
      categoryTitle="Subscription Services"
      infoTitle="How to edit your information"
      infoDescription="Review the details about your subscription services. Tap Edit on any item to update it."
      topics={topics}
      onPrint={() => window.print()}
      afterTopics={
        <Button 
          onClick={() => navigate('/dashboard')}
          className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
        >
          Back to Dashboard
        </Button>
      }
    />
  );
};

export default SubscriptionReviewPage;
