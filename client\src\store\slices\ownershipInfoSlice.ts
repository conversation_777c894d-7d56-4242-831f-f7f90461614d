import { createSlice, createAsyncThunk, PayloadAction, createSelector } from '@reduxjs/toolkit';
import userInputService from '@/services/userInputService';
import ownershipInfoData from '../../data/ownershipInfo.json';
import { Question } from '@/web/components/Category/OwnershipInfo/FormFields';
import type { EncryptableUserInput } from '@/utils/encryptionUtils';

// Types
export interface SubCategory {
  id: string;
  title: string;
  questionsCount: number;
}

export interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

export interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

// Helper function to convert EncryptableUserInput to UserInput
const convertToUserInput = (input: EncryptableUserInput): UserInput => {
  return {
    ...input,
    answersBySection: input.answersBySection.map(section => ({
      ...section,
      isCompleted: section.isCompleted ?? false,
      answers: section.answers.map(answer => ({
        ...answer,
        question: answer.question ?? '' // Provide default empty string if question is undefined
      }))
    }))
  };
};

export interface UserInput {
  _id?: string;
  userId: string;
  categoryId?: string;
  originalCategoryId: string;
  subCategoryId?: string;
  originalSubCategoryId: string;
  answersBySection: {
    sectionId?: string;
    originalSectionId: string;
    isCompleted: boolean;
    answers: {
      index: number;
      originalQuestionId: string;
      question: string;
      type: string;
      answer: string;
    }[];
  }[];
}

export interface ProgressStats {
  totalQuestions: number;
  answeredQuestions: number;
  completionPercentage: number;
}

// Normalize questions to ensure all 'choice' questions have an options array
function normalizeQuestions(data: Record<string, any[]>): Record<string, Question[]> {
  const result: Record<string, Question[]> = {};
  for (const [key, questions] of Object.entries(data)) {
    result[key] = questions.map((q) => {
      if (q.type === 'choice' || q.type === 'dropdown') {
        return { ...q, options: q.options || [] };
      }
      return q;
    });
  }
  return result;
}

interface OwnershipInfoState {
  questions: Record<string, Question[]>;
  subcategories: SubCategory[];
  userInputs: UserInput[];
  loading: boolean;
  error: string | null;
  progressStats: ProgressStats;
}

const initialState: OwnershipInfoState = {
  questions: normalizeQuestions(ownershipInfoData),
  subcategories: [
    { id: '402A', title: 'Property Information', questionsCount: (ownershipInfoData['402'] || []).filter(q => q.sectionId === '402A').length },
    { id: '402B', title: 'Vehicle Information', questionsCount: (ownershipInfoData['402'] || []).filter(q => q.sectionId === '402B').length },
    { id: '402C', title: 'Driver Information', questionsCount: (ownershipInfoData['402'] || []).filter(q => q.sectionId === '402C').length },
  ],
  userInputs: [],
  loading: false,
  error: null,
  progressStats: {
    totalQuestions: Object.values(ownershipInfoData).reduce(
      (sum, questions) => sum + questions.length, 0
    ),
    answeredQuestions: 0,
    completionPercentage: 0
  }
};

// Create async thunks
export const fetchUserInputs = createAsyncThunk<UserInput[], string>(
  'ownershipInfo/fetchUserInputs',
  async (userOrOwnerId: string, { rejectWithValue }) => {
    try {
      // Try owner-based fetching first, fallback to user-based
      const response = await userInputService.getUserInputsByCategory(userOrOwnerId, '10', true);
      return response as UserInput[];
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch user inputs');
    }
  }
);

export const saveUserInput = createAsyncThunk(
  'ownershipInfo/saveUserInput',
  async (userData: Omit<UserInput, '_id'>) => {
    return await userInputService.saveUserInput(userData);
  }
);

export const updateUserInput = createAsyncThunk(
  'ownershipInfo/updateUserInput',
  async ({ id, userData }: { id: string; userData: Omit<UserInput, '_id'> }) => {
    return await userInputService.updateUserInput(id, userData);
  }
);

const ownershipInfoSlice = createSlice({
  name: 'ownershipInfo',
  initialState,
  reducers: {
    updateProgressStats: (state) => {
      const totalQuestions = Object.values(state.questions).reduce(
        (sum, questions) => sum + questions.length, 0
      );
      let answeredQuestions = 0;
      state.userInputs.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          answeredQuestions += section.answers.length;
        });
      });
      const completionPercentage = totalQuestions > 0
        ? Math.round((answeredQuestions / totalQuestions) * 100)
        : 0;
      state.progressStats = {
        totalQuestions,
        answeredQuestions,
        completionPercentage
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserInputs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserInputs.fulfilled, (state, action: PayloadAction<EncryptableUserInput[]>) => {
        state.loading = false;
        state.userInputs = action.payload.map(convertToUserInput);
        // Update progress stats after fetching user inputs
        const totalQuestions = state.progressStats.totalQuestions;
        let answeredQuestions = 0;
        state.userInputs.forEach(userInput => {
          userInput.answersBySection.forEach((section: SectionAnswers) => {
            answeredQuestions += section.answers.length;
          });
        });
        const completionPercentage = totalQuestions > 0
          ? Math.round((answeredQuestions / totalQuestions) * 100)
          : 0;
        state.progressStats = {
          totalQuestions,
          answeredQuestions,
          completionPercentage
        };
      })
      .addCase(fetchUserInputs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error?.message || 'Failed to fetch user inputs';
      })
      .addCase(saveUserInput.fulfilled, (state, action: PayloadAction<EncryptableUserInput>) => {
        const userInput = convertToUserInput(action.payload);
        state.userInputs.push(userInput);
        // Update progress stats after saving
        state.progressStats.answeredQuestions += userInput.answersBySection.reduce(
          (sum: number, section: SectionAnswers) => sum + section.answers.length, 0
        );
        state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
          ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
          : 0;
      })
      .addCase(saveUserInput.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error?.message || 'Failed to save user input';
      })
      .addCase(updateUserInput.fulfilled, (state, action: PayloadAction<EncryptableUserInput>) => {
        const userInput = convertToUserInput(action.payload);
        const index = state.userInputs.findIndex(input => input._id === userInput._id);
        if (index !== -1) {
          state.userInputs[index] = userInput;
        }
        // Recalculate progress stats
        state.progressStats.answeredQuestions = state.userInputs.reduce(
          (sum: number, input: UserInput) => sum + input.answersBySection.reduce(
            (sectionSum: number, section: SectionAnswers) => sectionSum + section.answers.length, 0
          ), 0
        );
        state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
          ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
          : 0;
      })
      .addCase(updateUserInput.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error?.message || 'Failed to update user input';
      });
  },
});

export const { updateProgressStats } = ownershipInfoSlice.actions;

// Basic selectors
const selectOwnershipInfoState = (state: { ownershipInfo: OwnershipInfoState }) => state.ownershipInfo;

export const selectLoading = createSelector(
  [selectOwnershipInfoState],
  (ownershipInfo) => ownershipInfo.loading
);

export const selectError = createSelector(
  [selectOwnershipInfoState],
  (ownershipInfo) => ownershipInfo.error
);

export const selectUserInputs = createSelector(
  [selectOwnershipInfoState],
  (ownershipInfo) => ownershipInfo.userInputs
);

export const selectSubcategories = createSelector(
  [selectOwnershipInfoState],
  (ownershipInfo) => ownershipInfo.subcategories
);

export const selectProgressStats = createSelector(
  [selectOwnershipInfoState],
  (ownershipInfo) => ownershipInfo.progressStats
);

export const selectQuestions = createSelector(
  [selectOwnershipInfoState],
  (ownershipInfo) => ownershipInfo.questions
);

// Memoized selectors with parameters
export const selectSubcategoryById = (subcategoryId: string) =>
  createSelector(
    [selectSubcategories],
    (subcategories) => subcategories.find(subcategory => subcategory.id === subcategoryId)
  );

export const selectQuestionsBySubcategoryId = (subcategoryId: string) =>
  createSelector(
    [selectQuestions],
    (questions) => {
      // For ownership info, we need to get questions from the '402' array and filter by sectionId
      const allQuestions = questions['402'] || [];
      return allQuestions.filter((q: Question) => q.sectionId === subcategoryId);
    }
  );

// Selector to filter questions by sectionId within the 402 array
export const selectQuestionsBySectionId = (sectionId: string) =>
  createSelector(
    [selectQuestions],
    (questions) => {
      // Filter questions by sectionId
      const allQuestions = questions['402'] || [];
      return allQuestions.filter((question: Question) => question.sectionId === sectionId);
    }
  );

export const selectUserInputsBySubcategoryId = (subcategoryId: string) =>
  createSelector(
    [selectUserInputs],
    (userInputs) => userInputs ? userInputs.filter(input => 
      input.answersBySection?.some(section => section.originalSectionId === subcategoryId)
    ) : []
  );

export default ownershipInfoSlice.reducer;
