import React from 'react';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import HomeInstructionSection from '@/web/components/QuickStartPage/HomeInstructionSection';
import WillInstructionSection from '@/web/components/QuickStartPage/WillInstructionSection';
import FuneralSection from '@/web/components/QuickStartPage/FuneralSection';
import ImportantContactsSection from '@/web/components/QuickStartPage/ImportantContactSection';
import HousingSection from '@/web/components/QuickStartPage/HousingSection';
import { useAuth } from '@/contexts/AuthContext';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { Avatar } from '@radix-ui/react-avatar';
import { useNavigate } from 'react-router-dom';
import { createUserInfo } from '@/utils/avatarUtils';

function QuickStartForm() {
  const { user } = useAuth();
  const userInfo = createUserInfo(user); 
  const navigate = useNavigate();
  return (
    <div className="flex flex-col min-h-screen">
      <AppHeader />
      <div className="bg-gradient-to-r from-[#1F4168] to-[#2BCFD5] text-white py-4 mt-20">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-1">Quick Start</h1>
              <button
                onClick={() => navigate('/dashboard')}
                className="flex items-center text-sm hover:underline text-[#2BCFD5] font-semibold text-md mt-1 mb-1"
              >
                <span className="mr-1">&larr;</span> Back to Home
              </button>
            </div>
            <div className="flex items-center">
              <div className="text-right mr-4">
                <div className="font-semibold">{userInfo.name}</div>
                <div className="text-sm opacity-80">{userInfo.email}</div>
              </div>
              <Avatar className="rounded-full w-14 h-14 bg-white overflow-hidden">
                <img
                  src={userInfo.avatar}
                  alt={userInfo.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = avatar;
                  }}
                />
              </Avatar>
            </div>
          </div>
        </div>
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-8">
            {/* Home Location Section */}
            <div id="home-instructions">
              <h2 className="text-2xl font-semibold mb-4 text-gray-800">Home Instructions</h2>
              <HomeInstructionSection />
            </div>
            
            {/* Housing & Ownership Section */}
            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-800">Housing & Ownership</h2>
              <HousingSection />
            </div>
            
            {/* Location Instructions Section */}
            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-800">Will Instructions</h2>
              <WillInstructionSection />
            </div>
            
            {/* Funeral Arrangements Section */}
            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-800">Funeral Arrangements</h2>
              <FuneralSection />
            </div>

            {/* Important Contacts Section */}
            <div>
              <h2 className="text-2xl font-semibold mb-4 text-gray-800">Important Contacts</h2>
              <ImportantContactsSection />
            </div>

            {/* Next Category Button */}
            <div className="flex justify-end pt-4">
              <button
                onClick={() => navigate('/category/homeinstructions')}
                className="bg-[#2BCFD5] hover:bg-[#19bbb5] text-white font-semibold px-6 py-3 rounded shadow"
              >
                Go to Home Instructions
              </button>
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}

export default QuickStartForm;
