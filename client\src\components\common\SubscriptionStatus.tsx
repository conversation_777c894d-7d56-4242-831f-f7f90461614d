import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Crown, Key, Lock, Unlock, User, ArrowUpRight } from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';

interface SubscriptionStatusProps {
  className?: string;
  showUpgradeButton?: boolean;
}

export const SubscriptionStatus: React.FC<SubscriptionStatusProps> = ({ 
  className, 
  showUpgradeButton = true 
}) => {
  const { userAccess, handleRequestEmergencyAccess } = useAccessControl();

  if (!userAccess) {
    return null;
  }

  const getSubscriptionInfo = () => {
    switch (userAccess.subscriptionType) {
      case 'temporary_key':
        return {
          name: 'Temporary Key',
          description: 'Free trial with limited access',
          icon: <Key className="h-4 w-4" />,
          color: 'bg-blue-100 text-blue-800',
          features: [
            'Access to Home Instructions and Quick Start categories',
            'Both owner and key holder can access',
            'Perfect for trying HeirKey'
          ]
        };
      
      case 'spare_key':
        return {
          name: 'Spare Key',
          description: 'Limited access for key holders',
          icon: <Lock className="h-4 w-4" />,
          color: 'bg-orange-100 text-orange-800',
          features: [
            'Owner has full access',
            'Key holders need emergency access request',
            '24-hour auto-grant if not denied',
            'Key holders can view but not edit'
          ]
        };
      
      case 'all_access_key':
        return {
          name: 'All Access Key',
          description: 'Full access for everyone',
          icon: <Crown className="h-4 w-4" />,
          color: 'bg-purple-100 text-purple-800',
          features: [
            'Full access to all categories',
            'Both owner and key holder can edit',
            'Perfect for family sharing',
            'No restrictions'
          ]
        };
      
      default:
        return {
          name: 'Unknown',
          description: 'Subscription type not set',
          icon: <User className="h-4 w-4" />,
          color: 'bg-gray-100 text-gray-800',
          features: []
        };
    }
  };

  const subscriptionInfo = getSubscriptionInfo();

  const renderEmergencyAccessStatus = () => {
    if (userAccess.subscriptionType !== 'spare_key') return null;

    if (userAccess.isOwner) {
      return (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Unlock className="h-4 w-4 text-green-600" />
            <span className="font-medium text-green-800">Owner Access</span>
          </div>
          <p className="text-sm text-green-700">
            You have full access to all categories and can manage emergency access requests.
          </p>
        </div>
      );
    }

    if (userAccess.emergencyAccessGranted) {
      return (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Unlock className="h-4 w-4 text-green-600" />
            <span className="font-medium text-green-800">Emergency Access Granted</span>
          </div>
          <p className="text-sm text-green-700">
            You can now view the information. You cannot edit it.
          </p>
        </div>
      );
    }

    if (userAccess.emergencyAccessDenied) {
      return (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Lock className="h-4 w-4 text-red-600" />
            <span className="font-medium text-red-800">Access Denied</span>
          </div>
          <p className="text-sm text-red-700">
            Emergency access was denied by the owner.
          </p>
        </div>
      );
    }

    if (userAccess.emergencyAccessRequested) {
      return (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Lock className="h-4 w-4 text-yellow-600" />
            <span className="font-medium text-yellow-800">Access Requested</span>
          </div>
          <p className="text-sm text-yellow-700">
            Emergency access requested. Waiting for owner approval or auto-grant (24 hours).
          </p>
        </div>
      );
    }

    // Show request button for invited/keyholder users with spare_key who have not requested/granted/denied
    if (
      !userAccess.isOwner &&
      userAccess.subscriptionType === 'spare_key' &&
      !userAccess.emergencyAccessRequested &&
      !userAccess.emergencyAccessGranted &&
      !userAccess.emergencyAccessDenied
    ) {
      return (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Lock className="h-4 w-4 text-blue-600" />
            <span className="font-medium text-blue-800">Access Required</span>
          </div>
          <p className="text-sm text-blue-700">
            You need to request emergency access to view information.
          </p>
          <Button className="mt-2" onClick={handleRequestEmergencyAccess}>
            Request Emergency Access
          </Button>
        </div>
      );
    }

    return (
      <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center gap-2 mb-2">
          <Lock className="h-4 w-4 text-blue-600" />
          <span className="font-medium text-blue-800">Access Required</span>
        </div>
        <p className="text-sm text-blue-700">
          You need to request emergency access to view information.
        </p>
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {subscriptionInfo.icon}
          Subscription Status
        </CardTitle>
        <CardDescription>
          Your current access level and permissions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <Badge className={subscriptionInfo.color}>
              {subscriptionInfo.name}
            </Badge>
            <p className="text-sm text-gray-600 mt-1">
              {subscriptionInfo.description}
            </p>
          </div>
                     {showUpgradeButton && userAccess.subscriptionType !== 'all_access_key' && (
             <Button 
               variant="outline" 
               size="sm"
               onClick={() => {
                 window.location.href = '/subscription';
               }}
             >
               <ArrowUpRight className="h-3 w-3 mr-1" />
               Upgrade
             </Button>
           )}
        </div>

        <div className="space-y-2">
          <h4 className="font-medium text-sm">Features:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            {subscriptionInfo.features.map((feature, index) => (
              <li key={index} className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />
                {feature}
              </li>
            ))}
          </ul>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium text-sm">Access Details:</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div className="flex items-center justify-between">
              <span>Role:</span>
              <span className="font-medium">
                {userAccess.isOwner ? 'Owner' : userAccess.userRole || 'Key Holder'}
              </span>
            </div>
            {userAccess.userPermissions && userAccess.userPermissions.length > 0 && (
              <div className="flex items-center justify-between">
                <span>Permissions:</span>
                <span className="font-medium text-xs">
                  {userAccess.userPermissions.join(', ')}
                </span>
              </div>
            )}
            {userAccess.isInvited && (
              <div className="flex items-center justify-between">
                <span>Status:</span>
                <span className="font-medium text-green-600">Invited User</span>
              </div>
            )}
          </div>
        </div>

        {userAccess.subscriptionType === 'temporary_key' && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Key className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800">Allowed Categories</span>
            </div>
            <p className="text-sm text-blue-700">
              You can access: <strong>Home Instructions</strong> and <strong>Quick Start</strong>
            </p>
          </div>
        )}

        {renderEmergencyAccessStatus()}
      </CardContent>
    </Card>
  );
}; 