import mongoose from 'mongoose';

interface IUser extends mongoose.Document {
  _id: mongoose.Types.ObjectId;

  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  image?: string;
  phone?: string;
  address?: string;
  zipCode?: string;
  country?: string;
  googleId?: string;
  isEmailVerified?: boolean;
  emailVerificationToken?: string;
  emailVerificationExpire?: Date;
  resetPasswordToken?: string;
  resetPasswordExpire?: Date;
  updateMobileToken?: string;
  updateMobileExpire?: Date;
  newMobileNumber?: string;
  roleId?: mongoose.Types.ObjectId;
  ownerId?: mongoose.Types.ObjectId;
  subscriptionType?: 'temporary_key' | 'spare_key' | 'all_access_key';
  allowedCategoryId?: string;
  emergencyAccessRequested?: boolean;
  emergencyAccessRequestedAt?: Date | null;
  emergencyAccessGranted?: boolean;
  emergencyAccessGrantedAt?: Date | null;
  emergencyAccessDenied?: boolean;
  emergencyAccessDeniedAt?: Date | null;
  comparePassword(candidatePassword: string): Promise<boolean>;
  generateResetToken(): string;
  generateEmailVerificationToken(): string;
  generateMobileUpdateToken(newMobileNumber: string): string;
  externalUser?: boolean;
}