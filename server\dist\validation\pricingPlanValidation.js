"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validatePricingPlanUpdate = exports.validatePricingPlan = void 0;
const joi_1 = __importDefault(require("joi"));
const pricingPlanSchema = joi_1.default.object({
    type: joi_1.default.string()
        .valid('temporary_key', 'spare_key', 'all_access_key')
        .required()
        .messages({
        'any.only': 'Type must be one of: temporary_key, spare_key, all_access_key'
    }),
    price: joi_1.default.number()
        .min(0)
        .required()
        .messages({
        'number.min': 'Price must be 0 or greater'
    }),
    displayPrice: joi_1.default.string()
        .required()
        .min(1)
        .messages({
        'string.min': 'Display price cannot be empty'
    }),
    tagline: joi_1.default.string()
        .required()
        .min(1)
        .messages({
        'string.min': 'Tagline cannot be empty'
    }),
    features: joi_1.default.array()
        .items(joi_1.default.string().min(1))
        .min(1)
        .required()
        .messages({
        'array.min': 'At least one feature is required',
        'string.min': 'Features cannot be empty strings'
    }),
    duration: joi_1.default.number()
        .custom((value, helpers) => {
        // Duration must be -1 (infinite) or positive number
        if (value === -1 || value > 0) {
            return value;
        }
        return helpers.error('any.invalid');
    })
        .optional()
        .messages({
        'any.invalid': 'Duration must be -1 (infinite) or a positive number of months'
    }),
    categorylimit: joi_1.default.number()
        .custom((value, helpers) => {
        // Category limit must be -1 (unlimited) or positive number
        if (value === -1 || value > 0) {
            return value;
        }
        return helpers.error('any.invalid');
    })
        .optional()
        .messages({
        'any.invalid': 'Category limit must be -1 (unlimited) or a positive number'
    }),
    active: joi_1.default.boolean()
        .optional()
        .default(true)
});
const updatePricingPlanSchema = joi_1.default.object({
    type: joi_1.default.string()
        .valid('temporary_key', 'spare_key', 'all_access_key')
        .optional()
        .messages({
        'any.only': 'Type must be one of: temporary_key, spare_key, all_access_key'
    }),
    price: joi_1.default.number()
        .min(0)
        .optional()
        .messages({
        'number.min': 'Price must be 0 or greater'
    }),
    displayPrice: joi_1.default.string()
        .optional()
        .min(1)
        .messages({
        'string.min': 'Display price cannot be empty'
    }),
    tagline: joi_1.default.string()
        .optional()
        .min(1)
        .messages({
        'string.min': 'Tagline cannot be empty'
    }),
    features: joi_1.default.array()
        .items(joi_1.default.string().min(1))
        .min(1)
        .optional()
        .messages({
        'array.min': 'At least one feature is required',
        'string.min': 'Features cannot be empty strings'
    }),
    duration: joi_1.default.number()
        .custom((value, helpers) => {
        // Duration must be -1 (infinite) or positive number
        if (value === -1 || value > 0) {
            return value;
        }
        return helpers.error('any.invalid');
    })
        .optional()
        .messages({
        'any.invalid': 'Duration must be -1 (infinite) or a positive number of months'
    }),
    categorylimit: joi_1.default.number()
        .custom((value, helpers) => {
        // Category limit must be -1 (unlimited) or positive number
        if (value === -1 || value > 0) {
            return value;
        }
        return helpers.error('any.invalid');
    })
        .optional()
        .messages({
        'any.invalid': 'Category limit must be -1 (unlimited) or a positive number'
    }),
    active: joi_1.default.boolean()
        .optional()
});
const validatePricingPlan = (req, res, next) => {
    const { error } = pricingPlanSchema.validate(req.body);
    if (error) {
        res.status(400).json({
            message: 'Validation error',
            details: error.details.map(detail => detail.message)
        });
        return;
    }
    next();
};
exports.validatePricingPlan = validatePricingPlan;
const validatePricingPlanUpdate = (req, res, next) => {
    const { error } = updatePricingPlanSchema.validate(req.body);
    if (error) {
        res.status(400).json({
            message: 'Validation error',
            details: error.details.map(detail => detail.message)
        });
        return;
    }
    next();
};
exports.validatePricingPlanUpdate = validatePricingPlanUpdate;
//# sourceMappingURL=pricingPlanValidation.js.map