import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Field } from 'formik';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, Trash2 } from 'lucide-react';

// Question type definitions
export interface BaseQuestion {
  id: string;
  text: string;
  required: boolean;
  sectionId: string;
  order: number;
  dependsOn?: {
    questionId: string;
    value: string;
  };
}

export interface DisplayQuestion extends BaseQuestion {
  type: 'display';
}

export interface TextQuestion extends BaseQuestion {
  type: 'text';
  placeholder?: string;
  validationRules?: {
    minLength?: number;
    maxLength?: number;
  };
}

export interface BooleanQuestion extends BaseQuestion {
  type: 'boolean';
}

export interface DropdownQuestion extends BaseQuestion {
  type: 'dropdown';
  options: string[];
}

export interface TextareaQuestion extends BaseQuestion {
  type: 'textarea';
  placeholder?: string;
}

export interface ContactsQuestion extends BaseQuestion {
  type: 'contacts';
  isContacts: boolean;
}

export type Question = DisplayQuestion | TextQuestion | BooleanQuestion | DropdownQuestion | TextareaQuestion | ContactsQuestion;

// Custom form field components for Formik
export const TextareaField = ({ question }: { question: TextQuestion | TextareaQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Textarea
        id={question.id}
        placeholder={question.placeholder}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const TextField = ({ question }: { question: TextQuestion }) => {
  const [field, meta] = useField(question.id);
  
  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium" htmlFor={question.id}>
        {question.text}
      </Label>
      <Input
        id={question.id}
        type={question.id.includes('password') || question.id.includes('Password') ? 'password' : 'text'}
        placeholder={question.placeholder}
        {...field}
        className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export const BooleanField = ({ question }: { question: BooleanQuestion }) => {
  const [field, meta, helpers] = useField(question.id);
  
  return (
    <div className="mb-6">
      <div className="flex flex-col">
        <Label className="font-medium mb-2" htmlFor={question.id}>
          {question.text}
        </Label>
        <div className="flex gap-2">
          <Button
            type="button"
            onClick={() => helpers.setValue('yes')}
            className={field.value === 'yes' 
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5]' 
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          >
            Yes
          </Button>
          <Button
            type="button"
            onClick={() => helpers.setValue('no')}
            className={field.value === 'no' 
              ? 'bg-[#2BCFD5] hover:bg-[#19bbb5]' 
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}
          >
            No
          </Button>
        </div>
        {meta.touched && meta.error ? (
          <div className="text-red-500 text-sm mt-1">{meta.error}</div>
        ) : null}
      </div>
    </div>
  );
};

export const DropdownField = ({ question }: { question: DropdownQuestion }) => {
  const [field, meta, helpers] = useField(question.id);

  return (
    <div className="mb-6">
      <Label className="block mb-2 font-medium">
        {question.text}
      </Label>
      <Select value={field.value || ''} onValueChange={(value) => helpers.setValue(value)}>
        <SelectTrigger className={`w-full ${meta.touched && meta.error ? 'border-red-500' : ''}`}>
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          {question.options.map((option) => (
            <SelectItem key={option} value={option}>
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

// Contact array field component similar to FriendsFamily pattern
export const ContactsField = ({ question }: { question: ContactsQuestion }) => {
  return (
    <div className="mb-6">
      <FieldArray name={question.id}>
        {({ push, remove, form }) => {
          // Ensure we have an array to work with
          const contacts = Array.isArray(form.values[question.id])
            ? form.values[question.id]
            : [];

          return (
            <div>
              <div className="mb-2 font-medium">{question.text}</div>
              {contacts.length === 0 && (
                <div className="text-gray-400 mb-2">No contacts added.</div>
              )}
              {contacts.map((contact: any, idx: number) => (
                <div key={idx} className="flex items-center gap-2 mb-2">
                  <Field
                    name={`${question.id}.${idx}.name`}
                    placeholder="Name"
                    className="border rounded px-2 py-1 flex-1"
                  />
                  <Field
                    name={`${question.id}.${idx}.phone`}
                    placeholder="Phone Number"
                    className="border rounded px-2 py-1 flex-1"
                  />
                  <button
                    type="button"
                    className="text-red-500 hover:text-red-700"
                    onClick={() => remove(idx)}
                    aria-label="Delete contact"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              ))}
              <button
                type="button"
                className="flex items-center gap-1 text-[#2BCFD5] hover:text-[#19bbb5] font-medium mt-2"
                onClick={() => push({ name: '', phone: '' })}
              >
                <Plus size={18} /> Add Contact
              </button>
            </div>
          );
        }}
      </FieldArray>
    </div>
  );
};

// Question item component that renders the appropriate field type
export const QuestionItem = ({
  question,
  questionRef
}: {
  question: Question;
  questionRef?: (el: HTMLDivElement | null) => void;
}) => {
  return (
    <div ref={questionRef} className="mb-4">
      {question.type === 'text' && <TextField question={question} />}
      {question.type === 'boolean' && <BooleanField question={question} />}
      {question.type === 'dropdown' && <DropdownField question={question} />}
      {question.type === 'textarea' && <TextareaField question={question} />}
      {question.type === 'contacts' && <ContactsField question={question} />}
      {question.type === 'display' && (
        <div className="mb-6">
          <div className="text-lg font-medium text-gray-800">{question.text}</div>
        </div>
      )}
    </div>
  );
};

// Utility functions for form handling
export const generateInitialValues = (questions: Question[]) => {
  const initialValues: Record<string, any> = {};
  questions.forEach(question => {
    if (question.type !== 'display') {
      if (question.type === 'contacts') {
        // Initialize contact arrays with one empty contact
        initialValues[question.id] = [{ name: '', phone: '' }];
      } else {
        initialValues[question.id] = '';
      }
    }
  });
  return initialValues;
};

export const buildValidationSchema = (questions: Question[], Yup: any) => {
  const schemaFields: Record<string, any> = {};

  questions.forEach(question => {
    if (question.type !== 'display') {
      if (question.type === 'contacts') {
        // Validation for contact arrays
        let validator = Yup.array().of(
          Yup.object().shape({
            name: Yup.string(),
            phone: Yup.string()
          })
        );

        if (question.required) {
          validator = validator.min(1, `${question.text} is required`);
        }

        schemaFields[question.id] = validator;
      } else {
        let validator = Yup.string();

        if (question.required) {
          validator = validator.required(`${question.text} is required`);
        }

        if (question.type === 'text' && question.validationRules) {
          if (question.validationRules.minLength) {
            validator = validator.min(question.validationRules.minLength,
              `Must be at least ${question.validationRules.minLength} characters`);
          }
          if (question.validationRules.maxLength) {
            validator = validator.max(question.validationRules.maxLength,
              `Must be no more than ${question.validationRules.maxLength} characters`);
          }
        }

        schemaFields[question.id] = validator;
      }
    }
  });

  return Yup.object().shape(schemaFields);
};

// Handle dependent questions logic
export const handleDependentAnswers = (values: Record<string, any>, questions: Question[]) => {
  const visibleQuestions: Question[] = [];

  questions.forEach(question => {
    if (!question.dependsOn) {
      visibleQuestions.push(question);
    } else {
      const dependentValue = values[question.dependsOn.questionId];
      if (dependentValue === question.dependsOn.value) {
        visibleQuestions.push(question);
      }
    }
  });

  return visibleQuestions;
};
