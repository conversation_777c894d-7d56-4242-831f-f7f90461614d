import { createSlice, createAsyncThunk, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { RootState } from '@/store';
import userInputService from '@/services/userInputService';
import socialMediaData from '@/data/socialMedia.json';

// Define types for our state
export interface SubCategory {
  id: string;
  title: string;
  questionsCount: number;
}

export interface GroupQuestion {
  id: string;
  text: string;
  type: 'group';
  required?: boolean;
  sectionId: string;
  order: number;
  dependsOn?: {
    questionId: string;
    value: string;
  };
  fields: Question[];
}

export type Question = {
  id: string;
  text: string;
  type: 'text' | 'textarea' | 'number' | 'boolean' | 'choice' | 'password' | 'dropdown';
  required: boolean;
  sectionId: string;
  order: number;
  options?: string[];
  dependsOn?: {
    questionId: string;
    value: string;
  };
  placeholder?: string;
  validationRules?: {
    minLength?: number;
    maxLength?: number;
  };
} | GroupQuestion;

export interface Answer {
  index: number;
  questionId?: string;
  originalQuestionId: string;
  question: string;
  type: string;
  answer: string;
}

export interface SectionAnswers {
  originalSectionId: string;
  isCompleted: boolean;
  answers: Answer[];
}

export interface UserInput {
  userId: string;
  categoryId?: string;
  originalCategoryId: string;
  subCategoryId?: string;
  originalSubCategoryId: string;
  answersBySection: SectionAnswers[];
  _id?: string;
}

interface SocialMediaState {
  subcategories: SubCategory[];
  questions: Record<string, Question[]>;
  userInputs: UserInput[];
  loading: boolean;
  error: string | null;
  progressStats: {
    totalQuestions: number;
    answeredQuestions: number;
    completionPercentage: number;
  };
}

// Define initial state
const initialState: SocialMediaState = {
  subcategories: [
    { id: '206A', title: 'Email', questionsCount: (socialMediaData['206'] || []).filter(q => q.sectionId === '206A').length },
    { id: '206B', title: 'Facebook', questionsCount: (socialMediaData['206'] || []).filter(q => q.sectionId === '206B').length },
    { id: '206C', title: 'Instagram', questionsCount: (socialMediaData['206'] || []).filter(q => q.sectionId === '206C').length },
    { id: '206D', title: 'Other Accounts', questionsCount: (socialMediaData['206'] || []).filter(q => q.sectionId === '206D').length },
    { id: '206E', title: 'Cell Phone', questionsCount: (socialMediaData['206'] || []).filter(q => q.sectionId === '206E').length }
  ],
  questions: Object.entries(socialMediaData).reduce((acc, [key, questions]) => ({
    ...acc,
    [key]: questions.map((q: any) => {
      if (q.type === 'group') {
        return {
          ...q,
          type: 'group',
          required: q.required ?? false,
          fields: q.fields || [],
        } as GroupQuestion;
      }
      return {
        ...q,
        type: q.type as 'text' | 'textarea' | 'number' | 'boolean' | 'choice' | 'password' | 'dropdown',
        required: q.required ?? false,
      };
    }) as Question[]
  }), {} as Record<string, Question[]>),
  userInputs: [],
  loading: false,
  error: null,
  progressStats: {
    totalQuestions: Object.values(socialMediaData).reduce(
      (sum, questions) => sum + questions.length, 0
    ),
    answeredQuestions: 0,
    completionPercentage: 0
  }
};

// Create async thunks
export const fetchUserInputs = createAsyncThunk<UserInput[], string>(
  'socialMedia/fetchUserInputs',
  async (userOrOwnerId: string, { rejectWithValue }) => {
    try {
      const response = await userInputService.getUserInputsByCategory(userOrOwnerId, '6', true);
      return response as UserInput[];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user inputs';
      return rejectWithValue(errorMessage);
    }
  }
);

export const saveUserInput = createAsyncThunk<UserInput, Omit<UserInput, '_id'>>(
  'socialMedia/saveUserInput',
  async (userData: Omit<UserInput, '_id'>, { rejectWithValue }) => {
    try {
      // Ensure originalCategoryId is '206' for social media
      const dataToSave = { ...userData, originalCategoryId: '6' };
      const response = await userInputService.saveUserInput(dataToSave);
      return response as UserInput;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to save user input';
      return rejectWithValue(errorMessage);
    }
  }
);

export const updateUserInput = createAsyncThunk<
  UserInput,
  { id: string, userData: Omit<UserInput, '_id'> }
>(
  'socialMedia/updateUserInput',
  async ({ id, userData }: { id: string, userData: Omit<UserInput, '_id'> }, { rejectWithValue }) => {
    try {
      // Ensure originalCategoryId is '206' for social media
      const dataToUpdate = { ...userData, originalCategoryId: '6' };
      const response = await userInputService.updateUserInput(id, dataToUpdate);
      return response as UserInput;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update user input';
      return rejectWithValue(errorMessage);
    }
  }
);

// Create slice
const socialMediaSlice = createSlice({
  name: 'socialMedia',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateProgressStats: (state) => {
      // Calculate total questions
      const totalQuestions = Object.values(state.questions).reduce(
        (sum, questions) => sum + questions.length, 0
      );

      // Calculate answered questions
      let answeredQuestions = 0;
      state.userInputs.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          answeredQuestions += section.answers.length;
        });
      });

      // Calculate completion percentage
      const completionPercentage = totalQuestions > 0
        ? Math.round((answeredQuestions / totalQuestions) * 100)
        : 0;

      state.progressStats = {
        totalQuestions,
        answeredQuestions,
        completionPercentage
      };
    }
  },
  extraReducers: (builder) => {
    // Handle fetchUserInputs
    builder.addCase(fetchUserInputs.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchUserInputs.fulfilled, (state, action: PayloadAction<UserInput[]>) => {
      state.loading = false;
      state.userInputs = action.payload;
      // Update progress stats after fetching user inputs
      const totalQuestions = state.progressStats.totalQuestions;
      let answeredQuestions = 0;

      action.payload.forEach(userInput => {
        userInput.answersBySection.forEach(section => {
          answeredQuestions += section.answers.length;
        });
      });

      const completionPercentage = totalQuestions > 0
        ? Math.round((answeredQuestions / totalQuestions) * 100)
        : 0;

      state.progressStats = {
        totalQuestions,
        answeredQuestions,
        completionPercentage
      };
    });
    builder.addCase(fetchUserInputs.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Handle saveUserInput
    builder.addCase(saveUserInput.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(saveUserInput.fulfilled, (state, action: PayloadAction<UserInput>) => {
      state.loading = false;
      const index = state.userInputs.findIndex(
        input => input.originalSubCategoryId === action.payload.originalSubCategoryId
      );
      if (index !== -1) {
        state.userInputs[index] = action.payload;
      } else {
        state.userInputs.push(action.payload);
      }
      // Update progress stats after saving
      state.progressStats.answeredQuestions += action.payload.answersBySection.reduce(
        (sum, section) => sum + section.answers.length, 0
      );
      state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
        ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
        : 0;
    });
    builder.addCase(saveUserInput.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });

    // Handle updateUserInput
    builder.addCase(updateUserInput.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(updateUserInput.fulfilled, (state, action: PayloadAction<UserInput>) => {
      state.loading = false;
      const index = state.userInputs.findIndex(input => input._id === action.payload._id);
      if (index !== -1) {
        state.userInputs[index] = action.payload;
      }
      // Recalculate progress stats
      state.progressStats.answeredQuestions = state.userInputs.reduce(
        (sum, input) => sum + input.answersBySection.reduce(
          (sectionSum, section) => sectionSum + section.answers.length, 0
        ), 0
      );
      state.progressStats.completionPercentage = state.progressStats.totalQuestions > 0
        ? Math.round((state.progressStats.answeredQuestions / state.progressStats.totalQuestions) * 100)
        : 0;
    });
    builder.addCase(updateUserInput.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
  }
});

export const { clearError, updateProgressStats } = socialMediaSlice.actions;

// Basic selector
export const selectSocialMediaState = (state: RootState) => state.socialMedia;

// Memoized selectors
export const selectSubcategories = createSelector(
  [selectSocialMediaState],
  (socialMedia) => socialMedia.subcategories
);

export const selectQuestions = createSelector(
  [selectSocialMediaState],
  (socialMedia) => socialMedia.questions
);

export const selectUserInputs = createSelector(
  [selectSocialMediaState],
  (socialMedia) => socialMedia.userInputs
);

export const selectLoading = createSelector(
  [selectSocialMediaState],
  (socialMedia) => socialMedia.loading
);

export const selectError = createSelector(
  [selectSocialMediaState],
  (socialMedia) => socialMedia.error
);

export const selectProgressStats = createSelector(
  [selectSocialMediaState],
  (socialMedia) => socialMedia.progressStats
);

// Memoized selectors with parameters
export const selectSubcategoryById = (subcategoryId: string) =>
  createSelector(
    [selectSubcategories],
    (subcategories) => subcategories.find(subcategory => subcategory.id === subcategoryId)
  );

export const selectQuestionsBySubcategoryId = (subcategoryId: string) =>
  createSelector(
    [selectQuestions],
    (questions) => questions[subcategoryId] || []
  );

export const selectUserInputsBySubcategoryId = (subcategoryId: string) =>
  createSelector(
    [selectUserInputs],
    (userInputs) => userInputs.filter(input => input.originalSubCategoryId === subcategoryId)
  );

export default socialMediaSlice.reducer;
