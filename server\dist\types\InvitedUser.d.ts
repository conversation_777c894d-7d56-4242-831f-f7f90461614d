import mongoose from 'mongoose';
export declare enum InvitationStatus {
    PENDING = "pending",
    ACCEPTED = "accepted",
    REJECTED = "rejected"
}
export interface IInvitedUser extends mongoose.Document {
    _id: mongoose.Types.ObjectId;
    ownerId: mongoose.Types.ObjectId;
    invitedUserId: mongoose.Types.ObjectId;
    status: InvitationStatus;
    relation: string;
    invitationToken?: string;
    invitationTokenExpire?: Date;
    createdAt: Date;
    updatedAt: Date;
    owner?: any;
    invitedUser?: any;
    generateInvitationToken(): string;
}
export interface IInvitedUserModel extends mongoose.Model<IInvitedUser> {
    findByOwnerId(ownerId: mongoose.Types.ObjectId): Promise<IInvitedUser[]>;
    findByUserId(userId: mongoose.Types.ObjectId): Promise<IInvitedUser[]>;
    findPendingInvitations(ownerId?: mongoose.Types.ObjectId): Promise<IInvitedUser[]>;
}
export interface ICreateInvitedUserData {
    ownerId: mongoose.Types.ObjectId;
    invitedUserId: mongoose.Types.ObjectId;
    relation: string;
    status?: InvitationStatus;
    invitationToken?: string;
    invitationTokenExpire?: Date;
}
export interface IInviteUserData {
    name: string;
    email: string;
    relation: string;
    phone?: string;
}
export interface IUpdateInvitedUserData {
    status?: InvitationStatus;
}
