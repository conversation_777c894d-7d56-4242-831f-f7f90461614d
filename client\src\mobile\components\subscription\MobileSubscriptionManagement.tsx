import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  Calendar, 
  Settings, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  ExternalLink,
  ArrowLeft
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import stripeService from '@/services/stripeService';
import subscriptionService from '@/services/subscriptionService';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Subscription, PricingPlan } from '@/types/subscription';
import { useNavigate } from 'react-router-dom';

interface MobileSubscriptionManagementProps {
  className?: string;
}

export default function MobileSubscriptionManagement({ className = "" }: MobileSubscriptionManagementProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isManaging, setIsManaging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [plans, setPlans] = useState<PricingPlan[]>([]);

  useEffect(() => {
    const fetchSubscription = async () => {
      if (!user?.id) {
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (ownerId) {
          const sub = await subscriptionService.getOwnerSubscription(ownerId);
          setSubscription(sub);
        }
        // Fetch all plans
        const allPlans = await subscriptionService.getPricingPlans();
        setPlans(allPlans);
      } catch (err) {
        console.error('Error fetching subscription:', err);
        setError('Failed to load subscription details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubscription();
  }, [user]);

  const handleManageSubscription = async () => {
    if (!subscription) return;

    setIsManaging(true);
    try {
      // This would need the Stripe customer ID from your subscription
      // For now, we'll show a placeholder
      toast({
        title: "Subscription Management",
        description: "Redirecting to Stripe customer portal...",
      });
      
      // In a real implementation, you'd get the customer ID from your subscription
      // await stripeService.redirectToCustomerPortal(customerId);
      
      // For now, just show a message
      toast({
        title: "Feature Coming Soon",
        description: "Subscription management portal will be available soon!",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to open subscription management",
        variant: "destructive",
      });
    } finally {
      setIsManaging(false);
    }
  };

  const getStatusBadge = (subscription: Subscription) => {
    if (!subscription.expiryAt) {
      return <Badge variant="secondary">Unknown</Badge>;
    }

    const now = new Date();
    const expiry = new Date(subscription.expiryAt);
    const isActive = expiry > now;

    if (isActive) {
      const daysRemaining = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysRemaining <= 7) {
        return <Badge variant="destructive">Expires Soon</Badge>;
      } else if (daysRemaining <= 30) {
        return <Badge variant="outline">Expires Soon</Badge>;
      } else {
        return <Badge variant="default">Active</Badge>;
      }
    } else {
      return <Badge variant="destructive">Expired</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysRemaining = (expiryAt: string) => {
    const now = new Date();
    const expiry = new Date(expiryAt);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-[#2BCFD5] mx-auto mb-4" />
          <p className="text-gray-600">Loading subscription...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => navigate('/dashboard')} variant="outline">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="min-h-screen bg-white">
        {/* Header */}
        <div className="bg-[#1F4168] text-white px-4 py-6">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/dashboard')}
              className="text-white hover:bg-white/10 p-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-semibold">Subscription</h1>
          </div>
        </div>

        <div className="p-4">
          <Card className="border-0 shadow-none">
            <CardContent className="text-center py-12">
              <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2 text-[#1F4168]">No Active Subscription</h2>
              <p className="text-gray-600 mb-6">You don't have an active subscription.</p>
              <Button 
                onClick={() => navigate('/auth/subscribe')}
                className="bg-[#2BCFD5] hover:bg-[#1F4168]"
              >
                View Plans
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-[#1F4168] text-white px-4 py-6">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/dashboard')}
            className="text-white hover:bg-white/10 p-2"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-lg font-semibold">Subscription Details</h1>
        </div>
      </div>

      <div className="p-4">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-[#2BCFD5]" />
                <CardTitle className="text-lg">Subscription Details</CardTitle>
              </div>
              {getStatusBadge(subscription)}
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Subscription Info */}
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-600" />
                  <span className="text-sm text-gray-600">Created</span>
                </div>
                <p className="font-medium text-sm">{formatDate(subscription.createdAt)}</p>
              </div>
              
              {subscription.expiryAt && (
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-gray-600" />
                    <span className="text-sm text-gray-600">Expires</span>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">{formatDate(subscription.expiryAt)}</p>
                    <p className="text-xs text-gray-500">
                      {getDaysRemaining(subscription.expiryAt)} days remaining
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Plan Details */}
            {subscription.planId && (
              <div className="bg-[#F0F9FF] rounded-lg p-4 border border-[#D1E9F7]">
                <h4 className="font-medium mb-2 text-[#1F4168]">Current Plan</h4>
                <p className="text-sm text-gray-600">
                  Plan: {plans.find(plan => plan._id === subscription.planId)?.type.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase()) || 'Unknown'}
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3 pt-4">
              <Button
                onClick={handleManageSubscription}
                disabled={isManaging}
                className="w-full py-3 bg-[#2BCFD5] hover:bg-[#1F4168]"
              >
                {isManaging ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <Settings className="mr-2 h-4 w-4" />
                    Manage Subscription
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={() => navigate('/auth/subscribe')}
                className="w-full py-3"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Upgrade Plan
              </Button>
            </div>

            {/* Warning for expiring subscriptions */}
            {subscription.expiryAt && getDaysRemaining(subscription.expiryAt) <= 30 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800 mb-1">Subscription Expiring Soon</h4>
                    <p className="text-sm text-yellow-700">
                      Your subscription will expire in {getDaysRemaining(subscription.expiryAt)} days. 
                      Consider renewing to maintain uninterrupted access.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 