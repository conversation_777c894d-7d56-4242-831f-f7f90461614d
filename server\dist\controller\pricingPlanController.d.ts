import { Request, Response } from 'express';
export declare const createPricingPlan: (req: Request, res: Response) => Promise<void>;
export declare const getAllPricingPlans: (req: Request, res: Response) => Promise<void>;
export declare const getPricingPlanById: (req: Request, res: Response) => Promise<void>;
export declare const getPricingPlanByType: (req: Request, res: Response) => Promise<void>;
export declare const updatePricingPlan: (req: Request, res: Response) => Promise<void>;
export declare const deletePricingPlan: (req: Request, res: Response) => Promise<void>;
export declare const updateAllPlansWithDuration: (_req: Request, res: Response) => Promise<void>;
export declare const updateAllPlansWithCategoryLimits: (_req: Request, res: Response) => Promise<void>;
export declare const initializeDefaultPricingPlans: (_req: Request, res: Response) => Promise<void>;
