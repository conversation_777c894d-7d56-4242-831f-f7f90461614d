import React from 'react';
import { Field, useField, FieldArray } from 'formik';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

export interface Question {
  id: string;
  text: string;
  type: 'text' | 'choice' | 'boolean' | 'number' | 'display' | 'dropdown' | 'textarea' | 'contacts';
  required: boolean;
  sectionId: string;
  options?: string[];
  placeholder?: string;
  validationRules?: {
    maxLength?: number;
  };
  order: number;
  dependsOn?: {
    questionId: string;
    value: string;
  };
  isContacts?: boolean;
}

export const buildValidationSchema = (questions: Question[], Yup: any) => {
  const schema: Record<string, any> = {};

  questions.forEach(question => {
    // Skip validation for display type questions
    if (question.type === 'display') return;

    let fieldSchema;

    switch (question.type) {
      case 'text':
      case 'textarea':
        fieldSchema = Yup.string();
        break;
      case 'choice':
      case 'dropdown':
        fieldSchema = Yup.string();
        break;
      case 'boolean':
        fieldSchema = Yup.string();
        break;
      case 'number':
        fieldSchema = Yup.string().matches(
          /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,
          'Invalid phone number'
        );
        break;
      case 'contacts':
        fieldSchema = Yup.array().of(
          Yup.object().shape({
            name: Yup.string(),
            phone: Yup.string(),
          })
        );
        break;
      default:
        fieldSchema = Yup.string();
    }

    if ((question.type === 'text' || question.type === 'textarea') && question.validationRules?.maxLength) {
      fieldSchema = fieldSchema.max(
        question.validationRules.maxLength,
        `Maximum ${question.validationRules.maxLength} characters allowed`
      );
    }

    schema[question.id] = fieldSchema;
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: Question[]) => {
  const values: Record<string, any> = {};
  questions.forEach(question => {
    // Skip initial values for display type questions
    if (question.type === 'display') return;
    
    if (question.type === 'contacts') {
      values[question.id] = [{ name: '', phone: '' }];
    } else {
      values[question.id] = '';
    }
  });
  return values;
};

export const calculateProgress = (questions: Question[], values: Record<string, any>) => {
  // Filter out display type questions and handle dependent questions
  const relevantQuestions = questions.filter(q => {
    // Exclude display type questions
    if (q.type === 'display') return false;

    // For dependent questions, check if they should be included based on the parent's value
    if (q.dependsOn) {
      const parentValue = values[q.dependsOn.questionId];
      // Only include if parent question is answered and matches the required value
      return parentValue === q.dependsOn.value;
    }

    // Include all non-dependent, non-display questions
    return true;
  });

  const totalQuestions = relevantQuestions.length;
  const answeredQuestions = relevantQuestions.filter(question => {
    const value = values[question.id];

    if (question.type === 'contacts') {
      const contacts = Array.isArray(value) ? value : [];
      return contacts.some((contact: any) => contact.name?.trim() || contact.phone?.trim());
    }

    if (question.type === 'boolean') {
      return value === 'yes' || value === 'no';
    }

    return value !== '' && value !== undefined && value !== null;
  }).length;

  const completionPercentage = totalQuestions > 0
    ? Math.round((answeredQuestions / totalQuestions) * 100)
    : 0;

  return {
    totalQuestions,
    answeredQuestions,
    completionPercentage
  };
};

interface QuestionItemProps {
  question: Question;
  values: Record<string, any>;
}

export const QuestionItem: React.FC<QuestionItemProps> = ({ question, values }) => {
  // Note: Following web pattern - no dependency filtering, show all questions

  return (
    <div className="mb-6">
      <label className={`block text-sm font-medium text-gray-700 mb-2 ${question.type === 'display' ? 'text-lg' : ''}`}>
        {question.text}
      </label>

      {question.type === 'display' && null}

      {(question.type === 'text' || question.type === 'textarea') && (
        <Field
          as={question.type === 'textarea' ? 'textarea' : 'input'}
          name={question.id}
          placeholder={question.placeholder}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
          rows={question.type === 'textarea' ? 3 : undefined}
        />
      )}

      {question.type === 'number' && (
        <Field
          type="tel"
          name={question.id}
          placeholder={question.placeholder || "Enter phone number"}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
        />
      )}

      {(question.type === 'choice' || question.type === 'dropdown') && question.options && (
        <div className="space-y-2">
          {question.options.map(option => (
            <label key={option} className="flex items-center space-x-2">
              <Field
                type="radio"
                name={question.id}
                value={option}
                className="h-4 w-4 text-[#2BCFD5]"
              />
              <span className="text-sm text-gray-700">{option}</span>
            </label>
          ))}
        </div>
      )}

      {question.type === 'boolean' && (
        <div className="flex gap-2">
          {['Yes', 'No'].map(option => {
            const isSelected = values[question.id] === option.toLowerCase();
            return (
              <label
                key={option}
                className={`flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer transition-colors duration-150 ${
                  isSelected
                    ? 'bg-[#2BCFD5] text-white border-[#2BCFD5]'
                    : 'bg-gray-100 hover:bg-[#25b6bb] hover:text-white'
                }`}
              >
                <Field
                  type="radio"
                  name={question.id}
                  value={option.toLowerCase()}
                  className="hidden"
                />
                {option}
              </label>
            );
          })}
        </div>
      )}

      {question.type === 'contacts' && (
        <FieldArray name={question.id}>
          {({ push, remove }) => (
            <div>
              {Array.isArray(values[question.id]) && values[question.id].map((contact: any, idx: number) => (
                <div key={idx} className="flex gap-2 mb-2">
                  <div className="flex-1">
                    <Field
                      name={`${question.id}.${idx}.name`}
                      placeholder="Name"
                      className="w-full border rounded p-2"
                    />
                  </div>
                  <div className="relative flex-1">
                    <Field
                      name={`${question.id}.${idx}.phone`}
                      placeholder="Phone"
                      className="w-full border rounded p-2 pr-8"
                    />
                    <button
                      type="button"
                      onClick={() => remove(idx)}
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 text-red-400 hover:text-red-600"
                      aria-label="Delete contact"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3m5 0H6" />
                      </svg>
                    </button>
                  </div>
                </div>
              ))}
              <button 
                type="button" 
                onClick={() => push({ name: "", phone: "" })} 
                className="text-[#2BCFD5] mt-2 flex items-center gap-1"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Add Contact
              </button>
            </div>
          )}
        </FieldArray>
      )}
    </div>
  );
};

export const NumberField = ({ question }: { question: Question }) => {
  const [field, meta, helpers] = useField(question.id);

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor={question.id}>
        {question.text}
      </label>
      <PhoneInput
        country={'us'}
        value={field.value}
        onChange={value => helpers.setValue(value)}
        inputProps={{
          name: question.id,
          id: question.id,
          required: question.required,
        }}
        containerClass={`${meta.touched && meta.error ? 'border-red-500' : ''}`}
        inputClass="w-full border rounded-lg px-3 py-2"
        buttonClass="h-9"
        containerStyle={{ height: '36px', width: '100%', display: 'flex', alignItems: 'center' }}
        placeholder={question.placeholder || 'Enter phone number'}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};
