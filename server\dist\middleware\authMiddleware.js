"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ensureAuth = exports.authMiddleware = exports.combinedAuth = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const User_1 = __importDefault(require("../models/User"));
const mongoose_1 = __importDefault(require("mongoose"));
// Combined middleware that handles both JWT and Passport authentication
const combinedAuth = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        // First check if user is authenticated via Passport
        if (req.isAuthenticated()) {
            console.log('User authenticated via Passport session');
            next();
            return;
        }
        // If not authenticated via Passport, check JWT token
        const token = (_a = req.header('Authorization')) === null || _a === void 0 ? void 0 : _a.split(' ')[1];
        if (!token) {
            console.log('No token provided in Authorization header');
            // Check if userId is provided in the request body as a fallback
            if (req.body && req.body.userId) {
                console.log('Using userId from request body:', req.body.userId);
                try {
                    const user = yield User_1.default.findById(req.body.userId);
                    if (user) {
                        console.log('User found by ID from request body');
                        req.user = user;
                        next();
                        return;
                    }
                }
                catch (err) {
                    console.error('Error finding user by ID from request body:', err);
                }
            }
            res.status(401).json({ message: 'Access Denied - No token provided' });
            return;
        }
        try {
            // Try to decode the token
            let decoded;
            try {
                // First try to decode as { userId: string }
                decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
                console.log('Token decoded with userId format');
            }
            catch (e) {
                // If that fails, try to decode as { id: string }
                try {
                    decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
                    console.log('Token decoded with id format');
                }
                catch (e2) {
                    throw new Error('Invalid token format');
                }
            }
            // Fix the decoded token property access
            const userId = 'userId' in decoded ? decoded.userId : 'id' in decoded ? decoded.id : undefined;
            if (!userId) {
                res.status(401).json({ message: 'Invalid token format' });
                return;
            }
            console.log('Looking up user with ID:', userId);
            const user = yield User_1.default.findById(userId);
            if (!user) {
                console.log('User not found with ID:', userId);
                res.status(401).json({ message: 'User not found' });
                return;
            }
            console.log('User authenticated via JWT token');
            // Attach user to request
            req.user = user;
            next();
        }
        catch (error) {
            console.error('Token verification error:', error);
            res.status(401).json({ message: 'Invalid token' });
            return;
        }
    }
    catch (error) {
        res.status(500).json({ message: 'Authentication error' });
        return;
    }
});
exports.combinedAuth = combinedAuth;
// Keep the original middleware for specific use cases
const authMiddleware = (req, res, next) => {
    var _a;
    try {
        const token = (_a = req.header('Authorization')) === null || _a === void 0 ? void 0 : _a.split(' ')[1];
        if (!token) {
            res.status(401).json({ message: 'Access Denied' });
            return;
        }
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
        req.user = { _id: new mongoose_1.default.Types.ObjectId(decoded.userId) };
        next();
    }
    catch (error) {
        res.status(401).json({ message: 'Invalid Token' });
    }
};
exports.authMiddleware = authMiddleware;
const ensureAuth = (req, res, next) => {
    if (req.isAuthenticated()) {
        next();
        return;
    }
    res.status(401).json({ status: 'fail', message: 'You are not logged in' });
};
exports.ensureAuth = ensureAuth;
//# sourceMappingURL=authMiddleware.js.map