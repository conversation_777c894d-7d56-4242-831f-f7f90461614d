import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { Request } from 'express';


const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadsDir = path.join(__dirname, '../../uploads');
    // Ensure uploads directory exists
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    // Create unique filename with timestamp and random string
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 1 // Only allow 1 file at a time
  },
  fileFilter: (req, file, cb) => {
    checkFileType(file, cb);
  }
});

// Error handling middleware for multer
const handleMulterError = (error: any, req: any, res: any, next: any) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        message: 'Upload image less than 5MB',
        error: 'FILE_TOO_LARGE'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        message: 'Only one file allowed',
        error: 'TOO_MANY_FILES'
      });
    }
    return res.status(400).json({
      message: 'File upload error',
      error: error.code
    });
  }

  if (error.name === 'INVALID_FILE_TYPE') {
    return res.status(400).json({
      message: error.message,
      error: 'INVALID_FILE_TYPE'
    });
  }

  next(error);
};


function checkFileType(file: Express.Multer.File, cb: multer.FileFilterCallback) {
  // Allowed file types
  const filetypes = /jpeg|jpg|png|gif|webp/;
  // Allowed MIME types
  const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedMimeTypes.includes(file.mimetype.toLowerCase());

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    const error = new Error('Only image files are allowed! (JPEG, JPG, PNG, GIF, WebP)');
    error.name = 'INVALID_FILE_TYPE';
    cb(error);
  }
}

export default upload;
export { handleMulterError };
