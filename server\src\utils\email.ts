const nodemailer = require("nodemailer");
import { generateEmailVerificationTemplate, generatePasswordResetTemplate, generatePasswordResetSuccessTemplate, generateEmergencyAccessRequestTemplate, generateEmergencyAccessApprovalTemplate, generateEmergencyAccessRejectionTemplate, generateEmergencyAccessAutoGrantTemplate } from './emailTemplates';

// Helper function to extract reset URL from email text
const extractResetUrl = (text: string): string => {
  const urlMatch = text.match(/http:\/\/[^\s]+/);
  return urlMatch ? urlMatch[0] : 'URL not found';
};

const sendMail = async (options: any) => {
  const transporter = nodemailer.createTransport({
    service: "gmail",
    host: "smtp.gmail.com",
    port: 465,
    secure: true, // true for 465, false for other ports
    auth: {
      user: process.env.STEP_GMAIL,
      pass: process.env.STEP_PASSWORD,
    },
    tls: {
      rejectUnauthorized: false
    }
  });

  const emailOptions = {
    from: `<PERSON><PERSON><PERSON> support<${process.env.STEP_GMAIL}>`,
    to: options.to,
    subject: options.subject,
    text: options.text,
    html: options.html
  };

  try {
    const result = await transporter.sendMail(emailOptions);
    console.log('Email sent successfully:', result.messageId);
    return result;
  } catch (error: any) {
    console.error('Email sending failed:', error);

    // Check if it's a rate limit error
    if (error.responseCode === 550 && error.response?.includes('Daily user sending limit exceeded')) {
      console.log('Gmail daily sending limit exceeded. Using debug mode...');

      // In development, log the email content for debugging
      // if (process.env.NODE_ENV === 'development') {
      //   console.log('📧 EMAIL DEBUG MODE:');
      //   console.log('To:', emailOptions.to);
      //   console.log('Subject:', emailOptions.subject);
      //   console.log('Text:', emailOptions.text);
      //   console.log('========================');
      // }

      return {
        messageId: 'debug-mode-' + Date.now(),
        debugMode: true,
        error: 'Daily sending limit exceeded'
      };
    }

    // For other errors, still return a fallback
    return {
      messageId: 'fallback-mode-' + Date.now(),
      error: error.message
    };
  }
};

// Send email verification OTP
export const sendEmailVerificationOTP = async (email: string, username: string, otp: string) => {
  try {
    const emailTemplate = generateEmailVerificationTemplate(username, otp);

    const emailOptions = {
      from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
      to: email,
      subject: emailTemplate.subject,
      text: emailTemplate.text,
      html: emailTemplate.html
    };

    const result = await sendMail(emailOptions);
    // console.log('Email verification OTP sent successfully:', result.messageId);
    return result;
  } catch (error) {
    // console.error('Failed to send email verification OTP:', error);
    throw error;
  }
};

// Send password reset email
export const sendPasswordResetEmail = async (email: string, username: string, resetUrl: string) => {
  try {
    const emailTemplate = generatePasswordResetTemplate(username, resetUrl);

    const emailOptions = {
      from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
      to: email,
      subject: emailTemplate.subject,
      text: emailTemplate.text,
      html: emailTemplate.html
    };

    const result = await sendMail(emailOptions);
    // console.log('Password reset email sent successfully:', result.messageId);
    return result;
  } catch (error) {
    // console.error('Failed to send password reset email:', error);
    throw error;
  }
};

// Send password reset success email
export const sendPasswordResetSuccessEmail = async (email: string, username: string) => {
  try {
    const emailTemplate = generatePasswordResetSuccessTemplate(username);

    const emailOptions = {
      from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
      to: email,
      subject: emailTemplate.subject,
      text: emailTemplate.text,
      html: emailTemplate.html
    };

    const result = await sendMail(emailOptions);
    // console.log('Password reset success email sent successfully:', result.messageId);
    return result;
  } catch (error) {
    // console.error('Failed to send password reset success email:', error);
    throw error;
  }
};

// Send emergency access request email to owner
export const sendEmergencyAccessRequestEmail = async (ownerEmail: string, ownerName: string, keyHolderName: string) => {
  try {
    const emailTemplate = generateEmergencyAccessRequestTemplate(ownerName, keyHolderName);
    const emailOptions = {
      from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
      to: ownerEmail,
      subject: emailTemplate.subject,
      text: emailTemplate.text,
      html: emailTemplate.html
    };
    const result = await sendMail(emailOptions);
    return result;
  } catch (error) {
    throw error;
  }
};

// Send emergency access approval email to key holder
export const sendEmergencyAccessApprovalEmail = async (keyHolderEmail: string, keyHolderName: string, ownerName: string) => {
  try {
    const emailTemplate = generateEmergencyAccessApprovalTemplate(keyHolderName, ownerName);
    const emailOptions = {
      from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
      to: keyHolderEmail,
      subject: emailTemplate.subject,
      text: emailTemplate.text,
      html: emailTemplate.html
    };
    const result = await sendMail(emailOptions);
    return result;
  } catch (error) {
    throw error;
  }
};

// Send emergency access rejection email to key holder
export const sendEmergencyAccessRejectionEmail = async (keyHolderEmail: string, keyHolderName: string, ownerName: string) => {
  try {
    const emailTemplate = generateEmergencyAccessRejectionTemplate(keyHolderName, ownerName);
    const emailOptions = {
      from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
      to: keyHolderEmail,
      subject: emailTemplate.subject,
      text: emailTemplate.text,
      html: emailTemplate.html
    };
    const result = await sendMail(emailOptions);
    return result;
  } catch (error) {
    throw error;
  }
};

// Send emergency access auto-grant email to key holder
export const sendEmergencyAccessAutoGrantEmail = async (keyHolderEmail: string, keyHolderName: string, ownerName: string) => {
  try {
    const emailTemplate = generateEmergencyAccessAutoGrantTemplate(keyHolderName, ownerName);
    const emailOptions = {
      from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
      to: keyHolderEmail,
      subject: emailTemplate.subject,
      text: emailTemplate.text,
      html: emailTemplate.html
    };
    const result = await sendMail(emailOptions);
    return result;
  } catch (error) {
    throw error;
  }
};

export default sendMail;