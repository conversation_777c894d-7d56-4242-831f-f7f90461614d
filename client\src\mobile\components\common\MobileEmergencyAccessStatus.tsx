import React from 'react';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CheckCircle, XCircle, Clock } from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';

interface MobileEmergencyAccessStatusProps {
  className?: string;
  showText?: boolean;
}

export const MobileEmergencyAccessStatus: React.FC<MobileEmergencyAccessStatusProps> = ({ 
  className = "", 
  showText = true 
}) => {
  const { userAccess, emergencyAccessStatus } = useAccessControl();

  if (!userAccess || userAccess.subscriptionType !== 'spare_key' || userAccess.isOwner) {
    return null;
  }

  const formatTimeRemaining = (milliseconds: number): string => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  if (userAccess.emergencyAccessGranted) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <CheckCircle className="h-4 w-4 text-green-600" />
        {showText && (
          <Badge className="bg-green-100 text-green-800 text-xs">
            Access Granted
          </Badge>
        )}
      </div>
    );
  }

  if (userAccess.emergencyAccessDenied) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <XCircle className="h-4 w-4 text-red-600" />
        {showText && (
          <Badge className="bg-red-100 text-red-800 text-xs">
            Access Denied
          </Badge>
        )}
      </div>
    );
  }

  if (userAccess.emergencyAccessRequested) {
    const timeRemaining = emergencyAccessStatus?.timeRemaining || 0;
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Clock className="h-4 w-4 text-yellow-600" />
        {showText && (
          <Badge className="bg-yellow-100 text-yellow-800 text-xs">
            Pending {timeRemaining > 0 && `(${formatTimeRemaining(timeRemaining)})`}
          </Badge>
        )}
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <AlertTriangle className="h-4 w-4 text-orange-500" />
      {showText && (
        <Badge className="bg-orange-100 text-orange-800 text-xs">
          Access Required
        </Badge>
      )}
    </div>
  );
}; 