import { useEffect, useState, useRef } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '@/store';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import GoodToKnowBox from '@/web/components/Global/GoodToKnowBox';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import SubCategoryFooterNav from '@/web/components/Global/SubCategoryFooterNav';
import {
  QuestionItem,
  buildValidationSchema,
  generateInitialValues,
  handleDependentAnswers,
  isQuestionVisible
} from '@/web/components/Category/WillInstructions/FormFields';
import ScrollToQuestion from '@/web/components/Category/WillInstructions/ScrollToQuestion';
import SubCategoryHeader from '@/web/components/Global/SubCategoryHeader';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import SubCategoryTitle from '@/web/components/Global/SubCategoryTitle';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  updateFormValues,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId,
  selectFormValues
} from '@/store/slices/willInstructionsSlice';
import { generateObjectId } from '@/services/userInputService';
import { Button } from '@/components/ui/button';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';

const LegalInstructions = () => {
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  // Extract the target question ID from URL query parameters
  const searchParams = new URLSearchParams(location.search);
  const targetQuestionId = searchParams.get('questionId');

  // Get questions and form values from Redux
  const questions = useAppSelector(selectQuestionsBySubcategoryId('105B'));
  const userInputs = useAppSelector(selectUserInputsBySubcategoryId('105B'));
  const formValues = useAppSelector(selectFormValues);

  const tabs = [
    { label: 'Location', path: '/category/willinstructions/location' },
    { label: 'Legal Representation', path: '/category/willinstructions/legal' }
  ];

  // Use a ref to track if we've already processed the user inputs
  const processedUserInputs = useRef(false);
  const existingInputIdRef = useRef<string | null>(null);
  const [ownerId, setOwnerId] = useState<string | null>(null);

  // Fetch user inputs only once when component mounts
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        const ownerId = await getCachedOwnerIdFromUser(user);
        setOwnerId(ownerId || user.id);
        dispatch(fetchUserInputs(ownerId || user.id));
      }
    };
    fetchData();
  }, [dispatch, user]);

  // Process user inputs only once when they're loaded
  useEffect(() => {
    if (
      userInputs &&
      userInputs.length > 0 &&
      userInputs[0]._id !== existingInputId
    ) {
      setExistingInputId(userInputs[0]._id || null);
      existingInputIdRef.current = userInputs[0]._id || null;
      const combinedFormValues: Record<string, string> = {};
      userInputs.forEach(input => {
        input.answersBySection.forEach(section => {
          section.answers.forEach(answer => {
            if (answer.originalQuestionId) {
              combinedFormValues[answer.originalQuestionId] = answer.answer;
            }
          });
        });
      });
      dispatch(updateFormValues(combinedFormValues));
    }
  }, [userInputs]);

  if (questions.length === 0) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      <SubCategoryHeader
        title="Will & Testament"
        backTo="/dashboard"
        user={{
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
          email: user?.email || '<EMAIL>',
          avatar: createUserInfo(user).avatar
        }}
      />
      <SubCategoryTabs tabs={tabs} />
      <div className="container mx-auto px-6">
        <SubCategoryTitle
          mainCategory="Will & Testament"
          category="Legal Representation"
          description="These files contain questions to help you record your details so they're easy to find later."
        />
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Formik
                initialValues={formValues || generateInitialValues(questions as any)}
                validationSchema={Yup.object(buildValidationSchema(questions as any, Yup))}
                enableReinitialize={true}
                onSubmit={async (values) => {
                  try {
                    dispatch(updateFormValues(values));
                    const formattedAnswersBySection = [
                      {
                        originalSectionId: '105B',
                        isCompleted: true,
                        answers: questions
                          .filter(q => q.sectionId === '105B' && isQuestionVisible(q as any, values) && values[q.id] && values[q.id].trim() !== '')
                          .map((q, index) => ({
                            index,
                            originalQuestionId: q.id,
                            question: q.text,
                            type: q.type,
                            answer: values[q.id]
                          }))
                      }
                    ];
                    const idToUpdate = existingInputIdRef.current || existingInputId;
                    if (idToUpdate) {
                      await dispatch(updateUserInput({
                        id: idToUpdate,
                        userData: {
                          userId: user?.id || 'guest',
                          categoryId: generateObjectId(),
                          originalCategoryId: '3',
                          subCategoryId: generateObjectId(),
                          originalSubCategoryId: '105B',
                          answersBySection: formattedAnswersBySection
                        } as any
                      }));
                    } else {
                      const userData = {
                        userId: user?.id || 'guest',
                        categoryId: generateObjectId(),
                        originalCategoryId: '3',
                        subCategoryId: generateObjectId(),
                        originalSubCategoryId: '105B',
                        answersBySection: formattedAnswersBySection
                      } as any;
                      const result = await dispatch(saveUserInput(userData));
                      const payload = result.payload as any;
                      if (payload && payload._id) {
                        setExistingInputId(payload._id);
                        existingInputIdRef.current = payload._id;
                      }
                    }
                    setTimeout(() => {
                      navigate('/category/willinstructions/review');
                    }, 100);
                  } catch (error) {
                    console.error('Error saving data:', error);
                  }
                }}
              >
                {({ values, isSubmitting, setValues }) => {
                  // Function to handle field changes and clear dependent fields
                  const handleFieldChange = (fieldId: string, value: string) => {
                    // Create a new values object with the updated field
                    const newValues = { ...values, [fieldId]: value };

                    // Process dependent answers - clear values of dependent questions if condition not met
                    const updatedValues = handleDependentAnswers(newValues, questions);

                    // Update form values
                    setValues(updatedValues);

                    // Update Redux store with the new values
                    dispatch(updateFormValues(updatedValues));
                  };

                  return (
                    <Form>
                      <ScrollToQuestion questions={questions}>
                        {(refs) => (
                        <>
                          {questions.map((question) => (
                            <div
                              key={question.id}
                              id={`question-${question.id}`}
                              ref={(el: HTMLDivElement | null) => {
                                refs[question.id] = el;
                              }}
                            >
                              <QuestionItem
                                question={question as any}
                                formValues={values}
                                onChange={(value: string) => handleFieldChange(question.id, value)}
                              />
                            </div>
                          ))}
                        </>
                      )}
                    </ScrollToQuestion>
                    <div className="mt-8 flex justify-center">
                          <Button
                            type="submit"
                            disabled={isSubmitting}
                            className="bg-[#2BCFD5] hover:bg-[#19bbb5] text-white px-6 py-2 rounded-lg font-semibold w-full"
                          >
                            {isSubmitting ? 'Saving...' : 'Save & Continue'}
                          </Button>
                    </div>
                    <GoodToKnowBox
                      title="Editing my Answers"
                      description="Each topic below is a part of your home documents, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you."
                    />
                    {/* <div className="mt-6">
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb] w-full"
                      >
                        {isSubmitting ? 'Saving...' : 'Save and Continue'}
                      </button>
                    </div> */}
                    <SubCategoryFooterNav
                      leftLabel="Location"
                      leftTo="/category/willinstructions/location"
                      rightLabel="Review"
                      rightTo="/category/willinstructions/review"
                    />
                  </Form>
                  );
                }}
              </Formik>
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default LegalInstructions;