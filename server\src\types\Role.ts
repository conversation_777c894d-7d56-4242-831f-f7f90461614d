import mongoose from 'mongoose';

export enum RoleType {
  OWNER = 'Owner',
  NOMINEE = 'Nominee', 
  FAMILY = 'Family'
}

export interface IRole extends mongoose.Document {
  _id: mongoose.Types.ObjectId;
  name: RoleType;
  description: string;
  permissions: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Instance method
  hasPermission(permission: string): boolean;
}

// Add this interface for static methods
export interface IRoleModel extends mongoose.Model<IRole> {
  getDefaultRoles(): Array<{
    name: RoleType;
    description: string;
    permissions: string[];
    isActive: boolean;
  }>;
}

export interface IRolePermissions {
  canViewAll: boolean;
  canEditAll: boolean;
  canDeleteAll: boolean;
  canCreateAll: boolean;
}

