import React from 'react';
import { Field, useField } from 'formik';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';


export interface Question {
  id: string;
  text: string;
  type: 'text' | 'choice' | 'boolean' | 'number' | 'display';
  required: boolean;
  sectionId: string;
  options?: string[];
  placeholder?: string;
  validationRules?: {
    maxLength?: number;
  };
  order: number;
  dependsOn?: {
    questionId: string;
    value: string;
  };
}

export const buildValidationSchema = (questions: Question[], Yup: any) => {
  const schema: Record<string, any> = {};

  questions.forEach(question => {
    // Skip validation for display type questions
    if (question.type === 'display') return;

    let fieldSchema;

    switch (question.type) {
      case 'text':
        fieldSchema = Yup.string();
        break;
      case 'choice':
        fieldSchema = Yup.string();
        break;
      case 'boolean':
        fieldSchema = Yup.string();
        break;
      case 'number':
        fieldSchema = Yup.string().matches(
          /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,
          'Invalid phone number'
        );
        break;
      default:
        fieldSchema = Yup.string();
    }

    if (question.type === 'text' && question.validationRules?.maxLength) {
      fieldSchema = fieldSchema.max(
        question.validationRules.maxLength,
        `Maximum ${question.validationRules.maxLength} characters allowed`
      );
    }

    schema[question.id] = fieldSchema;
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: Question[]) => {
  const values: Record<string, any> = {};
  questions.forEach(question => {
    // Skip initial values for display type questions
    if (question.type === 'display') return;
    values[question.id] = '';
  });
  return values;
};

export const calculateProgress = (questions: Question[], values: Record<string, any>) => {
  // Filter out display type questions and handle dependent questions
  const relevantQuestions = questions.filter(q => {
    // Exclude display type questions
    if (q.type === 'display') return false;

    // For dependent questions, check if they should be included based on the parent's value
    if (q.dependsOn) {
      const parentValue = values[q.dependsOn.questionId];
      // Only include if parent question is answered and matches the required value
      return parentValue === q.dependsOn.value;
    }

    // Include all non-dependent, non-display questions
    return true;
  });

  const totalQuestions = relevantQuestions.length;
  const answeredQuestions = Object.entries(values).filter(([id, value]) => {
    const question = relevantQuestions.find(q => q.id === id);
    // Only count answered questions that are relevant (non-display and properly dependent)
    return question && value !== '';
  }).length;

  const completionPercentage = totalQuestions > 0 
    ? Math.round((answeredQuestions / totalQuestions) * 100) 
    : 0;

  return {
    totalQuestions,
    answeredQuestions,
    completionPercentage
  };
};

interface QuestionItemProps {
  question: Question;
  values: Record<string, any>;
}

export const QuestionItem: React.FC<QuestionItemProps> = ({ question, values }) => {
  if (question.dependsOn) {
    const dependentValue = values[question.dependsOn.questionId];
    if (dependentValue !== question.dependsOn.value) {
      return null;
    }
  }

  return (
    <div className="mb-6">
      <label className={`block text-sm font-medium text-gray-700 mb-2 ${question.type === 'display' ? 'text-lg' : ''}`}>
        {question.text}
      </label>

      {question.type === 'display' && null}

      {question.type === 'text' && (
        <Field
          as="textarea"
          name={question.id}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
          rows={3}
        />
      )}

      {question.type === 'number' && (
        <Field
          type="tel"
          name={question.id}
          placeholder={question.placeholder || "Enter phone number"}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
        />
      )}

      {question.type === 'choice' && question.options && (
        <div className="space-y-2">
          {question.options.map(option => (
            <label key={option} className="flex items-center space-x-2">
              <Field
                type="radio"
                name={question.id}
                value={option}
                className="h-4 w-4 text-[#2BCFD5]"
              />
              <span className="text-sm text-gray-700">{option}</span>
            </label>
          ))}
        </div>
      )}

      {question.type === 'boolean' && (
        <div className="flex gap-2">
          {['Yes', 'No'].map(option => (
            <label
              key={option}
              className="flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer bg-gray-100 hover:bg-[#25b6bb] hover:text-white transition-colors duration-150"
            >
              <Field
                type="radio"
                name={question.id}
                value={option.toLowerCase()}
                className="hidden"
              />
              {option}
            </label>
          ))}
        </div>
      )}
    </div>
  );
};

export const NumberField = ({ question }: { question: Question }) => {
  const [field, meta, helpers] = useField(question.id);

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor={question.id}>
        {question.text}
      </label>
      <PhoneInput
        country={'us'}
        value={field.value}
        onChange={value => helpers.setValue(value)}
        inputProps={{
          name: question.id,
          id: question.id,
          required: question.required,
        }}
        containerClass={`${meta.touched && meta.error ? 'border-red-500' : ''}`}
        inputClass="w-full border rounded-lg px-3 py-2"
        buttonClass="h-9"
        containerStyle={{ height: '36px', width: '100%', display: 'flex', alignItems: 'center' }}
        placeholder={question.placeholder || 'Enter phone number'}
      />
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-sm mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};