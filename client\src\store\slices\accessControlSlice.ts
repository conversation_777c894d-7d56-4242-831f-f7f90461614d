import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { AccessControlState, UserAccessInfo, EmergencyAccessStatus, KeyHolder, SubscriptionType } from '@/types/accessControl';
import { accessControlService } from '@/services/accessControlService';

const initialState: AccessControlState = {
  userAccess: null,
  emergencyAccessStatus: null,
  keyHolders: [],
  loading: false,
  error: null,
  userAccessLoading: false,
  emergencyAccessLoading: false,
  keyHoldersLoading: false,
};

// Async thunks
export const fetchUserAccessInfo = createAsyncThunk(
  'accessControl/fetchUserAccessInfo',
  async () => {
    return await accessControlService.getUserAccessInfo();
  }
);

export const updateUserSubscription = createAsyncThunk(
  'accessControl/updateUserSubscription',
  async ({ subscriptionType, allowedCategoryId }: { subscriptionType: SubscriptionType; allowedCategoryId?: string }) => {
    return await accessControlService.updateUserSubscription(subscriptionType, allowedCategoryId);
  }
);

export const requestEmergencyAccess = createAsyncThunk(
  'accessControl/requestEmergencyAccess',
  async () => {
    return await accessControlService.requestEmergencyAccess();
  }
);

export const grantEmergencyAccess = createAsyncThunk(
  'accessControl/grantEmergencyAccess',
  async (keyHolderId: string) => {
    return await accessControlService.grantEmergencyAccess(keyHolderId);
  }
);

export const denyEmergencyAccess = createAsyncThunk(
  'accessControl/denyEmergencyAccess',
  async (keyHolderId: string) => {
    return await accessControlService.denyEmergencyAccess(keyHolderId);
  }
);

export const fetchEmergencyAccessStatus = createAsyncThunk(
  'accessControl/fetchEmergencyAccessStatus',
  async () => {
    return await accessControlService.getEmergencyAccessStatus();
  }
);

export const fetchKeyHoldersForOwner = createAsyncThunk(
  'accessControl/fetchKeyHoldersForOwner',
  async () => {
    return await accessControlService.getKeyHoldersForOwner();
  }
);

const accessControlSlice = createSlice({
  name: 'accessControl',
  initialState,
  reducers: {
    clearAccessControlError: (state) => {
      state.error = null;
    },
    resetAccessControl: (state) => {
      state.userAccess = null;
      state.emergencyAccessStatus = null;
      state.keyHolders = [];
      state.error = null;
      state.userAccessLoading = false;
      state.emergencyAccessLoading = false;
      state.keyHoldersLoading = false;
    },
    clearAccessControlOnLogout: (state) => {
      state.userAccess = null;
      state.emergencyAccessStatus = null;
      state.keyHolders = [];
      state.error = null;
      state.loading = false;
      state.userAccessLoading = false;
      state.emergencyAccessLoading = false;
      state.keyHoldersLoading = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch User Access Info
      .addCase(fetchUserAccessInfo.pending, (state) => {
        state.userAccessLoading = true;
        state.error = null;
      })
      .addCase(fetchUserAccessInfo.fulfilled, (state, action) => {
        state.userAccessLoading = false;
        state.userAccess = action.payload;
      })
      .addCase(fetchUserAccessInfo.rejected, (state, action) => {
        state.userAccessLoading = false;
        state.error = action.error.message || 'Failed to fetch user access info';
      })
      // Update User Subscription
      .addCase(updateUserSubscription.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserSubscription.fulfilled, (state, action) => {
        state.loading = false;
        if (state.userAccess) {
          state.userAccess.subscriptionType = action.payload.user.subscriptionType;
          state.userAccess.allowedCategoryId = action.payload.user.allowedCategoryId;
        }
      })
      .addCase(updateUserSubscription.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update user subscription';
      })
      // Request Emergency Access
      .addCase(requestEmergencyAccess.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(requestEmergencyAccess.fulfilled, (state) => {
        state.loading = false;
        if (state.userAccess) {
          state.userAccess.emergencyAccessRequested = true;
          state.userAccess.emergencyAccessRequestedAt = new Date().toISOString();
        }
      })
      .addCase(requestEmergencyAccess.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to request emergency access';
      })
      // Grant Emergency Access
      .addCase(grantEmergencyAccess.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(grantEmergencyAccess.fulfilled, (state, action) => {
        state.loading = false;
        // Update the key holder in the list
        const keyHolderId = action.meta.arg;
        const keyHolderIndex = state.keyHolders.findIndex(kh => kh._id === keyHolderId);
        if (keyHolderIndex !== -1) {
          state.keyHolders[keyHolderIndex].emergencyAccessGranted = true;
          state.keyHolders[keyHolderIndex].emergencyAccessGrantedAt = new Date().toISOString();
        }
      })
      .addCase(grantEmergencyAccess.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to grant emergency access';
      })
      // Deny Emergency Access
      .addCase(denyEmergencyAccess.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(denyEmergencyAccess.fulfilled, (state, action) => {
        state.loading = false;
        // Update the key holder in the list
        const keyHolderId = action.meta.arg;
        const keyHolderIndex = state.keyHolders.findIndex(kh => kh._id === keyHolderId);
        if (keyHolderIndex !== -1) {
          state.keyHolders[keyHolderIndex].emergencyAccessDenied = true;
          state.keyHolders[keyHolderIndex].emergencyAccessDeniedAt = new Date().toISOString();
        }
      })
      .addCase(denyEmergencyAccess.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to deny emergency access';
      })
      // Fetch Emergency Access Status
      .addCase(fetchEmergencyAccessStatus.pending, (state) => {
        state.emergencyAccessLoading = true;
        state.error = null;
      })
      .addCase(fetchEmergencyAccessStatus.fulfilled, (state, action) => {
        state.emergencyAccessLoading = false;
        state.emergencyAccessStatus = action.payload;
      })
      .addCase(fetchEmergencyAccessStatus.rejected, (state, action) => {
        state.emergencyAccessLoading = false;
        state.error = action.error.message || 'Failed to fetch emergency access status';
      })
      // Fetch Key Holders for Owner
      .addCase(fetchKeyHoldersForOwner.pending, (state) => {
        state.keyHoldersLoading = true;
        state.error = null;
      })
      .addCase(fetchKeyHoldersForOwner.fulfilled, (state, action) => {
        state.keyHoldersLoading = false;
        state.keyHolders = action.payload;
      })
      .addCase(fetchKeyHoldersForOwner.rejected, (state, action) => {
        state.keyHoldersLoading = false;
        state.error = action.error.message || 'Failed to fetch key holders';
      });
  },
});

export const { clearAccessControlError, resetAccessControl, clearAccessControlOnLogout } = accessControlSlice.actions;
export default accessControlSlice.reducer; 