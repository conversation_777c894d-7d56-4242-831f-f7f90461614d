import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik, Form, ErrorMessage } from "formik";
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import bankingData from '@/data/banking.json';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectUserInputsBySubcategoryId,
  updateUserInput,
  selectError,
} from '@/store/slices/bankingInformationSlice';
import { QuestionItem } from '@/mobile/components/dashboard/BankingInformation/FormFields';
import ScrollToQuestion from '@/mobile/components/dashboard/BankingInformation/ScrollToQuestion';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Use 405 (Bank Details) questions
const questions = bankingData["405"] || [];

function splitBankingSteps(questions: any[]) {
  // Step 0: Bank selection
  const step0 = questions.filter(q => q.id === "b1");
  // Step 1: Bank details (branch and contact info)
  const step1 = questions.filter(q => ["b2", "b3"].includes(q.id));
  // Step 2: Online banking question + credentials (b4, b5, b6 together)
  const step2 = questions.filter(q => ["b4", "b5", "b6"].includes(q.id));
  return [step0, step1, step2];
}



export default function BankDetailsPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [formError, setError] = useState<string | null>(null);
  const [step, setStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Get data from Redux store
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId('405A')(state));
  const isLoadingRedux = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');
  const fromReview = queryParams.get('fromReview');

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (userInputs && userInputs.length > 0) {
      // Use the first matching record
      const userInput = userInputs[0];

      // Only update state if we have a new ID or if it's the first time
      if (userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);

        // Convert the saved answers to form values
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      } else if (!existingInputId && userInput._id) {
        // First time setting the ID
        setExistingInputId(userInput._id);

        // Convert the saved answers to form values
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      }
    }
  }, [userInputs, existingInputId]);

  // Handle navigation to specific question from review page
  useEffect(() => {
    if (fromReview && targetQuestionId && questions.length > 0) {
      // Find which step contains the target question
      const steps = splitBankingSteps(questions);
      let targetStep = 0;

      for (let i = 0; i < steps.length; i++) {
        if (steps[i].some(q => q.id === targetQuestionId)) {
          targetStep = i;
          break;
        }
      }

      setStep(targetStep);
    }
  }, [fromReview, targetQuestionId, questions]);

  // Show loading state if data is being fetched
  if (isLoading || isLoadingRedux) {
    return (
      <>
        <GradiantHeader title="Banking Information" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  const steps = splitBankingSteps(questions);
  const currentStepQuestions = steps[step] || [];

  return (
    <>
      <GradiantHeader title="Banking Information" showAvatar={true} />
      <div className="p-4">
        {/* Tab Bar */}
        <div className="flex flex-row flex-nowrap gap-3 mb-4 bg-gray-50 rounded-lg p-1 overflow-x-auto scrollbar-hide">
          {categoryTabsConfig.bankinginformation.map(tab => {
            const isActive = tab.label === "Bank Details";
            return (
              <button
                key={tab.label}
                className={`flex-1 py-1 px-1 rounded-md font-medium whitespace-nowrap ${
                  isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500"
                }`}
                disabled={isActive}
                onClick={() => !isActive && navigate(tab.path)}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Error message */}
        {(formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        {/* Form */}
        <Formik
          initialValues={Object.keys(savedAnswers).length > 0 ? savedAnswers : {}}
          enableReinitialize={true}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              if (!user || !user.id) {
                setError('You must be logged in to save answers');
                return;
              }

              // Group answers by sectionId
              const answersBySection: Record<string, any[]> = {};
              questions.forEach(q => {
                let value = values[q.id];

                // Special handling for online banking dependent fields
                if (q.dependsOn && q.dependsOn.questionId === 'b4' && q.dependsOn.value === 'Yes') {
                  // If user selected "No" for online banking, clear username and password fields
                  if (values['b4'] === 'No') {
                    value = "";
                  }
                }

                if (value !== "" && value !== undefined && value !== null) {
                  if (!answersBySection[q.sectionId]) answersBySection[q.sectionId] = [];

                  // Convert frontend question types to backend-compatible types
                  const backendType = q.type === 'password' ? 'text' :
                                    q.type === 'dropdown' ? 'choice' :
                                    q.type === 'textarea' ? 'text' :
                                    q.type;

                  answersBySection[q.sectionId].push({
                    index: q.order,
                    originalQuestionId: q.id,
                    question: q.text,
                    type: backendType,
                    answer: Array.isArray(value) ? value.join(', ') : value
                  });
                }
              });

              const formattedAnswersBySection = Object.entries(answersBySection).map(
                ([sectionId, answers]) => ({
                  originalSectionId: sectionId,
                  isCompleted: true,
                  answers
                })
              );

              const userData = {
                userId: user.id,
                categoryId: generateObjectId(),
                originalCategoryId: '13', // Banking Information category ID
                subCategoryId: generateObjectId(),
                originalSubCategoryId: '405A', // Bank Details subcategory ID
                answersBySection: formattedAnswersBySection
              };

              if (existingInputId) {
                await dispatch(updateUserInput({
                  id: existingInputId,
                  userData
                })).unwrap();
              } else {
                const result = await dispatch(saveUserInput(userData)).unwrap();
                if (result && result._id) {
                  setExistingInputId(result._id);
                }
              }

              // Refresh data after saving for consistency with web version
              const ownerId = await getCachedOwnerIdFromUser(user);
              if (ownerId) {
                await dispatch(fetchUserInputs(ownerId));
              }

              if (fromReview) {
                navigate('/category/bankinginformation/review');
              } else {
                navigate("/category/bankinginformation/review");
              }
              setSubmitting(false);
            } catch (err) {
              console.error('Error saving banking information:', err);
              setError("Failed to save your answers. Please try again.");
              setSubmitting(false);
            }
          }}
        >
          {({ values, isSubmitting, handleSubmit }) => {
            const visibleQuestions = currentStepQuestions.filter(q => {
              if (!q.dependsOn) return true;
              return values[q.dependsOn.questionId] === q.dependsOn.value;
            });

            const handleNext = async () => {
              setStep(s => s + 1);
            };

            const handleSave = async () => {
              handleSubmit();
            };

            return (
              <Form>
                <div className="bg-gray-50 p-5 rounded-xl shadow-sm border mb-4">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-semibold">
                      Banking Information: <span className="text-[#2BCFD5]">Bank Details</span>
                    </p>
                    <CircularProgress
                      value={step + 1}
                      max={steps.length}
                      size={40}
                      stroke={3}
                      color="#2BCFD5"
                    />
                  </div>
                </div>
                
                <ScrollToQuestion questions={visibleQuestions}>
                  {(questionRefs) => (
                    <div className="space-y-4 mt-8 bg-gray-50 p-5 rounded-xl shadow-sm border">
                      {visibleQuestions.map(q => (
                        <div key={q.id} ref={el => { questionRefs[q.id] = el; }}>
                          <QuestionItem question={q} values={values} />
                          <ErrorMessage name={q.id} component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollToQuestion>

                <div className="mt-6 flex justify-between items-center">
                  <button
                    type="button"
                    onClick={() => setStep(s => s - 1)}
                    disabled={step === 0}
                    className="text-[#2BCFD5] underline disabled:opacity-50"
                  >
                    ← Back
                  </button>
                  {step < steps.length - 1 ? (
                    <button
                      type="button"
                      onClick={handleNext}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      Next →
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={handleSave}
                      disabled={isSubmitting}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      {isSubmitting ? 'Saving...' : 'Save'}
                    </button>
                  )}
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <Footer />
    </>
  );
}
