import { body, param } from 'express-validator';
import { validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

// Validation for creating owner
export const validateCreateOwner = [
  body('userId')
    .notEmpty()
    .withMessage('User ID is required')
    .isMongoId()
    .withMessage('User ID must be a valid MongoDB ObjectId'),
  
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email')
    .normalizeEmail()
    .toLowerCase(),
  
  body('username')
    .optional()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('firstName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters')
    .trim(),
  
  body('lastName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters')
    .trim(),
  
  body('googleId')
    .optional()
    .isString()
    .withMessage('Google ID must be a string'),
  
  body('externalUser')
    .optional()
    .isBoolean()
    .withMessage('External user must be a boolean value'),
  
  handleValidationErrors
];

// Validation for updating owner
export const validateUpdateOwner = [
  param('id')
    .isMongoId()
    .withMessage('Owner ID must be a valid MongoDB ObjectId'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email')
    .normalizeEmail()
    .toLowerCase(),
  
  body('username')
    .optional()
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('firstName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('First name must be between 1 and 50 characters')
    .trim(),
  
  body('lastName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Last name must be between 1 and 50 characters')
    .trim(),
  
  handleValidationErrors
];

// Validation for getting owner by ID
export const validateGetOwnerById = [
  param('id')
    .isMongoId()
    .withMessage('Owner ID must be a valid MongoDB ObjectId'),
  
  handleValidationErrors
];

// Validation for getting owner by user ID
export const validateGetOwnerByUserId = [
  param('userId')
    .isMongoId()
    .withMessage('User ID must be a valid MongoDB ObjectId'),
  
  handleValidationErrors
];

// Error handling middleware
function handleValidationErrors(req: Request, res: Response, next: NextFunction): void {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    res.status(400).json({
      status: 'fail',
      message: 'Validation failed',
      errors: errors.array()
    });
    return;
  }
  
  next();
}
