"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const Role_1 = require("../types/Role");
const roleSchema = new mongoose_1.default.Schema({
    name: {
        type: String,
        enum: Object.values(Role_1.RoleType),
        required: true,
        unique: true
    },
    description: {
        type: String,
        required: true
    },
    permissions: [{
            type: String,
            required: true
        }],
    isActive: {
        type: Boolean,
        default: true
    }
}, { timestamps: true });
// Add text index for name field for search capabilities
roleSchema.index({ name: 'text' });
// Static method to get default roles
roleSchema.statics.getDefaultRoles = function () {
    return [
        {
            name: Role_1.RoleType.OWNER,
            description: 'Primary account holder with full access to all features and data',
            permissions: [
                'canViewAll',
                'canEditAll',
                'canDeleteAll',
                'canCreateAll'
            ],
            isActive: true
        },
        {
            name: Role_1.RoleType.NOMINEE,
            description: 'Designated beneficiary with view-only access to information',
            permissions: [
                'canViewAll'
            ],
            isActive: true
        },
        {
            name: Role_1.RoleType.FAMILY,
            description: 'Family members with full access to all features and data',
            permissions: [
                'canViewAll',
                'canEditAll',
                'canDeleteAll',
                'canCreateAll'
            ],
            isActive: true
        }
    ];
};
// Instance method to check if role has specific permission
roleSchema.methods.hasPermission = function (permission) {
    return this.permissions.includes(permission);
};
const Role = mongoose_1.default.model('Role', roleSchema);
exports.default = Role;
//# sourceMappingURL=Role.js.map