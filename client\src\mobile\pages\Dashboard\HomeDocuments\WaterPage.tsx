import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik, Form, ErrorMessage, Field } from "formik";
import * as Yup from 'yup';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import homeDocuments from '@/data/homeDocuments.json';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId,
  updateUserInput,
  selectError,
} from '@/store/slices/homeDocumentsSlice';
import { QuestionItem } from '@/mobile/components/dashboard/HomeDocuments/FormFields';
import ScrollToQuestion from '@/mobile/components/dashboard/HomeDocuments/ScrollToQuestion';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Only use 303 (Water) questions
const questions = homeDocuments["303"] || [];

function splitWaterSteps(questions: any[]) {
  // Step 0: Initial question about paying for water
  const step0 = questions.filter(q => q.id === "w0");
  // Step 1: provider, account, bill (only if they pay for water)
  const step1 = questions.filter(q => ["w1", "w2", "w3"].includes(q.id));
  // Step 2: online payment + login (if yes)
  const step2 = questions.filter(q => !["w0", "w1", "w2", "w3"].includes(q.id));
  return [step0, step1, step2];
}

// Update the type definitions at the top
type FormValues = Record<string, string | string[]>;

export default function WaterPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [formError, setError] = useState<string | null>(null);
  const [step, setStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Get data from Redux store
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId('303A')(state));
  const isLoadingRedux = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');
  const fromReview = queryParams.get('fromReview');

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (userInputs && userInputs.length > 0) {
      // Use the first matching record
      const userInput = userInputs[0];

      // Only update state if we have a new ID or if it's the first time
      if (userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);

        // Convert the saved answers to form values
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      } else if (!existingInputId && userInput._id) {
        // First time setting the ID
        setExistingInputId(userInput._id);

        // Convert the saved answers to form values
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      }
    }
  }, [userInputs, existingInputId]);

  // Show loading state if data is being fetched
  if (isLoading) {
    return (
      <>
        <GradiantHeader title="Home Documents" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  const steps = splitWaterSteps(questions);
  const currentStepQuestions = steps[step] || [];

  return (
    <>
      <GradiantHeader title="Home Documents" showAvatar={true} />
      <div className="p-4">
        {/* Tab Bar */}
        <div className="flex flex-row flex-nowrap gap-3 mb-4 bg-gray-50 rounded-lg p-1 overflow-x-auto scrollbar-hide">
          {categoryTabsConfig.homedocuments.map(tab => {
            const isActive = tab.label === "Water";
            return (
              <button
                key={tab.label}
                className={`flex-1 py-1 px-1 rounded-md font-medium whitespace-nowrap ${
                  isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500"
                }`}
                disabled={isActive}
                onClick={() => !isActive && navigate(tab.path)}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Error message */}
        {(formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        {/* Form */}
        <Formik
          initialValues={Object.keys(savedAnswers).length > 0 ? savedAnswers : {}}
          enableReinitialize={true}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              if (!user || !user.id) {
                setError('You must be logged in to save answers');
                return;
              }

              // If user doesn't pay for water, only save that answer
              if (values["w0"] === "no") {
                const userData = {
                  userId: user.id,
                  categoryId: generateObjectId(),
                  originalCategoryId: '2',
                  subCategoryId: generateObjectId(),
                  originalSubCategoryId: '303A',
                  answersBySection: [{
                    originalSectionId: "303A",
                    isCompleted: true,
                    answers: [{
                      index: 0,
                      originalQuestionId: "w0",
                      question: "Do you pay for your water?",
                      type: "boolean",
                      answer: "no"
                    }]
                  }]
                };

                if (existingInputId) {
                  await dispatch(updateUserInput({
                    id: existingInputId,
                    userData
                  })).unwrap();
                } else {
                  await dispatch(saveUserInput(userData)).unwrap();
                }
                if (fromReview) {
                  navigate('/category/homedocuments/review');
                } else {
                  navigate("/category/homedocuments/trashcollection");
                }
                setSubmitting(false);
                return;
              }

              // Group answers by sectionId
              const answersBySection: Record<string, any[]> = {};
              questions.forEach(q => {
                const value = values[q.id];
                if (value !== "" && value !== undefined) {
                  if (!answersBySection[q.sectionId]) answersBySection[q.sectionId] = [];
                  answersBySection[q.sectionId].push({
                    index: q.order,
                    originalQuestionId: q.id,
                    question: q.text,
                    type: q.type,
                    answer: Array.isArray(value) ? value.join(', ') : value
                  });
                }
              });

              const formattedAnswersBySection = Object.entries(answersBySection).map(
                ([sectionId, answers]) => ({
                  originalSectionId: sectionId,
                  isCompleted: true,
                  answers
                })
              );

              const userData = {
                userId: user.id,
                categoryId: generateObjectId(),
                originalCategoryId: '2',
                subCategoryId: generateObjectId(),
                originalSubCategoryId: '303A',
                answersBySection: formattedAnswersBySection
              };

              if (existingInputId) {
                await dispatch(updateUserInput({
                  id: existingInputId,
                  userData
                })).unwrap();
              } else {
                await dispatch(saveUserInput(userData)).unwrap();
              }
              if (fromReview) {
                navigate('/category/homedocuments/review');
              } else {
                navigate("/category/homedocuments/trashcollection");
              }
              setSubmitting(false);
            } catch (err) {
              setError("Failed to save your answers. Atleast one question must be answered.");
              setSubmitting(false);
            }
          }}
        >
          {({ values, isSubmitting, errors, touched, setTouched, validateForm, handleSubmit }) => {
            const visibleQuestions = currentStepQuestions.filter(q => {
              if (!q.dependsOn) return true;
              return values[q.dependsOn.questionId] === q.dependsOn.value;
            });

            const handleNext = async () => {
              // Removed validation checks - allow proceeding without required fields
              if (step === 0 && values["w0"] === "no") {
                handleSubmit();
              } else {
                setStep(s => s + 1);
              }
            };

            const handleSave = async (e: React.FormEvent) => {
              const touchedFields = Object.fromEntries(visibleQuestions.map(q => [q.id, true]));
              setTouched(touchedFields, true);
              const formErrors = await validateForm();
              const stepHasError = visibleQuestions.some(q => formErrors[q.id]);
              if (!stepHasError) {
                if (step === 0 && values["w0"] === "no") {
                  handleSubmit();
                } else {
                  handleSubmit();
                }
              }
            };

            return (
              <Form>
                <div className="bg-gray-50 p-5 rounded-xl shadow-sm border mb-4">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-semibold">
                      Home Documents: <span className="text-[#2BCFD5]">Water</span>
                    </p>
                    <CircularProgress
                      value={step + 1}
                      max={values["w0"] === "no" ? 1 : steps.length}
                      size={40}
                      stroke={3}
                      color="#2BCFD5"
                    />
                  </div>
                </div>
                <ScrollToQuestion questions={visibleQuestions}>
                  {(questionRefs) => (
                    <div className="space-y-4 mt-8 bg-gray-50 p-5 rounded-xl shadow-sm border">
                      {visibleQuestions.map(q => (
                        <div key={q.id}>
                          <label className="block font-medium text-gray-700 mb-2">
                            {q.text}{q.required && " *"}
                          </label>
                          {q.type === "boolean" ? (
                            <div className="flex space-x-4">
                              {["yes", "no"].map(option => (
                                <label
                                  key={option}
                                  className={
                                    "flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer " +
                                    (values[q.id] === option
                                      ? "bg-[#2BCFD5] text-white border-[#2BCFD5]"
                                      : "bg-gray-50 hover:bg-[#25b6bb] hover:text-white")
                                  }
                                  style={{ transition: "all 0.2s" }}
                                >
                                  <Field type="radio" name={q.id} value={option} className="hidden" />
                                  {option.charAt(0).toUpperCase() + option.slice(1)}
                                </label>
                              ))}
                            </div>
                          ) : q.type === "choice" && Array.isArray(q.options) ? (
                            <Field as="select" name={q.id} className="w-full border rounded-lg px-3 py-2">
                              <option value="">Select...</option>
                              {q.options.map((option: string) => (
                                <option key={option} value={option}>{option}</option>
                              ))}
                            </Field>
                          ) : (
                            <Field
                              name={q.id}
                              type={
                                q.text.toLowerCase().includes("password") ? "password"
                                : q.text.toLowerCase().includes("account number") ? "number"
                                : q.text.toLowerCase().includes("bill amount") ? "number"
                                : "text"
                              }
                              className="w-full border rounded-lg px-3 py-2"
                            />
                          )}
                          <ErrorMessage name={q.id} component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollToQuestion>

                <div className="mt-6 flex justify-between items-center">
                  <button
                    type="button"
                    onClick={() => setStep(s => s - 1)}
                    disabled={step === 0}
                    className="text-[#2BCFD5] underline disabled:opacity-50"
                  >
                    ← Back
                  </button>
                  {step < steps.length - 1 ? (
                    <button
                      type="button"
                      onClick={handleNext}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      Next →
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={handleSave}
                      disabled={isSubmitting}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      Save
                    </button>
                  )}
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <Footer />
    </>
  );
}
