"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleMulterError = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        const uploadsDir = path_1.default.join(__dirname, '../../uploads');
        // Ensure uploads directory exists
        if (!fs_1.default.existsSync(uploadsDir)) {
            fs_1.default.mkdirSync(uploadsDir, { recursive: true });
        }
        cb(null, uploadsDir);
    },
    filename: (req, file, cb) => {
        // Create unique filename with timestamp and random string
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path_1.default.extname(file.originalname));
    }
});
const upload = (0, multer_1.default)({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
        files: 1 // Only allow 1 file at a time
    },
    fileFilter: (req, file, cb) => {
        checkFileType(file, cb);
    }
});
// Error handling middleware for multer
const handleMulterError = (error, req, res, next) => {
    if (error instanceof multer_1.default.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                message: 'Upload image less than 5MB',
                error: 'FILE_TOO_LARGE'
            });
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                message: 'Only one file allowed',
                error: 'TOO_MANY_FILES'
            });
        }
        return res.status(400).json({
            message: 'File upload error',
            error: error.code
        });
    }
    if (error.name === 'INVALID_FILE_TYPE') {
        return res.status(400).json({
            message: error.message,
            error: 'INVALID_FILE_TYPE'
        });
    }
    next(error);
};
exports.handleMulterError = handleMulterError;
function checkFileType(file, cb) {
    // Allowed file types
    const filetypes = /jpeg|jpg|png|gif|webp/;
    // Allowed MIME types
    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const extname = filetypes.test(path_1.default.extname(file.originalname).toLowerCase());
    const mimetype = allowedMimeTypes.includes(file.mimetype.toLowerCase());
    if (mimetype && extname) {
        return cb(null, true);
    }
    else {
        const error = new Error('Only image files are allowed! (JPEG, JPG, PNG, GIF, WebP)');
        error.name = 'INVALID_FILE_TYPE';
        cb(error);
    }
}
exports.default = upload;
//# sourceMappingURL=multerMiddleware.js.map