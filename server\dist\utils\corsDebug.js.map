{"version": 3, "file": "corsDebug.js", "sourceRoot": "", "sources": ["../../src/utils/corsDebug.ts"], "names": [], "mappings": ";;;AAEA,wCAAwC;AACjC,MAAM,mBAAmB,GAAG,CAAC,GAAoB,EAAE,GAAqB,EAAE,IAA0B,EAAE,EAAE;IAC7G,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IACvC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;IAEvC,iCAAiC;IACjC,GAAG,CAAC,MAAM,CAAC,6BAA6B,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC;IACrE,GAAG,CAAC,MAAM,CAAC,8BAA8B,EAAE,iCAAiC,CAAC,CAAC;IAC9E,GAAG,CAAC,MAAM,CAAC,8BAA8B,EAAE,iFAAiF,CAAC,CAAC;IAC9H,GAAG,CAAC,MAAM,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAC;IAEvD,4BAA4B;IAC5B,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACtB,OAAO;IACT,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAtBW,QAAA,mBAAmB,uBAsB9B;AAEF,0CAA0C;AACnC,MAAM,gBAAgB,GAAG,CAAC,GAAoB,EAAE,GAAqB,EAAE,EAAE;IAC9E,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IACtC,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,kBAAkB;QAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM;QAC1B,MAAM,EAAE,GAAG,CAAC,MAAM;KACnB,CAAC,CAAC;AACL,CAAC,CAAC;AARW,QAAA,gBAAgB,oBAQ3B"}