import { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger, SheetTitle, SheetDescription } from '@/components/ui/sheet';
import { InviteDialog } from "@/components/common/InviteDialog";
import { RequestCategoryDialog } from "@/components/RequestCategories/RequestCategoryDialog";
import { 
  KeySquare, 
  Menu, 
  LogOut, 
  User, 
  HelpCircle, 
  Bell, 
  Mail, 
  Home,
  Settings,
  FileText,
  Phone,
  Info,
  BookOpen,
  Newspaper,
  Mail as MailIcon,
  LogIn,
  UserPlus
} from 'lucide-react';

import logo from '@/assets/global/logo.png';
import defaultAvatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { useAccessControl } from '@/hooks/useAccessControl';

export default function Header({
  isLandingPage,
  isAuthenticated,
  showAuthButtons,
  user,
  handleLogout,
}: {
  isLandingPage: boolean;
  isAuthenticated: boolean;
  user: any;
  showAuthButtons: boolean;
  handleLogout: () => void;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { logout } = useAuth();
  const { toast } = useToast();

  // Get the avatar image source
  const getAvatarSrc = () => {
    if (user?.image) {
      return user.image.startsWith('http')
        ? user.image
        : `${import.meta.env.VITE_API_URL}/uploads/${user.image}`;
    }
    return defaultAvatar;
  };
  const { keyHolders, userAccess } = useAccessControl();
  const pendingEmergencyRequests = userAccess?.isOwner
    ? keyHolders.filter(kh => kh.emergencyAccessRequested && !kh.emergencyAccessGranted && !kh.emergencyAccessDenied)
    : [];
  
  const isDashboard = location.pathname.startsWith('/dashboard') || location.pathname.startsWith('/category');
  const isAuthPage = location.pathname.startsWith('/auth/');
  const currentLogo = logo;
  

  const renderLandingLinks = () => (
    <nav className="hidden md:flex items-center space-x-6">
      <Link to="/about" className="text-white hover:text-[#2BCFD5] font-medium">About</Link>
      <Link to="/plans" className="text-white hover:text-[#2BCFD5] font-medium">Plans</Link>
      <Link to="/blog" className="text-white hover:text-[#2BCFD5] font-medium">Blog</Link>
      <Link to="/contact" className="text-white hover:text-[#2BCFD5] font-medium">Contact</Link>
    </nav>
  );

  const renderAppLinks = () => (
    <nav className="hidden md:flex items-center space-x-6">
      <Link to="/dashboard" className="text-secondary-900 hover:text-[#2BCFD5] font-medium">Dashboard</Link>
      <Link to="/directory" className="text-secondary-900 hover:text-[#2BCFD5] font-medium">Directory</Link>
      <Link to="/support" className="text-secondary-900 hover:text-[#2BCFD5] font-medium">Support</Link>
    </nav>
  );

  const renderDesktopButtons = () => {
    if (isLandingPage) {
      return (
        <div className="hidden md:flex gap-2">
          <Button variant="secondary" className="bg-white text-[#1F4168] hover:bg-gray-200 font-medium">Directory</Button>
          <Button variant="secondary" className="bg-[#2BCFD5] text-white hover:bg-[#2BCFD5]/90 font-medium">Dashboard</Button>
        </div>
      );
    }

    if (isAuthenticated) {
      return (
        <div className="hidden md:flex items-center gap-2">
          <Link to="/auth/subscribe">
            <Button variant="outline" className="font-medium">Subscribe</Button>
          </Link>
          <Button variant="ghost" size="icon"><Settings className="w-5 h-5" /></Button>
          <Button variant="ghost" size="icon"><Bell className="w-5 h-5" /></Button>
          <div className="h-8 w-8 rounded-full overflow-hidden bg-white shadow-sm">
            <img
              src={getAvatarSrc()}
              alt={`${user?.firstName || 'User'}'s avatar`}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = defaultAvatar;
              }}
            />
          </div>
        </div>
      );
    }

    return null;
  };

  const handleLogoutClick = async () => {
    try {
      await logout();
      toast({
        title: "Logged out successfully",
        description: "You have been logged out",
        variant: "custom"
      });
      setIsOpen(false);
      navigate('/');
    } catch (error) {
      toast({
        title: "Logout failed",
        description: "Please try again",
        variant: "custom"
      });
    }
  };

  return (
    <header
      className={`sticky top-0 z-50 w-full transition-colors duration-200 ${
        isLandingPage
          ? 'bg-[#1F4168] text-white'
          : 'bg-white text-secondary-900 border-b border-border shadow-sm'
      }`}
    >
      <div className="container mx-auto px-4 py-4 md:py-5 flex items-center justify-between">
        <div className="flex items-center space-x-4 md:space-x-8">
          <Link to="/" className="flex items-center">
            <img
              src={currentLogo}
              alt="HeirKey Logo"
              className={`h-12 md:h-16 w-auto object-contain ${
                isLandingPage ? 'invert-[.7] sepia-[.3] saturate-[1000%] hue-rotate-[140deg] brightness-[.9] contrast-[.9]' : ''
              }`}
            />
          </Link>
          {isLandingPage ? renderLandingLinks() : renderAppLinks()}
        </div>

        {renderDesktopButtons()}

        {/* Mobile Navigation */}
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className={`md:hidden transition-colors ${
                isLandingPage ? 'text-white' : 'text-secondary-900'
              } relative`}
            >
              <Menu className="w-7 h-7" />
              {pendingEmergencyRequests.length > 0 && (
                <span className="absolute top-1 right-1 block w-2.5 h-2.5 bg-green-600 rounded-full animate-pulse z-10" />
              )}
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="w-[85vw] sm:w-[400px] p-0">
            <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
            <SheetDescription className="sr-only">Main navigation and user options</SheetDescription>
            <div className="flex flex-col h-full bg-gray-50">
              {/* Header: Only logo, no close/hamburger icon */}
              <div className="p-6 bg-white border-b flex items-center justify-between">
                <img
                  src={currentLogo}
                  alt="HeirKey Logo"
                  className={`h-12 w-18 object-contain ${
                    isLandingPage ? 'invert-[.7] sepia-[.3] saturate-[1000%] hue-rotate-[140deg] brightness-[.9] contrast-[.9]' : ''
                  }`}
                />
              </div>

              <div className="flex-1 overflow-y-auto">
                {isAuthenticated ? (
                  <>
                    {/* User Profile Card */}
                    <div className="p-6 mb-4 bg-gradient-to-br from-[#1F2668] to-[#22BBCC] text-white">
                      <div className="flex items-start gap-4">
                        <div className="h-12 w-12 rounded-full overflow-hidden bg-white shadow-lg">
                          <img
                            src={getAvatarSrc()}
                            alt={`${user?.firstName || 'User'}'s avatar`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = defaultAvatar;
                            }}
                          />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">
                            {user?.firstName} {user?.lastName}
                          </h3>
                          <div className="flex items-center gap-2 opacity-80 mt-1">
                            <Mail className="h-4 w-4" />
                            <span className="text-sm">{user?.email}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Navigation Links with icons */}
                    <div className="px-3">
                      <div className="space-y-1">
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12" 
                          onClick={() => {
                            navigate('/dashboard');
                            setIsOpen(false);
                          }}
                        >
                          <KeySquare className="mr-3 h-5 w-5" />
                          Dashboard
                        </Button>
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12 relative" 
                          onClick={() => {
                            navigate('/auth/user-profile');
                            setIsOpen(false);
                          }}
                        >
                          <span className="relative">
                            <User className="mr-3 h-5 w-5" />
                            {pendingEmergencyRequests.length > 0 && (
                              <span className="absolute top-0 right-0 block w-2 h-2 bg-green-600 rounded-full animate-pulse z-10" />
                            )}
                          </span>
                          Profile
                        </Button>
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12" 
                          onClick={() => {
                            navigate('/subscription');
                            setIsOpen(false);
                          }}
                        >
                          <BookOpen className="mr-3 h-5 w-5" />
                          Subscription
                        </Button>
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12" 
                          onClick={() => {
                            navigate('/settings');
                            setIsOpen(false);
                          }}
                        >
                          <Settings className="mr-3 h-5 w-5" />
                          Settings
                        </Button>
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12" 
                          onClick={() => {
                            navigate('/directory');
                            setIsOpen(false);
                          }}
                        >
                          <FileText className="mr-3 h-5 w-5" />
                          Directory
                        </Button>
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12" 
                          onClick={() => {
                            navigate('/support');
                            setIsOpen(false);
                          }}
                        >
                          <Phone className="mr-3 h-5 w-5" />
                          Support
                        </Button>
                        {(user?.role === 'Family' || user?.role === 'Nominee') && <RequestCategoryDialog />}
                        {user?.role === 'Owner' && <InviteDialog />}
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    {showAuthButtons && (
                      <div className="p-6 space-y-3">
                        <Button 
                          variant="outline" 
                          className="w-full justify-center h-11 flex items-center gap-2"
                          onClick={() => {
                            navigate('/auth/login');
                            setIsOpen(false);
                          }}
                        >
                          <LogIn className="w-5 h-5" />
                          Log in
                        </Button>
                        <Button 
                          className="w-full justify-center h-11 bg-[#2BCFD5] hover:bg-[#2BCFD5]/90 flex items-center gap-2"
                          onClick={() => {
                            navigate('/auth/register');
                            setIsOpen(false);
                          }}
                        >
                          <UserPlus className="w-5 h-5" />
                          Sign up
                        </Button>
                      </div>
                    )}

                    {/* Quick Links for non-authenticated users */}
                    <div className="px-3">
                      <h4 className="px-3 text-sm font-medium text-gray-500 mb-2">Quick Links</h4>
                      <div className="space-y-1">
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12" 
                          onClick={() => {
                            navigate('/');
                            setIsOpen(false);
                          }}
                        >
                          <Home className="mr-3 h-5 w-5" />
                          Home
                        </Button>
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12" 
                          onClick={() => {
                            navigate('/about');
                            setIsOpen(false);
                          }}
                        >
                          <Info className="mr-3 h-5 w-5" /> About
                        </Button>
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12" 
                          onClick={() => {
                            navigate('/plans');
                            setIsOpen(false);
                          }}
                        >
                          <BookOpen className="mr-3 h-5 w-5" /> Plans
                        </Button>
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12" 
                          onClick={() => {
                            navigate('/blog');
                            setIsOpen(false);
                          }}
                        >
                          <Newspaper className="mr-3 h-5 w-5" /> Blog
                        </Button>
                        <Button 
                          variant="ghost" 
                          className="w-full justify-start h-12" 
                          onClick={() => {
                            navigate('/contact');
                            setIsOpen(false);
                          }}
                        >
                          <MailIcon className="mr-3 h-5 w-5" /> Contact
                        </Button>
                      </div>
                    </div>
                  </>
                )}
              </div>

              {/* Footer Actions */}
              {isAuthenticated && (
                <div className="p-4 border-t bg-white mt-auto">
                  <Button 
                    variant="ghost" 
                    className="w-full justify-start h-12 text-red-500 hover:text-red-600 hover:bg-red-50" 
                    onClick={handleLogoutClick}
                  >
                    <LogOut className="mr-3 h-5 w-5" />
                    Log out
                  </Button>
                </div>
              )}

              <div className="p-4 border-t bg-white">
                <Button 
                  variant="ghost" 
                  className="w-full justify-start h-12" 
                  onClick={() => {
                    navigate('/help');
                    setIsOpen(false);
                  }}
                >
                  <HelpCircle className="mr-3 h-5 w-5" />
                  Help & Support
                </Button>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </header>
  );
}
