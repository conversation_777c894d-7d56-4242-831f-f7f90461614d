import { Request, Response } from 'express';
export declare const createRole: (req: Request, res: Response) => Promise<void>;
export declare const getAllRoles: (req: Request, res: Response) => Promise<void>;
export declare const getRoleById: (req: Request, res: Response) => Promise<void>;
export declare const getRoleByName: (req: Request, res: Response) => Promise<void>;
export declare const updateRole: (req: Request, res: Response) => Promise<void>;
export declare const deleteRole: (req: Request, res: Response) => Promise<void>;
export declare const initializeDefaultRoles: (req: Request, res: Response) => Promise<void>;
export declare const assignRoleToUser: (req: Request, res: Response) => Promise<void>;
export declare const getUsersByRole: (req: Request, res: Response) => Promise<void>;
