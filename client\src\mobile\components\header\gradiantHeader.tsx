import { useAuth } from '@/contexts/AuthContext';
import { UserAvatar } from '../layout/UserAvatar';
import { MobileEmergencyAccessStatus } from '../common/MobileEmergencyAccessStatus';
import defaultAvatar from '@/assets/global/defaultAvatar/defaultImage.jpg';

interface AuthHeaderProps {
  title: string;
  showAvatar?: boolean;
  logo?: string;
  showEmergencyStatus?: boolean;
}

export default function AuthHeader({ title, showAvatar = false, showEmergencyStatus = false }: AuthHeaderProps) {
  const { user } = useAuth();

  // Get the avatar image source
  const getAvatarSrc = () => {
    if (user?.image) {
      return user.image.startsWith('http')
        ? user.image
        : `${import.meta.env.VITE_API_URL}/uploads/${user.image}`;
    }
    return defaultAvatar;
  };

  // Get user initials for fallback
  const getInitials = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
    }
    if (user?.email) {
      return user.email[0].toUpperCase();
    }
    return 'U';
  };

  return (
    <div className="w-full md:w-1/2 bg-gradient-to-br from-[#1F2668] to-[#22BBCC] text-white">
      <div className="py-6 md:py-8 px-6 md:px-10 relative">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">
              {title}
            </h1>
            {showAvatar && (
              <div className="text-base md:text-lg opacity-80 mt-1">
                {user?.email}
              </div>
            )}
            {showEmergencyStatus && (
              <div className="mt-2">
                <MobileEmergencyAccessStatus showText={true} />
              </div>
            )}
          </div>
          {showAvatar && (
            <div className="w-16 h-16 rounded-full shadow-lg overflow-hidden bg-white">
              <img
                src={getAvatarSrc()}
                alt={`${user?.firstName || 'User'}'s avatar`}
                className="w-full h-full object-cover rounded-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = defaultAvatar;
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 