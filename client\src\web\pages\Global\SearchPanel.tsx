import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { useState, useEffect, useMemo, useCallback } from 'react';
import { useAppSelector } from '@/store/hooks';
import { useNavigate } from 'react-router-dom';
import { Search, X, FileText, MapPin, Phone, Home, Heart, Users, Globe } from 'lucide-react';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { subCategoryIdToPath } from '@/data/subCategoryIdToPath';

interface SearchResult {
  id: string;
  category: string;
  subCategory: string;
  question: string;
  answer: string;
  path: string;
  icon: React.ReactNode;
}

interface UserInput {
  _id?: string;
  userId: string;
  categoryId?: string;
  originalCategoryId: string;
  subCategoryId?: string;
  originalSubCategoryId: string;
  answersBySection: {
    sectionId?: string;
    originalSectionId: string;
    isCompleted: boolean;
    answers: {
      index: number;
      originalQuestionId: string;
      question: string;
      type: string;
      answer: string;
    }[];
  }[];
}

const categoryIcons: Record<string, React.ReactNode> = {
  homeinstructions: <Home className="w-4 h-4" />,
  homedocuments: <FileText className="w-4 h-4" />,
  willinstructions: <FileText className="w-4 h-4" />,
  funeralarrangements: <Heart className="w-4 h-4" />,
  importantcontacts: <Users className="w-4 h-4" />,
  socialmedia: <Globe className="w-4 h-4" />,
  quickstart: <MapPin className="w-4 h-4" />,
};

const categoryDisplayNames: Record<string, string> = {
  homeinstructions: 'Home Instructions',
  homedocuments: 'Home Documents',
  willinstructions: 'Will & Testament',
  funeralarrangements: 'Funeral Arrangements',
  importantcontacts: 'Important Contacts',
  socialmedia: 'Social Media & Phone',
  quickstart: 'Quick Start',
};

// Debounce hook
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Highlight search terms
function highlightText(text: string, query: string): React.ReactNode {
  if (!query.trim()) return text;
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  const parts = text.split(regex);
  
  return parts.map((part, index) => 
    regex.test(part) ? (
      <mark key={index} className="bg-yellow-200 px-1 rounded">
        {part}
      </mark>
    ) : part
  );
}

export default function SearchPanel() {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const navigate = useNavigate();

  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Get all user inputs from different slices
  const homeInstructionsInputs = useAppSelector(state => state.homeInstructions.userInputs);
  const funeralArrangementsInputs = useAppSelector(state => state.funeralArrangements.userInputs);
  const willInstructionsInputs = useAppSelector(state => state.willInstructions.userInputs);
  const importantContactsInputs = useAppSelector(state => state.importantContacts.userInputs);
  const socialMediaInputs = useAppSelector(state => state.socialMedia.userInputs);
  const homeDocumentsInputs = useAppSelector(state => state.homeDocuments.userInputs);
  const quickStartAnswers = useAppSelector(state => state.quickStart.answers);
  const quickStartQuestions = useAppSelector(state => state.quickStart.questions);

  // Combine all user inputs
  const allUserInputs = useMemo(() => {
    const categoryInputs: (UserInput & { category: string })[] = [
      ...homeInstructionsInputs.map((input: UserInput) => ({ ...input, category: 'homeinstructions' })),
      ...funeralArrangementsInputs.map((input: UserInput) => ({ ...input, category: 'funeralarrangements' })),
      ...willInstructionsInputs.map((input: UserInput) => ({ ...input, category: 'willinstructions' })),
      ...importantContactsInputs.map((input: UserInput) => ({ ...input, category: 'importantcontacts' })),
      ...socialMediaInputs.map((input: UserInput) => ({ ...input, category: 'socialmedia' })),
      ...homeDocumentsInputs.map((input: UserInput) => ({ ...input, category: 'homedocuments' })),
    ];

    // Add quick start data in a compatible format
    if (Object.keys(quickStartAnswers).length > 0) {
      const quickStartInput: UserInput & { category: string } = {
        _id: 'quickstart',
        userId: '',
        originalCategoryId: 'quickstart',
        originalSubCategoryId: 'quickstart',
        category: 'quickstart',
        answersBySection: [{
          originalSectionId: 'quickstart',
          isCompleted: false,
          answers: Object.entries(quickStartAnswers).map(([questionId, answer]) => {
            const question = quickStartQuestions.find(q => q.id === questionId);
            return {
              index: 0,
              originalQuestionId: questionId,
              question: question?.text || '',
              type: 'text',
              answer: answer || ''
            };
          })
        }]
      };
      categoryInputs.push(quickStartInput);
    }

    return categoryInputs;
  }, [
    homeInstructionsInputs,
    funeralArrangementsInputs,
    willInstructionsInputs,
    importantContactsInputs,
    socialMediaInputs,
    homeDocumentsInputs,
    quickStartAnswers,
    quickStartQuestions
  ]);

  // Helper to get correct subcategory path from config or mapping
  function getSubCategoryPath(category: string, subCategoryId: string, sectionId?: string): string | null {
    // Try mapping file first (sectionId or subCategoryId)
    if (sectionId && subCategoryIdToPath[sectionId]) return subCategoryIdToPath[sectionId];
    if (subCategoryIdToPath[subCategoryId]) return subCategoryIdToPath[subCategoryId];
    // Fallback to config
    const tabs = categoryTabsConfig[category as keyof typeof categoryTabsConfig];
    if (!tabs) return null;
    let found = tabs.find(tab => {
      const pathEnd = tab.path.split('/').pop()?.toLowerCase();
      if (pathEnd === subCategoryId?.toLowerCase()) return true;
      const labelNorm = tab.label.replace(/\s+/g, '').toLowerCase();
      if (labelNorm === subCategoryId?.replace(/\s+/g, '').toLowerCase()) return true;
      return false;
    });
    return found ? found.path : null;
  }

  // Search results
  const searchResults = useMemo(() => {
    if (!debouncedSearchQuery.trim()) return [];

    const query = debouncedSearchQuery.toLowerCase();
    const results: SearchResult[] = [];

    allUserInputs.forEach((input: UserInput & { category: string }) => {
      input.answersBySection?.forEach((section: any) => {
        section.answers?.forEach((answer: any) => {
          const questionMatch = answer.question?.toLowerCase().includes(query);
          const answerMatch = answer.answer?.toLowerCase().includes(query);
          
          if (questionMatch || answerMatch) {
            // Use mapping file or config to get correct path
            const subCategoryPath = getSubCategoryPath(input.category, input.originalSubCategoryId, section.originalSectionId) || `/category/${input.category}/${input.originalSubCategoryId}`;
            results.push({
              id: `${input._id}-${section.originalSectionId}-${answer.originalQuestionId}`,
              category: input.category,
              subCategory: input.originalSubCategoryId,
              question: answer.question || '',
              answer: answer.answer || '',
              path: subCategoryPath,
              icon: categoryIcons[input.category] || <FileText className="w-4 h-4" />
            });
          }
        });
      });
    });

    return results.slice(0, 10); // Limit to 10 results
  }, [debouncedSearchQuery, allUserInputs]);

  // Handle search input change
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setShowResults(value.length > 0);
    setIsSearching(value.length > 0);
    setSelectedIndex(-1);
  }, []);

  // Handle search submission
  const handleSearch = useCallback(() => {
    if (searchQuery.trim()) {
      setShowResults(true);
      setIsSearching(false);
    }
  }, [searchQuery]);

  // Handle result click
  const handleResultClick = useCallback((result: SearchResult) => {
    navigate(result.path);
    setSearchQuery('');
    setShowResults(false);
    setSelectedIndex(-1);
  }, [navigate]);

  // Handle clear search
  const handleClearSearch = useCallback(() => {
    setSearchQuery('');
    setShowResults(false);
    setIsSearching(false);
    setSelectedIndex(-1);
  }, []);

  // Handle key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedIndex >= 0 && searchResults[selectedIndex]) {
        handleResultClick(searchResults[selectedIndex]);
      } else {
        handleSearch();
      }
    } else if (e.key === 'Escape') {
      handleClearSearch();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => 
        prev < searchResults.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
    }
  }, [selectedIndex, searchResults, handleResultClick, handleSearch, handleClearSearch]);

  // Update searching state based on debounced query
  useEffect(() => {
    setIsSearching(debouncedSearchQuery !== searchQuery);
  }, [debouncedSearchQuery, searchQuery]);

  return (
    <div className="bg-[#1F4168] rounded-xl p-8 w-full relative">
      <h2 className="text-white text-lg font-semibold mb-2">Search it Here</h2>
      <p className="text-gray-200 text-sm mb-4">
        Can't find a specific information you loaded, search for it below.
      </p>
      
      <div className="flex mb-6 relative">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            type="text"
            value={searchQuery}
            onChange={handleSearchChange}
            onKeyDown={handleKeyPress}
            className="flex-1 rounded-l-md border border-[#2BCFD5] focus:border-[#2BCFD5] pl-10 pr-10"
            placeholder="Search your information..."
          />
          {searchQuery && (
            <button
              onClick={handleClearSearch}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
        <Button 
          onClick={handleSearch}
          className="bg-[#2BCFD5] hover:bg-[#19bbb5] text-white rounded-l-none"
          disabled={!searchQuery.trim()}
        >
          Search
        </Button>
      </div>

      {/* Search Results */}
      {showResults && (
        <div className="absolute top-full left-0 right-0 bg-white rounded-lg shadow-lg border border-gray-200 max-h-96 overflow-y-auto z-50">
          {isSearching ? (
            <div className="p-4 text-center text-gray-500">
              <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#2BCFD5] mx-auto mb-2"></div>
              Searching...
            </div>
          ) : searchResults.length > 0 ? (
            <div className="py-2">
              {searchResults.map((result, index) => (
                <button
                  key={result.id}
                  onClick={() => handleResultClick(result)}
                  className={`w-full text-left p-4 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors ${
                    index === selectedIndex ? 'bg-blue-50 border-blue-200' : ''
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <div className="text-[#2BCFD5] mt-1">
                      {result.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-xs bg-[#2BCFD5] text-white px-2 py-1 rounded-full">
                          {categoryDisplayNames[result.category]}
                        </span>
                        {/* <span className="text-xs text-gray-500 capitalize">
                          {result.subCategory.replace(/([A-Z])/g, ' $1').trim()}
                        </span> */}
                      </div>
                      <h4 className="text-sm font-medium text-gray-900 mb-1 line-clamp-1">
                        {highlightText(result.question, debouncedSearchQuery)}
                      </h4>
                      <p className="text-xs text-gray-600 line-clamp-2">
                        {highlightText(result.answer, debouncedSearchQuery)}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : (
            <div className="p-4 text-center text-gray-500">
              <Search className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p>No results found for "{debouncedSearchQuery}"</p>
              <p className="text-xs mt-1">Try different keywords or check your spelling</p>
            </div>
          )}
        </div>
      )}

      <div className="bg-[#223c5a] rounded-lg p-4 mt-4 border border-[#2bcfd5]">
        <h3 className="text-white font-semibold mb-2">Still have questions?</h3>
        <p className="text-gray-200 text-sm mb-4">
          Can't find the answer you're looking for? Please chat to our friendly team.
        </p>
        <Button className="bg-[#2BCFD5] hover:bg-[#19bbb5] text-white w-full">
          Get in touch
        </Button>
      </div>
    </div>
  );
}