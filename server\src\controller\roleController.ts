import { Request, Response } from 'express';
import Role from '../models/Role';
import { RoleType } from '../types/Role';

// Create a new role
export const createRole = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, description, permissions, isActive } = req.body;

    // Check if role already exists
    const existingRole = await Role.findOne({ name });
    if (existingRole) {
      res.status(400).json({
        status: 'fail',
        message: `Role with name '${name}' already exists`
      });
      return;
    }

    const role = new Role({
      name,
      description,
      permissions,
      isActive: isActive !== undefined ? isActive : true
    });

    await role.save();
    
    res.status(201).json({
      status: 'success',
      message: 'Role created successfully',
      data: { role }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error creating role',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all roles
export const getAllRoles = async (req: Request, res: Response): Promise<void> => {
  try {
    const { isActive } = req.query;
    
    const filter: any = {};
    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    const roles = await Role.find(filter).sort({ createdAt: -1 });
    
    res.status(200).json({
      status: 'success',
      results: roles.length,
      data: { roles }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error fetching roles',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get role by ID
export const getRoleById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const role = await Role.findById(id);
    
    if (!role) {
      res.status(404).json({
        status: 'fail',
        message: 'Role not found'
      });
      return;
    }
    
    res.status(200).json({
      status: 'success',
      data: { role }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error fetching role',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get role by name
export const getRoleByName = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name } = req.params;
    
    const role = await Role.findOne({ name });
    
    if (!role) {
      res.status(404).json({
        status: 'fail',
        message: 'Role not found'
      });
      return;
    }
    
    res.status(200).json({
      status: 'success',
      data: { role }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error fetching role',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update role
export const updateRole = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { description, permissions, isActive } = req.body;
    
    const role = await Role.findById(id);
    
    if (!role) {
      res.status(404).json({
        status: 'fail',
        message: 'Role not found'
      });
      return;
    }

    // Update only provided fields
    if (description !== undefined) role.description = description;
    if (permissions !== undefined) role.permissions = permissions;
    if (isActive !== undefined) role.isActive = isActive;

    await role.save();
    
    res.status(200).json({
      status: 'success',
      message: 'Role updated successfully',
      data: { role }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error updating role',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete role (soft delete by setting isActive to false)
export const deleteRole = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const role = await Role.findById(id);
    
    if (!role) {
      res.status(404).json({
        status: 'fail',
        message: 'Role not found'
      });
      return;
    }

    role.isActive = false;
    await role.save();
    
    res.status(200).json({
      status: 'success',
      message: 'Role deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error deleting role',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Initialize default roles
export const initializeDefaultRoles = async (req: Request, res: Response): Promise<void> => {
  try {
    const defaultRoles = Role.getDefaultRoles();
    const createdRoles = [];

    for (const roleData of defaultRoles) {
      const existingRole = await Role.findOne({ name: roleData.name });

      if (!existingRole) {
        const role = new Role(roleData);
        await role.save();
        createdRoles.push(role);
      }
    }

    res.status(200).json({
      status: 'success',
      message: `Default roles initialized. Created ${createdRoles.length} new roles.`,
      data: { createdRoles }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error initializing default roles',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Assign role to user
export const assignRoleToUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const { userId, roleId } = req.body;

    if (!userId || !roleId) {
      res.status(400).json({
        status: 'fail',
        message: 'User ID and Role ID are required'
      });
      return;
    }

    // Check if role exists
    const role = await Role.findById(roleId);
    if (!role) {
      res.status(404).json({
        status: 'fail',
        message: 'Role not found'
      });
      return;
    }

    // Check if user exists and update their role
    const User = require('../models/User').default;
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({
        status: 'fail',
        message: 'User not found'
      });
      return;
    }

    user.roleId = roleId;
    await user.save();

    res.status(200).json({
      status: 'success',
      message: 'Role assigned to user successfully',
      data: {
        userId: user._id,
        roleId: role._id,
        roleName: role.name
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error assigning role to user',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get users by role
export const getUsersByRole = async (req: Request, res: Response): Promise<void> => {
  try {
    const { roleId } = req.params;

    // Check if role exists
    const role = await Role.findById(roleId);
    if (!role) {
      res.status(404).json({
        status: 'fail',
        message: 'Role not found'
      });
      return;
    }

    // Get users with this role
    const User = require('../models/User').default;
    const users = await User.find({ roleId }).populate('roleId', 'name description permissions');

    res.status(200).json({
      status: 'success',
      results: users.length,
      data: {
        role: role.name,
        users
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Error fetching users by role',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
