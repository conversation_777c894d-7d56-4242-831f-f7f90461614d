import { NextFunction, Request, Response } from "express";
export declare const SubscribeStripe: (req: Request, res: Response, next: NextFunction) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const successPayment: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const customerDetails: (req: Request, res: Response) => Promise<void>;
export declare const stripeWebhooks: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
