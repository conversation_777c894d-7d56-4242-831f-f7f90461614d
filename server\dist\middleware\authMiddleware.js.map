{"version": 3, "file": "authMiddleware.js", "sourceRoot": "", "sources": ["../../src/middleware/authMiddleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,gEAA+B;AAC/B,0DAAkC;AAElC,wDAAgC;AAOhC,wEAAwE;AACjE,MAAM,YAAY,GAAG,CAAO,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;;IACjG,IAAI,CAAC;QACD,oDAAoD;QACpD,IAAI,GAAG,CAAC,eAAe,EAAE,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;YACvD,IAAI,EAAE,CAAC;YACP,OAAO;QACX,CAAC;QAED,qDAAqD;QACrD,MAAM,KAAK,GAAG,MAAA,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,0CAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;YAEzD,gEAAgE;YAChE,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC9B,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChE,IAAI,CAAC;oBACD,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAClD,IAAI,IAAI,EAAE,CAAC;wBACP,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;wBACjD,GAAW,CAAC,IAAI,GAAG,IAAI,CAAC;wBACzB,IAAI,EAAE,CAAC;wBACP,OAAO;oBACX,CAAC;gBACL,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACX,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,GAAG,CAAC,CAAC;gBACtE,CAAC;YACL,CAAC;YAED,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,mCAAmC,EAAE,CAAC,CAAC;YACvE,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,0BAA0B;YAC1B,IAAI,OAAO,CAAC;YACZ,IAAI,CAAC;gBACD,4CAA4C;gBAC5C,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAoB,CAAuB,CAAC;gBACpF,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YACpD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACT,iDAAiD;gBACjD,IAAI,CAAC;oBACD,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAoB,CAAmB,CAAC;oBAChF,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;gBAChD,CAAC;gBAAC,OAAO,EAAE,EAAE,CAAC;oBACV,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBAC5C,CAAC;YACL,CAAC;YAED,wCAAwC;YACxC,MAAM,MAAM,GAAG,QAAQ,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAE/F,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;gBAC1D,OAAO;YACX,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAC;YAEhD,MAAM,IAAI,GAAG,MAAM,cAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEzC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;gBAC/C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;gBACpD,OAAO;YACX,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,yBAAyB;YACxB,GAAW,CAAC,IAAI,GAAG,IAAI,CAAC;YACzB,IAAI,EAAE,CAAC;QACX,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;YACnD,OAAO;QACX,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;QAC1D,OAAO;IACX,CAAC;AACL,CAAC,CAAA,CAAC;AAlFW,QAAA,YAAY,gBAkFvB;AAEF,sDAAsD;AAC/C,MAAM,cAAc,GAAG,CAAC,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;;IACxF,IAAI,CAAC;QACD,MAAM,KAAK,GAAG,MAAA,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,0CAAE,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;YACnD,OAAO;QACX,CAAC;QAED,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAoB,CAAuB,CAAC;QAC1F,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,IAAI,kBAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAW,CAAC;QACzE,IAAI,EAAE,CAAC;IACX,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;IACvD,CAAC;AACL,CAAC,CAAC;AAdW,QAAA,cAAc,kBAczB;AAEK,MAAM,UAAU,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAChF,IAAI,GAAG,CAAC,eAAe,EAAE,EAAE,CAAC;QACxB,IAAI,EAAE,CAAC;QACP,OAAO;IACX,CAAC;IACD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;AAC/E,CAAC,CAAC;AANW,QAAA,UAAU,cAMrB"}