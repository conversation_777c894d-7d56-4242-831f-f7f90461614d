import express from "express"
import {customerDetails,SubscribeStripe,stripeWebhooks,successPayment} from "../controller/stripeController"
import { corsDebugMiddleware, testCorsEndpoint } from "../utils/corsDebug"
import Stripe from "stripe"
import SubscribedPlan from '../models/SubscribedPlan';
import Owner from '../models/Owner';
import PricingPlan from '../models/PricingPlan';
import User from '../models/User';
// import { SubscribeStripe } from "../controller/stripeController";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string)

const router = express.Router()

// Add CORS debug middleware for development
if (process.env.NODE_ENV === 'development') {
  router.use(corsDebugMiddleware);
}

// Test endpoint to verify CORS
router.get('/test-cors', testCorsEndpoint);

// Regular JSON endpoints
router.get('/subscribe', SubscribeStripe as express.RequestHandler)
router.get('/success',successPayment as express.RequestHandler)
router.get('/customer/:customerId',customerDetails as express.RequestHandler)

// Stripe webhook endpoint - uses raw body stored by body-parser for signature verification
router.post('/',express.raw({ type: 'application/json' }), stripeWebhooks as express.RequestHandler)

// Check subscription status for a user (no auth required for testing)
router.get('/check-subscription/:ownerId', async (req: express.Request, res: express.Response) => {
    try {
        const { ownerId } = req.params;
        const owner = await Owner.findById(ownerId);
        const subscription = await SubscribedPlan.findOne({ ownerId });
        
        res.json({
            owner: {
                _id: owner?._id,
                subscribedPlanId: owner?.subscribedPlanId,
                email: owner?.email
            },
            subscription: subscription ? {
                _id: subscription._id,
                planId: subscription.planId,
                currentPlan: subscription.currentPlan,
                ownerId: subscription.ownerId
            } : null
        });
    } catch (error) {
        console.error('Error checking subscription:', error);
        res.status(500).json({ error: 'Failed to check subscription' });
    }
});

// Manual test endpoint for development - simulate webhook processing
router.post('/test-webhook', (async (req: express.Request, res: express.Response) => {
    if (process.env.NODE_ENV !== 'development') {
        return res.status(403).json({ error: 'Forbidden' });
    }
    try {
        const { sessionId } = req.body;
        if (!sessionId) {
            return res.status(400).json({ error: 'Session ID required' });
        }
        
        // Retrieve the session from Stripe
        const session = await stripe.checkout.sessions.retrieve(sessionId, {
            expand: ['subscription']
        });
        
        // Simulate webhook processing
        const metadata = session.metadata || {};
        const ownerId = metadata.ownerId;
        const planId = metadata.planId;
        
        if (ownerId && planId) {
            const existing = await SubscribedPlan.findOne({ ownerId });
            if (!existing) {
                const subscription = new SubscribedPlan({
                    planId,
                    ownerId,
                    currentPlan: planId,
                    previousPlans: []
                });
                await subscription.save();
                await Owner.findByIdAndUpdate(ownerId, { subscribedPlanId: subscription._id });
                res.json({ success: true, message: 'Subscription created manually', subscriptionId: subscription._id });
            } else {
                res.json({ success: true, message: 'Subscription already exists', subscriptionId: existing._id });
            }
        } else {
            res.status(400).json({ error: 'Missing metadata in session' });
        }
    } catch (error) {
        console.error('Manual webhook test error:', error);
        res.status(500).json({ error: 'Failed to process webhook manually' });
    }
}) as express.RequestHandler);

export default router;