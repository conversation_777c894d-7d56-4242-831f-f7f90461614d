{"402": [{"id": "o1", "text": "Do you rent or own your house?", "type": "dropdown", "options": ["Rent", "Own"], "required": true, "sectionId": "402A", "order": 1}, {"id": "o2", "text": "Do you have a mortgage?", "type": "boolean", "required": false, "sectionId": "402A", "order": 2, "dependsOn": {"questionId": "o1", "value": "Own"}}, {"id": "o3", "text": "What company holds your mortgage?", "type": "dropdown", "options": ["Rocket Mortgage", "Wells Fargo", "Chase", "U.S. Bank", "Bank of America", "LoanDepot", "Freedom Mortgage", "Guaranteed Rate", "Fairway Independent Mortgage", "PNC Mortgage", "Other"], "required": false, "sectionId": "402A", "order": 3, "dependsOn": {"questionId": "o2", "value": "yes"}}, {"id": "o4", "text": "What is your monthly mortgage payment?", "type": "currency", "required": false, "sectionId": "402A", "order": 4, "dependsOn": {"questionId": "o2", "value": "yes"}}, {"id": "o5", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "402A", "order": 5, "dependsOn": {"questionId": "o2", "value": "yes"}}, {"id": "o6", "text": "Username", "type": "text", "required": false, "sectionId": "402A", "order": 6, "dependsOn": {"questionId": "o5", "value": "yes"}}, {"id": "o7", "text": "Password", "type": "text", "required": false, "sectionId": "402A", "order": 7, "dependsOn": {"questionId": "o5", "value": "yes"}}, {"id": "o8", "text": "How much is your rent?", "type": "currency", "required": false, "sectionId": "402A", "order": 8, "dependsOn": {"questionId": "o1", "value": "Rent"}}, {"id": "o9", "text": "Who is your landlord/management company?", "type": "text", "required": false, "sectionId": "402A", "order": 9, "dependsOn": {"questionId": "o1", "value": "Rent"}}, {"id": "o10", "text": "Landlord/management company’s contact information", "type": "text", "required": false, "sectionId": "402A", "order": 10, "dependsOn": {"questionId": "o1", "value": "Rent"}}, {"id": "o11", "text": "What are the taxes on your home?", "type": "currency", "required": false, "sectionId": "402A", "order": 11}, {"id": "o12", "text": "Do you have an HOA?", "type": "boolean", "required": false, "sectionId": "402A", "order": 12}, {"id": "o13", "text": "What are your HOA fees?", "type": "currency", "required": false, "sectionId": "402A", "order": 13, "dependsOn": {"questionId": "o12", "value": "yes"}}, {"id": "o14", "text": "HOA contact information", "type": "text", "required": false, "sectionId": "402A", "order": 14, "dependsOn": {"questionId": "o12", "value": "yes"}}, {"id": "o15", "text": "Do you own or lease a car?", "type": "dropdown", "options": ["Own", "Lease"], "required": true, "sectionId": "402B", "order": 1}, {"id": "o16", "text": "Do you have a car loan?", "type": "boolean", "required": false, "sectionId": "402B", "order": 2, "dependsOn": {"questionId": "o15", "value": "Own"}}, {"id": "o17", "text": "What company holds your car loan?", "type": "dropdown", "options": ["Ally Financial", "Capital One Auto Finance", "Wells Fargo Auto", "Chase Auto", "Bank of America", "Toyota Financial Services", "Ford Credit", "Honda Financial Services", "GM Financial", "USAA", "Other"], "required": false, "sectionId": "402B", "order": 3, "dependsOn": {"questionId": "o16", "value": "yes"}}, {"id": "o18", "text": "What is your monthly car loan/lease payment?", "type": "currency", "required": false, "sectionId": "402B", "order": 4, "dependsOn": {"questionId": "o16", "value": "yes"}}, {"id": "o19", "text": "Do you pay your car loan/lease online?", "type": "boolean", "required": false, "sectionId": "402B", "order": 5, "dependsOn": {"questionId": "o16", "value": "yes"}}, {"id": "o20", "text": "Username", "type": "text", "required": false, "sectionId": "402B", "order": 6, "dependsOn": {"questionId": "o19", "value": "yes"}}, {"id": "o21", "text": "Password", "type": "text", "required": false, "sectionId": "402B", "order": 7, "dependsOn": {"questionId": "o19", "value": "yes"}}, {"id": "o22", "text": "Where is the title located?", "type": "text", "required": false, "sectionId": "402B", "order": 8, "dependsOn": {"questionId": "o19", "value": "no"}}, {"id": "o23", "text": "What company do you use for car insurance?", "type": "dropdown", "options": ["State Farm", "GEICO", "Progressive", "Allstate", "USAA", "Liberty Mutual", "Farmers Insurance", "Nationwide", "American Family Insurance", "Travelers", "Other"], "required": true, "sectionId": "402B", "order": 9}, {"id": "o24", "text": "What is your car insurance account number?", "type": "text", "required": false, "sectionId": "402B", "order": 10}, {"id": "o25", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "402B", "order": 11}, {"id": "o26", "text": "Username", "type": "text", "required": false, "sectionId": "402B", "order": 12, "dependsOn": {"questionId": "o25", "value": "yes"}}, {"id": "o27", "text": "Password", "type": "text", "required": false, "sectionId": "402B", "order": 13, "dependsOn": {"questionId": "o25", "value": "yes"}}, {"id": "o28", "text": "What is your driver’s license number?", "type": "text", "required": true, "sectionId": "402C", "order": 1}]}