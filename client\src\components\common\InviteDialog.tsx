import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { useAppDispatch, useAppSelector } from "@/store";
import { inviteUser, resetInvite } from "@/store/slices/inviteSlice";
import { InviteForm } from "@/types/invite.types";
import { UserPlus } from 'lucide-react';

interface InviteDialogProps {
  onInviteSent?: () => void;
}

export function InviteDialog({ onInviteSent }: InviteDialogProps) {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState<InviteForm>({
    name: "",
    email: "",
    relation: "",
    phone: "",
  });

  const dispatch = useAppDispatch();
  const { loading, error, success } = useAppSelector((state) => state.invite);

  useEffect(() => {
    if (success) {
      toast.success("Invitation sent successfully!");
      setOpen(false);
      setFormData({ name: "", email: "", relation: "", phone: "" });
      dispatch(resetInvite());
      onInviteSent?.();
    }
    if (error) {
      toast.error(error);
      dispatch(resetInvite());
    }
  }, [success, error, dispatch, onInviteSent]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleRelationChange = (value: string) => {
    setFormData((prev) => ({ ...prev, relation: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(inviteUser(formData));
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="font-medium flex items-center gap-2">
          <UserPlus className="w-5 h-5 mr-2" />
          Invite
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] w-[95vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl sm:text-2xl">Invite User</DialogTitle>
          <DialogDescription className="text-sm sm:text-base">
            Send an invitation to join your Heirkey account. They will receive an email with instructions.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
              <Label htmlFor="name" className="text-sm sm:text-base sm:text-right">
                Name
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="col-span-1 sm:col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
              <Label htmlFor="email" className="text-sm sm:text-base sm:text-right">
                Email
              </Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                className="col-span-1 sm:col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
              <Label htmlFor="relation" className="text-sm sm:text-base sm:text-right">
                Relation
              </Label>
              <Select
                value={formData.relation}
                onValueChange={handleRelationChange}
                required
              >
                <SelectTrigger className="col-span-1 sm:col-span-3">
                  <SelectValue placeholder="Select relation" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="family">Family</SelectItem>
                  <SelectItem value="nominee">Nominee</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-4 items-center gap-2 sm:gap-4">
              <Label htmlFor="phone" className="text-sm sm:text-base sm:text-right">
                Phone
              </Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleChange}
                className="col-span-1 sm:col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={loading} className="w-full sm:w-auto">
              {loading ? "Sending..." : "Send Invitation"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default InviteDialog; 