import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

const pricingPlanSchema = Joi.object({
  type: Joi.string()
    .valid('temporary_key', 'spare_key', 'all_access_key')
    .required()
    .messages({
      'any.only': 'Type must be one of: temporary_key, spare_key, all_access_key'
    }),
  price: Joi.number()
    .min(0)
    .required()
    .messages({
      'number.min': 'Price must be 0 or greater'
    }),
  displayPrice: Joi.string()
    .required()
    .min(1)
    .messages({
      'string.min': 'Display price cannot be empty'
    }),
  tagline: Joi.string()
    .required()
    .min(1)
    .messages({
      'string.min': 'Tagline cannot be empty'
    }),
  features: Joi.array()
    .items(Joi.string().min(1))
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one feature is required',
      'string.min': 'Features cannot be empty strings'
    }),
  duration: Joi.number()
    .custom((value, helpers) => {
      // Duration must be -1 (infinite) or positive number
      if (value === -1 || value > 0) {
        return value;
      }
      return helpers.error('any.invalid');
    })
    .optional()
    .messages({
      'any.invalid': 'Duration must be -1 (infinite) or a positive number of months'
    }),
  categorylimit: Joi.number()
    .custom((value, helpers) => {
      // Category limit must be -1 (unlimited) or positive number
      if (value === -1 || value > 0) {
        return value;
      }
      return helpers.error('any.invalid');
    })
    .optional()
    .messages({
      'any.invalid': 'Category limit must be -1 (unlimited) or a positive number'
    }),
  active: Joi.boolean()
    .optional()
    .default(true)
});

const updatePricingPlanSchema = Joi.object({
  type: Joi.string()
    .valid('temporary_key', 'spare_key', 'all_access_key')
    .optional()
    .messages({
      'any.only': 'Type must be one of: temporary_key, spare_key, all_access_key'
    }),
  price: Joi.number()
    .min(0)
    .optional()
    .messages({
      'number.min': 'Price must be 0 or greater'
    }),
  displayPrice: Joi.string()
    .optional()
    .min(1)
    .messages({
      'string.min': 'Display price cannot be empty'
    }),
  tagline: Joi.string()
    .optional()
    .min(1)
    .messages({
      'string.min': 'Tagline cannot be empty'
    }),
  features: Joi.array()
    .items(Joi.string().min(1))
    .min(1)
    .optional()
    .messages({
      'array.min': 'At least one feature is required',
      'string.min': 'Features cannot be empty strings'
    }),
  duration: Joi.number()
    .custom((value, helpers) => {
      // Duration must be -1 (infinite) or positive number
      if (value === -1 || value > 0) {
        return value;
      }
      return helpers.error('any.invalid');
    })
    .optional()
    .messages({
      'any.invalid': 'Duration must be -1 (infinite) or a positive number of months'
    }),
  categorylimit: Joi.number()
    .custom((value, helpers) => {
      // Category limit must be -1 (unlimited) or positive number
      if (value === -1 || value > 0) {
        return value;
      }
      return helpers.error('any.invalid');
    })
    .optional()
    .messages({
      'any.invalid': 'Category limit must be -1 (unlimited) or a positive number'
    }),
  active: Joi.boolean()
    .optional()
});

export const validatePricingPlan = (req: Request, res: Response, next: NextFunction): void => {
  const { error } = pricingPlanSchema.validate(req.body);

  if (error) {
    res.status(400).json({
      message: 'Validation error',
      details: error.details.map(detail => detail.message)
    });
    return;
  }

  next();
};

export const validatePricingPlanUpdate = (req: Request, res: Response, next: NextFunction): void => {
  const { error } = updatePricingPlanSchema.validate(req.body);

  if (error) {
    res.status(400).json({
      message: 'Validation error',
      details: error.details.map(detail => detail.message)
    });
    return;
  }

  next();
};
