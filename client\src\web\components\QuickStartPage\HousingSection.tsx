import React, { useEffect, useState } from 'react';
import { Formik, Form, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import futureCategoryService from '@/services/futureCategoryService';
import quickStartService from '@/services/quickStartService';
import userInputService, { generateObjectId } from '@/services/userInputService';
import QuickStartCardWrapper from '@/mobile/components/QuickStartPage/QuickStartCardWrapper';

// Define the housing questions structure
interface HousingQuestion {
  id: string;
  text: string;
  type: 'text' | 'boolean' | 'choice' | 'number';
  required?: boolean;
  dependsOn?: {
    question: string;
    value: string;
  };
  options?: string[];
}

const housingQuestions: HousingQuestion[] = [
  {
    id: 'housing_status',
    text: 'Do you rent or own your house?',
    type: 'choice',
    required: true,
    options: ['own', 'rent']
  },
  {
    id: 'has_mortgage',
    text: 'Do you have a mortgage?',
    type: 'choice',
    dependsOn: {
      question: 'housing_status',
      value: 'own'
    },
    options: ['yes', 'no']
  },
  {
    id: 'mortgage_company',
    text: 'What company holds your mortgage?',
    type: 'text',
    dependsOn: {
      question: 'has_mortgage',
      value: 'yes'
    }
  },
  {
    id: 'monthly_mortgage_payment',
    text: 'What is your monthly mortgage payment?',
    type: 'text',
    dependsOn: {
      question: 'has_mortgage',
      value: 'yes'
    }
  },
  {
    id: 'monthly_rent',
    text: 'How much is your rent?',
    type: 'text',
    dependsOn: {
      question: 'housing_status',
      value: 'rent'
    }
  },
  {
    id: 'landlord_company',
    text: 'Who is your landlord/management company?',
    type: 'text',
    dependsOn: {
      question: 'housing_status',
      value: 'rent'
    }
  },
  {
    id: 'landlord_contact',
    text: 'What is your landlord/management company\'s contact information?',
    type: 'text',
    dependsOn: {
      question: 'housing_status',
      value: 'rent'
    }
  }
];

const HousingSection: React.FC = () => {
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string>>({});
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const { user } = useAuth();

  // Fetch existing housing data on mount
  useEffect(() => {
    const fetchHousingData = async () => {
      if (user?.id) {
        try {
          const housingData = await futureCategoryService.getCategoryData(user.id, 'housing_ownership');
          if (housingData.length > 0) {
            const answers: Record<string, string> = {};
            housingData.forEach(item => {
              answers[item.fieldId] = item.answer;
            });
            setSavedAnswers(answers);
          }
        } catch (error) {
          console.error('Error fetching housing data:', error);
        }
      }
      setIsDataLoaded(true);
    };

    fetchHousingData();
  }, [user]);

  // Build validation schema
  const buildValidationSchema = () => {
    const schema: Record<string, any> = {};
    
    housingQuestions.forEach(question => {
      if (question.required) {
        schema[question.id] = Yup.string().required('This field is required');
      }
    });

    return Yup.object().shape(schema);
  };

  // Generate initial values
  const generateInitialValues = () => {
    const values: Record<string, string> = {};
    housingQuestions.forEach(question => {
      values[question.id] = savedAnswers[question.id] || '';
    });
    return values;
  };

  // Check if a question should be shown based on dependencies
  const shouldShowQuestion = (question: HousingQuestion, values: Record<string, string>): boolean => {
    if (!question.dependsOn) return true;
    
    const dependentValue = values[question.dependsOn.question];
    return dependentValue === question.dependsOn.value;
  };

  // Handle form submission
  const handleSubmit = async (
    values: Record<string, string>,
    { setSubmitting }: FormikHelpers<Record<string, string>>
  ) => {
    try {
      if (!user || !user.id) throw new Error('You must be logged in to save answers');

      // Get the current housing status to determine which questions are relevant
      const housingStatus = values.housing_status;
      
      // Filter questions based on current housing status and dependencies
      const relevantQuestions = housingQuestions.filter(question => {
        // Always include the main housing status question
        if (question.id === 'housing_status') return true;
        
        // Check dependencies
        if (question.dependsOn) {
          const dependentValue = values[question.dependsOn.question];
          return dependentValue === question.dependsOn.value;
        }
        
        return true;
      });

      // Filter values to only include relevant questions
      const relevantValues = relevantQuestions.reduce((acc, question) => {
        const value = values[question.id];
        if (value && value.trim() !== '') {
          acc[question.id] = value;
        }
        return acc;
      }, {} as Record<string, string>);

      if (Object.keys(relevantValues).length === 0) {
        console.log('No relevant data to save');
        setSubmitting(false);
        return;
      }

      // Save directly to future category using userInputService
      const ownerId = await getCachedOwnerIdFromUser(user);
      if (!ownerId) {
        throw new Error('Owner ID not found');
      }

      // Check if we already have housing data
      const existingInputs = await userInputService.getUserInputsByOwnerAndCategory(ownerId, '999');

      const answersBySection = [{
        originalSectionId: '999A',
        isCompleted: true,
        answers: Object.entries(relevantValues).map(([fieldId, answer], index) => ({
          index,
          originalQuestionId: fieldId,
          question: housingQuestions.find(q => q.id === fieldId)?.text || fieldId,
          type: 'text',
          answer: answer
        }))
      }];

      const userInputData = {
        userId: user.id,
        ownerId,
        categoryId: generateObjectId(), // Generate proper ObjectId
        originalCategoryId: '999', // Future category ID for housing
        subCategoryId: generateObjectId(), // Generate proper ObjectId
        originalSubCategoryId: '999',
        answersBySection
      };

      if (existingInputs && existingInputs.length > 0) {
        // Update existing data - this will replace all answers with only the relevant ones
        await userInputService.updateUserInput(existingInputs[0]._id!, userInputData);
        console.log('Housing data updated successfully with relevant questions only');
      } else {
        // Create new data
        await userInputService.createUserInput(userInputData);
        console.log('Housing data saved successfully');
      }

      // Refresh the saved answers
      const housingData = await futureCategoryService.getCategoryData(user.id, 'housing_ownership');
      if (housingData.length > 0) {
        const answers: Record<string, string> = {};
        housingData.forEach(item => {
          answers[item.fieldId] = item.answer;
        });
        setSavedAnswers(answers);
      }

      setSubmitting(false);
      // You can add a success toast here if you have a toast system
      // alert('Housing data saved successfully!');
    } catch (error) {
      console.error('Error saving housing data:', error);
      setSubmitting(false);
      // alert('Error saving housing data. Please try again.');
    }
  };

  // Show loading until data is loaded
  if (!isDataLoaded) {
    return <div className="flex justify-center items-center h-40">Loading...</div>;
  }

  const validationSchema = buildValidationSchema();
  const initialValues = generateInitialValues();

  return (
    <QuickStartCardWrapper>
      <div className="mb-6">
        <p className="text-sm text-gray-600">
          Help us understand your housing situation for better estate planning.
        </p>  
      </div>
      <Formik
        key={`${isDataLoaded}-${JSON.stringify(savedAnswers)}`}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize={true}
      >
        {({ values, isSubmitting, isValid, dirty, setFieldValue, errors, touched }) => {
          // Handle dependent field visibility
          const visibleQuestions = housingQuestions.filter(question => 
            shouldShowQuestion(question, values)
          );

          return (
            <Form>
              <div className="space-y-6">
                {visibleQuestions.map((question) => (
                  <div key={question.id} className="space-y-2">
                    <Label htmlFor={question.id} className="text-sm font-medium text-gray-700">
                      {question.text}
                      {question.required && <span className="text-red-500 ml-1">*</span>}
                    </Label>

                    {question.type === 'choice' && question.options ? (
                      <RadioGroup
                        value={values[question.id]}
                        onValueChange={(value) => setFieldValue(question.id, value)}
                        className="space-y-2"
                      >
                        {question.options.map((option) => (
                          <div key={option} className="flex items-center space-x-2">
                            <RadioGroupItem value={option} id={`${question.id}-${option}`} />
                            <Label 
                              htmlFor={`${question.id}-${option}`} 
                              className="text-sm capitalize"
                            >
                              {option}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    ) : (
                      <Input
                        id={question.id}
                        type={question.type === 'number' ? 'number' : 'text'}
                        value={values[question.id]}
                        onChange={(e) => setFieldValue(question.id, e.target.value)}
                        placeholder={`Enter your ${question.text.toLowerCase()}`}
                        className="w-full"
                      />
                    )}

                    {errors[question.id] && touched[question.id] && (
                      <p className="text-sm text-red-500">{errors[question.id]}</p>
                    )}
                  </div>
                ))}
              </div>

              <div className="mt-8 flex justify-end">
                <Button
                  type="submit"
                  disabled={isSubmitting || !isValid || !dirty}
                  className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                >
                  {isSubmitting ? 'Saving...' : 'Save & Continue'}
                </Button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </QuickStartCardWrapper>
  );
};

export default HousingSection; 