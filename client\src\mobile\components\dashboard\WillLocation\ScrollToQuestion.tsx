import { useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Question } from './FormFields';
import { useAppDispatch } from '@/store/hooks';
import { saveUserInput, updateUserInput } from '@/store/slices/importantContactsSlice';
import { useAuth } from '@/contexts/AuthContext';

interface ScrollToQuestionProps {
  questions: Question[];
  children: (refs: Record<string, HTMLDivElement | null>) => React.ReactNode;
  existingInputId?: string | null;
  onSave?: () => void;
  isReviewMode?: boolean;
}

/**
 * A utility component that handles scrolling to a specific contact question based on URL query parameters.
 * Also handles real-time saving of answers and review mode functionality.
 *
 * Usage:
 * <ScrollToQuestion 
 *   questions={questions}
 *   existingInputId={existingInputId}
 *   onSave={handleSave}
 *   isReviewMode={isReviewMode}
 * >
 *   {(refs) => (
 *     <>
 *       {questions.map(question => (
 *         <div key={question.id} ref={el => refs[question.id] = el}>
 *           <ContactQuestionItem 
 *             question={question} 
 *             values={values}
 *             existingInputId={existingInputId}
 *             onSave={handleSave}
 *           />
 *         </div>
 *       ))}
 *     </>
 *   )}
 * </ScrollToQuestion>
 */
const ScrollToQuestion = ({ 
  questions = [], 
  children,
  existingInputId,
  onSave,
  isReviewMode = false
}: ScrollToQuestionProps) => {
  const location = useLocation();
  const questionRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const [savedStates, setSavedStates] = useState<Record<string, boolean>>({});
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');

  // Scroll to the target question when the component is fully loaded
  useEffect(() => {
    if (isReviewMode && targetQuestionId && questionRefs.current[targetQuestionId]) {
      // Add a small delay to ensure the DOM is fully rendered
      setTimeout(() => {
        questionRefs.current[targetQuestionId]?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // Add a highlight effect to the target question
        const element = questionRefs.current[targetQuestionId];
        if (element) {
          element.classList.add('bg-yellow-50');
          element.classList.add('transition-colors');
          element.classList.add('duration-1000');

          // Remove the highlight after a few seconds
          setTimeout(() => {
            element.classList.remove('bg-yellow-50');
          }, 3000);
        }
      }, 500);
    }
  }, [isReviewMode, targetQuestionId, questions]);

  // Handle real-time saving
  const handleSave = async (questionId: string, value: any) => {
    if (!user?.id) return;

    try {
      const question = questions.find(q => q.id === questionId);
      if (!question) return;

      const answer = {
        index: question.order,
        originalQuestionId: question.id,
        question: question.text,
        type: question.type,
        answer: value
      };

      const formattedAnswersBySection = [{
        originalSectionId: question.sectionId,
        isCompleted: true,
        answers: [answer]
      }];

      const userData = {
        userId: user.id,
        categoryId: '5',
        originalCategoryId: '207',
        subCategoryId: question.sectionId,
        originalSubCategoryId: question.sectionId,
        answersBySection: formattedAnswersBySection
      };

      if (existingInputId) {
        await dispatch(updateUserInput({ id: existingInputId, userData })).unwrap();
      } else {
        await dispatch(saveUserInput(userData)).unwrap();
      }

      // Update saved state
      setSavedStates(prev => ({
        ...prev,
        [questionId]: true
      }));

      if (onSave) {
        onSave();
      }
    } catch (error) {
      console.error('Error saving answer:', error);
      setSavedStates(prev => ({
        ...prev,
        [questionId]: false
      }));
    }
  };

  // Ensure we're passing a valid object even if questions is empty
  return children(questionRefs.current);
};

export default ScrollToQuestion; 