{"401": [{"id": "i1", "text": "Do you have home insurance?", "type": "boolean", "required": false, "sectionId": "401A", "order": 1}, {"id": "i2", "text": "What is the name of your home insurance company?", "type": "dropdown", "required": false, "sectionId": "401A", "order": 2, "options": ["State Farm", "Allstate", "Liberty Mutual", "USAA", "Progressive", "Farmers Insurance", "Nationwide", "American Family Insurance", "Travelers", "<PERSON><PERSON>", "Other"], "dependsOn": {"questionId": "i1", "value": "yes"}}, {"id": "i3", "text": "What is your Account Number?", "type": "text", "required": false, "sectionId": "401A", "order": 3}, {"id": "i4", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "401A", "order": 4}, {"id": "i5", "text": "Username", "type": "text", "required": false, "sectionId": "401A", "order": 5, "dependsOn": {"questionId": "i4", "value": "yes"}}, {"id": "i6", "text": "Password", "type": "text", "required": false, "sectionId": "401A", "order": 6, "dependsOn": {"questionId": "i4", "value": "yes"}}, {"id": "i7", "text": "Please list any other information about your home insurance, like extra waivers or coverage.", "type": "text", "required": false, "sectionId": "401A", "order": 7}, {"id": "i8", "text": "Do you have medical insurance?", "type": "boolean", "required": false, "sectionId": "401B", "order": 8}, {"id": "i9", "text": "What is the name of your medical insurance company?", "type": "dropdown", "required": false, "sectionId": "401B", "order": 9, "options": ["UnitedHealthcare", "Blue Cross Blue Shield", "Kaiser Permanente", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Humana", "Molina Healthcare", "Centene", "Anthem", "Health Care Service Corporation (HCSC)", "Other"], "dependsOn": {"questionId": "i8", "value": "yes"}}, {"id": "i10", "text": "What is your Account Number?", "type": "text", "required": false, "sectionId": "401B", "order": 10}, {"id": "i11", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "401B", "order": 11}, {"id": "i12", "text": "Username", "type": "text", "required": false, "sectionId": "401B", "order": 12, "dependsOn": {"questionId": "i11", "value": "yes"}}, {"id": "i13", "text": "Password", "type": "text", "required": false, "sectionId": "401B", "order": 13, "dependsOn": {"questionId": "i11", "value": "yes"}}, {"id": "i14", "text": "Are you on Medicare?", "type": "boolean", "required": false, "sectionId": "401B", "order": 14}, {"id": "i15", "text": "What is your Medicare Number?", "type": "text", "required": false, "sectionId": "401B", "order": 15, "dependsOn": {"questionId": "i14", "value": "yes"}}, {"id": "i16", "text": "Do you have a Medicare Supplement Plan?", "type": "boolean", "required": false, "sectionId": "401B", "order": 16, "dependsOn": {"questionId": "i14", "value": "yes"}}, {"id": "i17", "text": "What is the name of your Medicare Supplement company?", "type": "dropdown", "required": false, "sectionId": "401B", "order": 17, "options": ["UnitedHealthcare", "Mutual of Omaha", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Humana", "<PERSON>them Blue Cross Blue Shield", "Blue Cross Blue Shield Association", "ManhattanLife", "Americo", "Bankers Fidelity", "Other"], "dependsOn": {"questionId": "i16", "value": "yes"}}, {"id": "i18", "text": "What is your Plan’s Number?", "type": "text", "required": false, "sectionId": "401B", "order": 18, "dependsOn": {"questionId": "i16", "value": "yes"}}, {"id": "i19", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "401B", "order": 19, "dependsOn": {"questionId": "i16", "value": "yes"}}, {"id": "i20", "text": "Username", "type": "text", "required": false, "sectionId": "401B", "order": 20, "dependsOn": {"questionId": "i19", "value": "yes"}}, {"id": "i21", "text": "Password", "type": "text", "required": false, "sectionId": "401B", "order": 21, "dependsOn": {"questionId": "i19", "value": "yes"}}, {"id": "i22", "text": "Do you have a Part D prescription plan?", "type": "boolean", "required": false, "sectionId": "401B", "order": 22}, {"id": "i23", "text": "What company provides your Part D prescription plan?", "type": "dropdown", "required": false, "sectionId": "401B", "order": 23, "options": ["UnitedHealthcare", "Humana", "WellCare", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Blue Cross Blue Shield", "SilverScript (now part of Aetna)", "Mutual of Omaha", "Express Scripts", "Elixir Insurance"], "dependsOn": {"questionId": "i22", "value": "yes"}}, {"id": "i24", "text": "What is your plan’s number?", "type": "text", "required": false, "sectionId": "401B", "order": 24, "dependsOn": {"questionId": "i22", "value": "yes"}}, {"id": "i25", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "401B", "order": 25, "dependsOn": {"questionId": "i22", "value": "yes"}}, {"id": "i26", "text": "Username", "type": "text", "required": false, "sectionId": "401B", "order": 26, "dependsOn": {"questionId": "i25", "value": "yes"}}, {"id": "i27", "text": "Password", "type": "text", "required": false, "sectionId": "401B", "order": 27, "dependsOn": {"questionId": "i25", "value": "yes"}}, {"id": "i28", "text": "Do you have life insurance?", "type": "boolean", "required": false, "sectionId": "401C", "order": 28}, {"id": "i29", "text": "What is the name of your life insurance company?", "type": "dropdown", "required": false, "sectionId": "401C", "order": 29, "options": ["State Farm", "Northwestern Mutual", "New York Life", "MetLife", "Prudential", "MassMutual", "Guardian Life", "Lincoln Financial Group", "Pacific Life", "Mutual of Omaha"], "dependsOn": {"questionId": "i28", "value": "yes"}}, {"id": "i30", "text": "What is your Account Number?", "type": "text", "required": false, "sectionId": "401C", "order": 30}, {"id": "i31", "text": "Do you pay online?", "type": "boolean", "required": false, "sectionId": "401C", "order": 31}, {"id": "i32", "text": "Username", "type": "text", "required": false, "sectionId": "401C", "order": 32, "dependsOn": {"questionId": "i31", "value": "yes"}}, {"id": "i33", "text": "Password", "type": "text", "required": false, "sectionId": "401C", "order": 33, "dependsOn": {"questionId": "i31", "value": "yes"}}, {"id": "i34", "text": "Please list any additional information about your life insurance, such as extra waivers or coverage.", "type": "text", "required": false, "sectionId": "401C", "order": 34}]}