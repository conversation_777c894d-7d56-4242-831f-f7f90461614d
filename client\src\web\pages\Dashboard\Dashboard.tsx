import { Link } from 'react-router-dom'
import { Avatar } from '@radix-ui/react-avatar'
import { useEffect, useState, useRef, useCallback } from 'react'
import AppHeader from '@/web/components/Layout/AppHeader'
import Footer from '@/web/components/Layout/Footer'
import SearchPanel from '@/web/pages/Global/SearchPanel'
import RequestList from '@/components/RequestCategories/RequestList'
import home from '@/assets/global/category/home.jpg'
import documents from '@/assets/global/category/document.jpg'
import will from '@/assets/global/category/will.jpg'
import funeral from '@/assets/global/category/funeral.jpg'
import contact from '@/assets/global/category/contact.jpg'
import socialMedia from '@/assets/global/category/socialMedia.jpg'
import quickStart from '@/assets/global/category/QuickStart.jpg'
import insurance from '@/assets/global/category/Insurance.jpg'
import financialPlans from '@/assets/global/category/Financial.jpg'
import ownership from '@/assets/global/category/Ownership.jpg'
import banking from '@/assets/global/category/Banking.jpg'
import subscription from '@/assets/global/category/OTT.jpg'
import passwords from '@/assets/global/category/Passwords.jpg'
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg'
import { useAuth } from '@/contexts/AuthContext'
import userInputService from '@/services/userInputService'
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils'
import { useAppSelector, useAppDispatch } from '@/store/hooks'
import { selectDynamicOverallProgress, fetchUserInputs as fetchHomeInstructionsUserInputs } from '@/store/slices/homeInstructionsSlice'
import { selectQuickStartProgress, selectQuickStartLoading, loadQuickStartData } from '@/store/slices/quickStartSlice'
import { selectProgressStats as selectInsuranceProgress, fetchUserInputs as fetchInsuranceUserInputs } from '@/store/slices/insuranceSlice'
import { selectProgressStats as selectFinancialPlansProgress, fetchUserInputs as fetchFinancialPlansUserInputs } from '@/store/slices/financialPlansSlice'
import { selectProgressStats as selectOwnershipInfoProgress, fetchUserInputs as fetchOwnershipInfoUserInputs } from '@/store/slices/ownershipInfoSlice'
import { selectProgressStats as selectBankingProgressStats, fetchUserInputs as fetchBankingUserInputs } from '@/store/slices/bankingInformationSlice'
import { selectDynamicOverallProgress as selectSubscriptionProgress, fetchUserInputs as fetchSubscriptionUserInputs } from '@/store/slices/subscriptionCategorySlice'
import { selectProgressStats as selectPasswordsProgress, fetchUserInputs as fetchPasswordsUserInputs } from '@/store/slices/passwordsSlice'
import { useAccessControl } from '@/hooks/useAccessControl'
import { CATEGORIES } from '@/constants/categories'
import React from 'react'
import { useToast } from '@/contexts/ToastContext'
import { Lock } from 'lucide-react'

// Define interfaces for the data structure
interface CategoryStat {
  categoryId: string;
  answeredQuestions: number;
}

const CategoryCard = ({
  title,
  imageSrc,
  questionCount,
  path,
  isCompleted = false,
  locked = false,
  onLockedClick,
  isLoading = false
}: {
  title: string;
  imageSrc: string;
  questionCount: string;
  path: string;
  isCompleted?: boolean;
  locked?: boolean;
  onLockedClick?: () => void;
  isLoading?: boolean;
}) => {
  const handleClick = (e: React.MouseEvent) => {
    if (locked) {
      e.preventDefault();
      if (onLockedClick) onLockedClick();
    }
  };
  return (
    <div className="relative flex flex-col rounded-lg overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-all">
      {isCompleted && !locked && (
        <div className="absolute top-2 right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full z-10">
          Completed
        </div>
      )}
      {locked && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex flex-col items-center justify-center z-20 mb-20 pointer-events-none">
          <Lock className="w-8 h-8 text-white mb-1" />
          <span className="text-white font-semibold">Locked</span>
        </div>
      )}
      <Link
        to={locked ? '#' : path}
        className={
          locked
            ? 'block cursor-not-allowed'
            : 'block'
        }
        onClick={handleClick}
      >
        <img
          src={imageSrc}
          alt={title}
          className="w-full h-40 object-cover"
        />
        <div className="p-4">
          <h3 className="font-medium">{title}</h3>
          {isLoading ? (
            <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
          ) : (
            <span className={`text-sm transition-all duration-300 ${isCompleted ? 'text-green-500' : 'text-blue-400'}`}>{questionCount}</span>
          )}
        </div>
      </Link>
    </div>
  )
}

const Dashboard = () => {
  const { user } = useAuth();
  const homeInstructionsProgress = useAppSelector(selectDynamicOverallProgress);
  const quickStartProgress = useAppSelector(selectQuickStartProgress);
  const quickStartLoading = useAppSelector(selectQuickStartLoading);
  const insuranceProgress = useAppSelector(selectInsuranceProgress);
  const financialPlansProgress = useAppSelector(selectFinancialPlansProgress);
  const ownershipInfoProgress = useAppSelector(selectOwnershipInfoProgress);
  const bankingProgress = useAppSelector(selectBankingProgressStats);
  const subscriptionProgress = useAppSelector(selectSubscriptionProgress);
  const passwordsProgress = useAppSelector(selectPasswordsProgress);
  const dispatch = useAppDispatch();
  const [categoryProgress, setCategoryProgress] = useState<Record<string, { answered: number, total: number }>>({
    homedocuments: { answered: 0, total: 64 },
    willinstructions: { answered: 0, total: 8 },
    funeralarrangements: { answered: 0, total: 15 },
    importantcontacts: { answered: 0, total: 4 },
    socialmedia: { answered: 0, total: 22 },
    quickstart: { answered: 0, total: 10 },
    insurance: { answered: 0, total: 34 },
    financialplans: { answered: 0, total: 18 },
    bankinginformation: { answered: 0, total: 6 },
    subscription: { answered: 0, total: 16 }
  });
  const [isLoading, setIsLoading] = useState(true);
  const dataFetchedRef = useRef(false);
  const quickStartDataLoadedRef = useRef(false);
  const { canAccessCategory, userAccess } = useAccessControl();
  const { toast } = useToast();

  useEffect(() => {
    if (user?.id && !quickStartDataLoadedRef.current) {
      dispatch(loadQuickStartData(user.id));
      quickStartDataLoadedRef.current = true;
    }
  }, [dispatch, user?.id]); // Only depend on user.id, not the entire user object

  // Fetch Home Instructions user inputs for progress on dashboard
  useEffect(() => {
    const fetchHomeInstructions = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchHomeInstructionsUserInputs(ownerId));
          } else {
            dispatch(fetchHomeInstructionsUserInputs(user.id));
          }
        } catch (error) {
          console.error('Error fetching owner ID for Home Instructions:', error);
          dispatch(fetchHomeInstructionsUserInputs(user.id));
        }
      }
    };
    fetchHomeInstructions();
  }, [dispatch, user]);

  // Fetch Insurance data
  useEffect(() => {
    const fetchInsurance = async () => {
      if (user && user.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchInsuranceUserInputs(ownerId));
          } else {
            dispatch(fetchInsuranceUserInputs(user.id));
          }
        } catch (error) {
          console.error('Error fetching owner ID for Insurance:', error);
          dispatch(fetchInsuranceUserInputs(user.id));
        }
      }
    };
    fetchInsurance();
  }, [dispatch, user]);

  // Fetch Financial Plans data
  useEffect(() => {
    const fetchFinancialPlans = async () => {
      if (user && user.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchFinancialPlansUserInputs(ownerId));
          } else {
            dispatch(fetchFinancialPlansUserInputs(user.id));
          }
        } catch (error) {
          console.error('Error fetching owner ID for Financial Plans:', error);
          dispatch(fetchFinancialPlansUserInputs(user.id));
        }
      }
    };
    fetchFinancialPlans();
    }, [dispatch, user]);

  // Fetch Ownership Information data
  useEffect(() => {
    const fetchOwnershipInfo = async () => {
      if (user && user.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchOwnershipInfoUserInputs(ownerId));
          } else {
            dispatch(fetchOwnershipInfoUserInputs(user.id));
          }
        } catch (error) {
          console.error('Error fetching owner ID for Ownership Information:', error);
          dispatch(fetchOwnershipInfoUserInputs(user.id));
        }
      }
    };
    fetchOwnershipInfo();
  }, [dispatch, user]);

  // Fetch Banking Information user inputs for progress on dashboard
  useEffect(() => {
    const fetchBankingInformation = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchBankingUserInputs(ownerId));
          }
        } catch (error) {
          console.error('Error fetching banking information:', error);
        }
      }
    };
    fetchBankingInformation();
  }, [dispatch, user]);

  // Fetch subscription data
  useEffect(() => {
    const fetchSubscriptionInformation = async () => {
      if (user) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchSubscriptionUserInputs(ownerId));
          }
        } catch (error) {
          console.error('Error fetching subscription information:', error);
        }
      }
    };
    fetchSubscriptionInformation();
  }, [dispatch, user]);

  // Fetch passwords data
  useEffect(() => {
    const fetchPasswordsInformation = async () => {
      if (user) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchPasswordsUserInputs(ownerId));
          }
        } catch (error) {
          console.error('Error fetching passwords information:', error);
        }
      }
    };
    fetchPasswordsInformation();
  }, [dispatch, user]);

  // Function to fetch progress data - wrapped in useCallback to prevent unnecessary re-renders
  const fetchProgressData = useCallback(async () => {
    if (!user || !user.id || dataFetchedRef.current) {
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    try {
      const ownerId = await getCachedOwnerIdFromUser(user);
      if (!ownerId) {
        setIsLoading(false);
        return;
      }
      const dashboardStats = await userInputService.getDashboardStats(ownerId);

      // Update progress with the stats from the backend
      const progress = { ...categoryProgress };

      // Process each category stat
      dashboardStats.forEach((stat: CategoryStat) => {
        // Map the categoryId to our frontend category keys
        switch (stat.categoryId) {
          case '2':
            progress.homedocuments.answered = stat.answeredQuestions;
            break;
          case '3':
            progress.willinstructions.answered = stat.answeredQuestions;
            break;
          case '4':
            progress.funeralarrangements.answered = stat.answeredQuestions;
            break;
          case '5':
            progress.importantcontacts.answered = stat.answeredQuestions;
            break;
          case '6':
            progress.socialmedia.answered = stat.answeredQuestions;
            break;
          case '7':
            progress.quickstart.answered = stat.answeredQuestions;
            break;
          case '13':
            progress.bankinginformation.answered = stat.answeredQuestions;
            break;
          case '11':
            progress.subscription.answered = stat.answeredQuestions;
            break;
        }
      });

      setCategoryProgress(progress);

      // Mark data as fetched
      dataFetchedRef.current = true;
    } catch (error) {
      console.error('Error fetching progress data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Fetch data when component mounts
  useEffect(() => {
    fetchProgressData();
  }, [fetchProgressData]);

  // Fallback user info if not authenticated
  const userInfo = {
    name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
    email: user?.email || '<EMAIL>',
    avatar: user?.image 
      ? (user.image.startsWith('http') 
          ? user.image 
          : `${import.meta.env.VITE_API_URL}/uploads/${user.image}`)
      : avatar
  }

  // Map category titles to IDs for access control
  const categoryIdMap: Record<string, string> = {
    'Quick Start': '7',
    'Home Instructions': '1',
    'Home Documents': '2',
    'Will Location': '3',
    'Funeral Arrangements': '4',
    'Important Contacts': '5',
    'Social Media & Phone': '6',
    'Insurance': '8',
    'Financial Plans': '9',
    'Ownership Information': '10',
    'Banking Information': '13',
    'Subscription': '11',
    'Passwords': '12',
  };

  const categories = [
    {
      title: 'Quick Start',
      imageSrc: quickStart,
      questionCount: (quickStartLoading && quickStartProgress.total === 0)
        ? 'Loading...'
        : `${quickStartProgress.answered}/${quickStartProgress.total} questions`,
      path: '/category/quickstart',
      isCompleted: !quickStartLoading && quickStartProgress.answered >= quickStartProgress.total && quickStartProgress.total > 0
    },
    {
      title: 'Home Instructions',
      imageSrc: home,
      questionCount: `${homeInstructionsProgress.answeredQuestions}/${homeInstructionsProgress.totalQuestions} questions`,
      path: '/category/homeinstructions',
      isCompleted: homeInstructionsProgress.answeredQuestions >= homeInstructionsProgress.totalQuestions
    },
    {
      title: 'Home Documents',
      imageSrc: documents,
      questionCount: `${categoryProgress.homedocuments.answered}/${categoryProgress.homedocuments.total} questions`,
      path: '/category/homedocuments',
      isCompleted: categoryProgress.homedocuments.answered >= categoryProgress.homedocuments.total
    },
    {
      title: 'Will Location',
      imageSrc: will,
      questionCount: `${categoryProgress.willinstructions.answered}/${categoryProgress.willinstructions.total} questions`,
      path: '/category/willinstructions',
      isCompleted: categoryProgress.willinstructions.answered >= categoryProgress.willinstructions.total
    },
    {
      title: 'Funeral Arrangements',
      imageSrc: funeral,
      questionCount: `${categoryProgress.funeralarrangements.answered}/${categoryProgress.funeralarrangements.total} questions`,
      path: '/category/funeralarrangements',
      isCompleted: categoryProgress.funeralarrangements.answered >= categoryProgress.funeralarrangements.total
    },
    {
      title: 'Important Contacts',
      imageSrc: contact,
      questionCount: `${categoryProgress.importantcontacts.answered}/${categoryProgress.importantcontacts.total} questions`,
      path: '/category/importantcontacts',
      isCompleted: categoryProgress.importantcontacts.answered >= categoryProgress.importantcontacts.total
    },
    {
      title: 'Social Media & Phone',
      imageSrc: socialMedia,
      questionCount: `${categoryProgress.socialmedia.answered}/${categoryProgress.socialmedia.total} questions`,
      path: '/category/socialmedia',
      isCompleted: categoryProgress.socialmedia.answered >= categoryProgress.socialmedia.total
    },
    {
      title: 'Insurance',
      imageSrc: insurance,
      questionCount: `${insuranceProgress.answeredQuestions}/${insuranceProgress.totalQuestions} questions`,
      path: '/category/insurance',
      isCompleted: insuranceProgress.answeredQuestions >= insuranceProgress.totalQuestions
    },

    {
      title: 'Financial Plans',
      imageSrc: financialPlans,
      questionCount: `${financialPlansProgress.answeredQuestions}/${financialPlansProgress.totalQuestions} questions`,
      path: '/category/financialplans',
      isCompleted: financialPlansProgress.answeredQuestions >= financialPlansProgress.totalQuestions
    },

    {
      title: 'Ownership Information',
      imageSrc: ownership,
      questionCount: `${ownershipInfoProgress.answeredQuestions}/${ownershipInfoProgress.totalQuestions} questions`,
      path: '/category/ownershipinfo',
      isCompleted: ownershipInfoProgress.answeredQuestions >= ownershipInfoProgress.totalQuestions
    },

    {
      title: 'Banking Information',
      imageSrc: banking,
      questionCount: `${bankingProgress.answeredQuestions}/${bankingProgress.totalQuestions} questions`,
      path: '/category/bankinginformation',
      isCompleted: bankingProgress.answeredQuestions >= bankingProgress.totalQuestions
    },
    {
      title: 'Passwords',
      imageSrc: passwords,
      questionCount: `${passwordsProgress.answeredQuestions}/${passwordsProgress.totalQuestions} questions`,
      path: '/category/passwords',
      isCompleted: passwordsProgress.answeredQuestions >= passwordsProgress.totalQuestions
    },
    {
      title: 'Subscription',
      imageSrc: subscription,
      questionCount: `${subscriptionProgress.answeredQuestions}/${subscriptionProgress.totalQuestions} questions`,
      path: '/category/subscription',
      isCompleted: subscriptionProgress.answeredQuestions >= subscriptionProgress.totalQuestions
    }
  ]

  return (
    <div className="flex flex-col">
      <AppHeader />
      <div className="bg-gradient-to-r from-[#1F4168] to-[#2BCFD5] text-white py-4">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-1">Dashboard</h1>
              {/* <Link to="/" className="flex items-center text-sm hover:underline text-[#2BCFD5] font-semibold text-md mt-1 mb-1">
                <span className="mr-1">←</span> Back to Home
              </Link> */}
            </div>
            <div className="flex items-center">
              <div className="text-right mr-4">
                <div className="font-semibold">{userInfo.name}</div>
                <div className="text-sm opacity-80">{userInfo.email}</div>
              </div>
              <Avatar className="rounded-full w-14 h-14 bg-white overflow-hidden">
                <img
                  src={userInfo.avatar}
                  alt={userInfo.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = avatar; // Fallback to default avatar
                  }}
                />
              </Avatar>
            </div>
          </div>
        </div>
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        {/* EmergencyAccessManagement removed from here */}
        <div className="flex justify-end mb-4">
          <button
            onClick={() => {
              dataFetchedRef.current = false;
              fetchProgressData();
            }}
            className="bg-[#2BCFD5] hover:bg-[#2BCFD5] text-white px-4 py-2 rounded-md text-sm flex items-center"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                Refreshing...
              </>
            ) : (
              <>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Refresh Progress
              </>
            )}
          </button>
        </div>
        {/* Category Access Requests Section for Owners */}
        {userAccess?.isOwner && (
          <div className="mb-8">
            <RequestList isOwner={true} />
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {isLoading ? (
                <div className="col-span-2 flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#2BCFD5]"></div>
                </div>
              ) : (
                categories.map((category, index) => {
                  const catId = categoryIdMap[category.title];
                  const locked = !canAccessCategory(catId);
                  return (
                    <CategoryCard
                      key={index}
                      title={category.title}
                      imageSrc={category.imageSrc}
                      questionCount={category.questionCount}
                      path={category.path}
                      isCompleted={category.isCompleted}
                      locked={locked}
                      isLoading={category.title === 'Quick Start' && quickStartLoading && quickStartProgress.total === 0}
                      onLockedClick={() => toast({
                        title: 'Upgrade Required',
                        description: 'Upgrade your plan to answer or view this category.',
                        variant: 'default',
                      })}
                    />
                  );
                })
              )}
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  )
}

export default Dashboard;
