{"1101": [{"id": "s1", "text": "Do you have Netflix?", "type": "boolean", "required": false, "sectionId": "1101A", "order": 1}, {"id": "s2", "text": "Netflix Username", "type": "text", "required": false, "dependsOn": {"questionId": "s1", "value": "yes"}, "sectionId": "1101A", "order": 2}, {"id": "s3", "text": "Netflix Password", "type": "password", "required": false, "dependsOn": {"questionId": "s1", "value": "yes"}, "sectionId": "1101A", "order": 3}], "1102": [{"id": "s4", "text": "Do you have Apple TV?", "type": "boolean", "required": false, "sectionId": "1102A", "order": 1}, {"id": "s5", "text": "Apple TV Username", "type": "text", "required": false, "dependsOn": {"questionId": "s4", "value": "yes"}, "sectionId": "1102A", "order": 2}, {"id": "s6", "text": "Apple TV Password", "type": "password", "required": false, "dependsOn": {"questionId": "s4", "value": "yes"}, "sectionId": "1102A", "order": 3}], "1103": [{"id": "s7", "text": "Do you have Amazon Prime?", "type": "boolean", "required": false, "sectionId": "1103A", "order": 1}, {"id": "s8", "text": "Amazon Prime Username", "type": "text", "required": false, "dependsOn": {"questionId": "s7", "value": "yes"}, "sectionId": "1103A", "order": 2}, {"id": "s9", "text": "Amazon Prime Password", "type": "password", "required": false, "dependsOn": {"questionId": "s7", "value": "yes"}, "sectionId": "1103A", "order": 3}], "1104": [{"id": "s10", "text": "Do you have any other subscription services?", "type": "dropdown", "options": ["Disney+", "<PERSON><PERSON>", "<PERSON> (formerly HBO Max)", "<PERSON>", "Paramount+", "Apple TV+", "YouTube TV", "Sling TV", "Other"], "required": false, "sectionId": "1104A", "order": 1}, {"id": "s11", "text": "Service Name", "type": "text", "required": false, "dependsOn": {"questionId": "s10", "value": "Other"}, "sectionId": "1104A", "order": 2}, {"id": "s12", "text": "Username", "type": "text", "required": false, "dependsOn": {"questionId": "s10", "value": "Disney+"}, "sectionId": "1104A", "order": 3}, {"id": "s13", "text": "Password", "type": "password", "required": false, "dependsOn": {"questionId": "s10", "value": "Disney+"}, "sectionId": "1104A", "order": 4}, {"id": "s14", "text": "Username", "type": "text", "required": false, "dependsOn": {"questionId": "s10", "value": "<PERSON><PERSON>"}, "sectionId": "1104A", "order": 5}, {"id": "s15", "text": "Password", "type": "password", "required": false, "dependsOn": {"questionId": "s10", "value": "<PERSON><PERSON>"}, "sectionId": "1104A", "order": 6}, {"id": "s16", "text": "Username", "type": "text", "required": false, "dependsOn": {"questionId": "s10", "value": "<PERSON> (formerly HBO Max)"}, "sectionId": "1104A", "order": 7}, {"id": "s17", "text": "Password", "type": "password", "required": false, "dependsOn": {"questionId": "s10", "value": "<PERSON> (formerly HBO Max)"}, "sectionId": "1104A", "order": 8}, {"id": "s18", "text": "Username", "type": "text", "required": false, "dependsOn": {"questionId": "s10", "value": "<PERSON>"}, "sectionId": "1104A", "order": 9}, {"id": "s19", "text": "Password", "type": "password", "required": false, "dependsOn": {"questionId": "s10", "value": "<PERSON>"}, "sectionId": "1104A", "order": 10}, {"id": "s20", "text": "Username", "type": "text", "required": false, "dependsOn": {"questionId": "s10", "value": "Paramount+"}, "sectionId": "1104A", "order": 11}, {"id": "s21", "text": "Password", "type": "password", "required": false, "dependsOn": {"questionId": "s10", "value": "Paramount+"}, "sectionId": "1104A", "order": 12}, {"id": "s22", "text": "Username", "type": "text", "required": false, "dependsOn": {"questionId": "s10", "value": "Apple TV+"}, "sectionId": "1104A", "order": 13}, {"id": "s23", "text": "Password", "type": "password", "required": false, "dependsOn": {"questionId": "s10", "value": "Apple TV+"}, "sectionId": "1104A", "order": 14}, {"id": "s24", "text": "Username", "type": "text", "required": false, "dependsOn": {"questionId": "s10", "value": "YouTube TV"}, "sectionId": "1104A", "order": 15}, {"id": "s25", "text": "Password", "type": "password", "required": false, "dependsOn": {"questionId": "s10", "value": "YouTube TV"}, "sectionId": "1104A", "order": 16}, {"id": "s26", "text": "Username", "type": "text", "required": false, "dependsOn": {"questionId": "s10", "value": "Sling TV"}, "sectionId": "1104A", "order": 17}, {"id": "s27", "text": "Password", "type": "password", "required": false, "dependsOn": {"questionId": "s10", "value": "Sling TV"}, "sectionId": "1104A", "order": 18}, {"id": "s28", "text": "Username", "type": "text", "required": false, "dependsOn": {"questionId": "s10", "value": "Other"}, "sectionId": "1104A", "order": 19}, {"id": "s29", "text": "Password", "type": "password", "required": false, "dependsOn": {"questionId": "s10", "value": "Other"}, "sectionId": "1104A", "order": 20}]}