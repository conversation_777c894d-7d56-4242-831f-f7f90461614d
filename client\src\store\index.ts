import { configureStore } from '@reduxjs/toolkit';
import homeInstructionsReducer from './slices/homeInstructionsSlice';
import funeralArrangementsReducer from './slices/funeralArrangementsSlice';
import willInstructionsReducer from './slices/willInstructionsSlice';
import importantContactsReducer from './slices/importantContactsSlice';
import socialMediaReducer from './slices/socialMediaSlice';
import homeDocumentsReducer from './slices/homeDocumentsSlice';
import subscriptionReducer from './slices/subscriptionSlice';
import inviteReducer from '@/store/slices/inviteSlice';
import requestedCategoriesReducer from './slices/requestedCategoriesSlice';
import quickStartReducer from './slices/quickStartSlice';
import accessControlReducer from './slices/accessControlSlice';
import insuranceReducer from './slices/insuranceSlice';
import financialPlansReducer from './slices/financialPlansSlice';
import bankingInformationReducer from './slices/bankingInformationSlice';
import ownershipInfoReducer from './slices/ownershipInfoSlice';
import passwordsReducer from './slices/passwordsSlice';
import  SubscriptionCategoryReducer from './slices/subscriptionCategorySlice';

export const store = configureStore({
  reducer: {
    homeInstructions: homeInstructionsReducer,
    funeralArrangements: funeralArrangementsReducer,
    willInstructions: willInstructionsReducer,
    importantContacts: importantContactsReducer,
    socialMedia: socialMediaReducer,
    homeDocuments: homeDocumentsReducer,
    subscription: subscriptionReducer,
    invite: inviteReducer,
    requestedCategories: requestedCategoriesReducer,
    quickStart: quickStartReducer,
    accessControl: accessControlReducer,
    insurance: insuranceReducer,
    financialPlans: financialPlansReducer,
    bankingInformation: bankingInformationReducer,
    ownershipInfo: ownershipInfoReducer,
    passwords: passwordsReducer,
    subscriptionCategory: SubscriptionCategoryReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
import { useDispatch, useSelector } from 'react-redux';
import type { TypedUseSelectorHook } from 'react-redux';

export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
