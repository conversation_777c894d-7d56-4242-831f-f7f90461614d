import quickStartConfig from '@/data/qucikStart.json';
import userInputService from './userInputService';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Cache for Quick Start answers to prevent repeated API calls
const quickStartAnswersCache = new Map<string, { data: QuickStartAnswer[], timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Function to clear cache for a specific user
const clearQuickStartCache = (userId: string) => {
  const cacheKey = `${userId}_quickstart_answers`;
  quickStartAnswersCache.delete(cacheKey);
};

// Types for Quick Start functionality
export interface QuickStartQuestion {
  id: string;
  text: string;
  type: 'text' | 'boolean' | 'choice' | 'number' | 'email' | 'phone';
  required?: boolean;
  placeholder?: string;
  options?: string[];
  sharedKey: string;
  targetFields: TargetField[];
}

export interface TargetField {
  file: string;
  fieldId: string;
  sharedKey: string;
  category?: string; // For future categories
  dependsOn?: {
    question: string;
    value: string;
  };
}

export interface QuickStartAnswer {
  questionId: string;
  answer: string;
  sharedKey: string;
}

export interface AutoPopulateResult {
  success: boolean;
  message: string;
  updatedCategories: string[];
}

/**
 * Service for handling Quick Start functionality including auto-population
 */
const quickStartService = {
  /**
   * Get all Quick Start questions from the configuration
   */
  getQuickStartQuestions: (): QuickStartQuestion[] => {
    const questions: QuickStartQuestion[] = [];
    
    quickStartConfig.quickstartToCategories.forEach((item, index) => {
      const question: QuickStartQuestion = {
        id: `qs${index + 1}`,
        text: item.quickstartQuestion,
        type: quickStartService.determineQuestionType(item.quickstartQuestion),
        required: false,
        sharedKey: Array.isArray(item.sharedKeys) ? (item.sharedKeys[0] || '') : (item.sharedKey || ''),
        targetFields: []
      };

      // Handle single target field
      if (item.target.fieldId) {
        const targetField: TargetField = {
          file: item.target.file,
          fieldId: item.target.fieldId,
          sharedKey: Array.isArray(item.sharedKeys) ? (item.sharedKeys[0] || '') : (item.sharedKey || '')
        };

        // Add category and dependsOn if they exist
        if (item.target.category) {
          targetField.category = item.target.category;
        }
        if (item.target.dependsOn) {
          targetField.dependsOn = item.target.dependsOn;
        }

        question.targetFields.push(targetField);
      }

      // Handle multiple target fields (like address fields)
      if (item.target.fields) {
        item.target.fields.forEach((field: any) => {
          const targetField: TargetField = {
            file: item.target.file,
            fieldId: field.id,
            sharedKey: field.sharedKey || ''
          };

          // Add category and dependsOn if they exist
          if (item.target.category) {
            targetField.category = item.target.category;
          }
          if (item.target.dependsOn) {
            targetField.dependsOn = item.target.dependsOn;
          }

          question.targetFields.push(targetField);
        });
      }

      questions.push(question);
    });

    return questions;
  },

  /**
   * Determine question type based on question text
   */
  determineQuestionType: (questionText: string): 'text' | 'boolean' | 'choice' | 'number' | 'email' | 'phone' => {
    const lowerText = questionText.toLowerCase();
    
    if (lowerText.includes('do you have') || lowerText.includes('is your')) {
      return 'boolean';
    }
    if (lowerText.includes('email')) {
      return 'email';
    }
    if (lowerText.includes('phone')) {
      return 'phone';
    }
    if (lowerText.includes('address') || lowerText.includes('where')) {
      return 'text';
    }
    if (lowerText.includes('name')) {
      return 'text';
    }
    
    return 'text';
  },

  /**
   * Auto-populate answers from Quick Start to other categories
   */
  autoPopulateFromQuickStart: async (
    userId: string, 
    quickStartAnswers: QuickStartAnswer[]
  ): Promise<AutoPopulateResult> => {
    try {
      const ownerId = await getCachedOwnerIdFromUser({ id: userId } as any);
      if (!ownerId) {
        throw new Error('Owner ID not found');
      }

      const updatedCategories: string[] = [];
      const questions = quickStartService.getQuickStartQuestions();

      // Group answers by target file
      const answersByFile: Record<string, any[]> = {};

      quickStartAnswers.forEach(answer => {
        const question = questions.find(q => q.id === answer.questionId);
        if (question) {
          question.targetFields.forEach(targetField => {
            if (!answersByFile[targetField.file]) {
              answersByFile[targetField.file] = [];
            }
            
            // Include category information for future categories
            const fieldData: any = {
              fieldId: targetField.fieldId,
              answer: answer.answer,
              sharedKey: targetField.sharedKey
            };

            // Add category info if it exists (for future categories)
            if ((targetField as any).category) {
              fieldData.category = (targetField as any).category;
            }

            answersByFile[targetField.file].push(fieldData);
          });
        }
      });

      // Update each category file
      for (const [fileName, fieldAnswers] of Object.entries(answersByFile)) {
        try {
          await quickStartService.updateCategoryWithQuickStartAnswers(
            ownerId,
            fileName,
            fieldAnswers
          );
          updatedCategories.push(fileName);
        } catch (error) {
          console.error(`Error updating ${fileName}:`, error);
        }
      }

      return {
        success: true,
        message: `Successfully auto-populated ${updatedCategories.length} categories`,
        updatedCategories
      };

    } catch (error) {
      console.error('Error in auto-population:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Auto-population failed',
        updatedCategories: []
      };
    }
  },

  /**
   * Update a specific category with Quick Start answers
   */
  updateCategoryWithQuickStartAnswers: async (
    ownerId: string,
    fileName: string,
    fieldAnswers: any[]
  ) => {
    // Handle future category targets
    if (fileName === 'future_category') {
      return await quickStartService.saveFutureCategoryAnswers(ownerId, fieldAnswers);
    }

    // Get existing user inputs for this category
    const categoryId = quickStartService.getCategoryIdFromFileName(fileName);
    const existingInputs = await userInputService.getUserInputsByOwnerAndCategory(ownerId, categoryId);

    // Dynamically set subcategory and section for each category
    let originalSubCategoryId = '105';
    let originalSectionId = '105A';
    if (fileName === 'funeralArrangements.json') {
      originalSubCategoryId = '205A';
      originalSectionId = '205A';
    }
    // You can add more categories here if needed in the future

    const userInputData = {
      userId: ownerId, // Using ownerId as userId for consistency
      ownerId,
      originalCategoryId: categoryId,
      originalSubCategoryId,
      answersBySection: [{
        originalSectionId,
        isCompleted: true,
        answers: fieldAnswers.map((field, idx) => ({
          index: idx,
          originalQuestionId: field.fieldId,
          question: `Quick Start Auto-Populated: ${field.fieldId}`,
          type: 'text',
          answer: field.answer
        }))
      }]
    };

    if (existingInputs && existingInputs.length > 0) {
      // Update existing input
      await userInputService.updateUserInput(existingInputs[0]._id!, userInputData);
    } else {
      // Create new input
      await userInputService.createUserInput(userInputData);
    }
  },

  /**
   * Save answers for future categories that don't exist yet
   */
  saveFutureCategoryAnswers: async (
    ownerId: string,
    fieldAnswers: any[]
  ) => {
    // Group answers by category
    const answersByCategory: Record<string, any[]> = {};
    
    fieldAnswers.forEach(field => {
      const category = field.category || 'future_category';
      if (!answersByCategory[category]) {
        answersByCategory[category] = [];
      }
      answersByCategory[category].push(field);
    });

    // Note: Cache clearing will be handled by the calling function

    // Save each category separately
    for (const [category, answers] of Object.entries(answersByCategory)) {
      const categoryId = quickStartService.getFutureCategoryId(category);
      
      // Check if we already have data for this future category
      const existingInputs = await userInputService.getUserInputsByOwnerAndCategory(ownerId, categoryId);

      const userInputData = {
        userId: ownerId,
        ownerId,
        originalCategoryId: categoryId,
        originalSubCategoryId: '999', // Special ID for future categories
        answersBySection: [{
          originalSectionId: '999A',
          isCompleted: true,
          answers: answers.map((field, idx) => ({
            index: idx,
            originalQuestionId: field.fieldId,
            question: `Quick Start Future Category: ${field.fieldId}`,
            type: 'text',
            answer: field.answer
          }))
        }]
      };

      if (existingInputs && existingInputs.length > 0) {
        // Update existing input
        await userInputService.updateUserInput(existingInputs[0]._id!, userInputData);
      } else {
        // Create new input
        await userInputService.createUserInput(userInputData);
      }
    }
  },

  /**
   * Get category ID for future categories
   */
  getFutureCategoryId: (category: string): string => {
    const futureCategoryMap: Record<string, string> = {
      'housing_ownership': '999',
      'insurance': '998',
      'investments': '997',
      'business': '996'
      // Add more future categories as needed
    };
    
    return futureCategoryMap[category] || '999';
  },

  /**
   * Get category ID from file name
   */
  getCategoryIdFromFileName: (fileName: string): string => {
    const fileToCategoryMap: Record<string, string> = {
      'homeIntsructions.json': '1',
      'homeDocuments.json': '2',
      'willInstructions.json': '3',
      'funeralArrangements.json': '4',
      'importantContacts.json': '5',
      'socialMedia.json': '6'
    };
    
    return fileToCategoryMap[fileName] || '1';
  },

  /**
   * Get existing Quick Start answers from other categories (optimized with caching)
   */
  getExistingQuickStartAnswers: async (userId: string): Promise<QuickStartAnswer[]> => {
    try {
      const ownerId = await getCachedOwnerIdFromUser({ id: userId } as any);
      if (!ownerId) {
        return [];
      }

      // Check cache first
      const cacheKey = `${ownerId}_quickstart_answers`;
      const cached = quickStartAnswersCache.get(cacheKey);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < CACHE_DURATION) {
        return cached.data;
      }

      const questions = quickStartService.getQuickStartQuestions();
      const existingAnswers: QuickStartAnswer[] = [];

      // Collect all unique category IDs to minimize API calls
      const categoryIds = new Set<string>();
      const categoryToTargetFields = new Map<string, { question: QuickStartQuestion, targetField: TargetField }[]>();

      questions.forEach(question => {
        question.targetFields.forEach(targetField => {
          const categoryId = quickStartService.getCategoryIdFromFileName(targetField.file);
          categoryIds.add(categoryId);

          if (!categoryToTargetFields.has(categoryId)) {
            categoryToTargetFields.set(categoryId, []);
          }
          categoryToTargetFields.get(categoryId)!.push({ question, targetField });
        });
      });

      // Fetch data for each category only once
      const categoryDataCache = new Map<string, any[]>();

      for (const categoryId of categoryIds) {
        try {
          const existingInputs = await userInputService.getUserInputsByOwnerAndCategory(ownerId, categoryId);
          categoryDataCache.set(categoryId, existingInputs || []);
        } catch (error) {
          console.error(`Error fetching data for category ${categoryId}:`, error);
          categoryDataCache.set(categoryId, []);
        }
      }

      // Process the cached data to find answers
      categoryToTargetFields.forEach((questionTargetPairs, categoryId) => {
        const existingInputs = categoryDataCache.get(categoryId) || [];

        if (existingInputs.length > 0) {
          questionTargetPairs.forEach(({ question, targetField }) => {
            // Find the answer in existing inputs
            for (const input of existingInputs) {
              if (input?.answersBySection) {
                for (const section of input.answersBySection) {
                  if (section?.answers) {
                    const answer = section.answers.find((a: any) => a.originalQuestionId === targetField.fieldId);
                    if (answer && answer.answer) {
                      existingAnswers.push({
                        questionId: question.id,
                        answer: answer.answer,
                        sharedKey: targetField.sharedKey
                      });
                      return; // Break out of nested loops for this target field
                    }
                  }
                }
              }
            }
          });
        }
      });

      // Cache the results
      quickStartAnswersCache.set(cacheKey, {
        data: existingAnswers,
        timestamp: now
      });

      return existingAnswers;
    } catch (error) {
      console.error('Error getting existing Quick Start answers:', error);
      return [];
    }
  },

  /**
   * Sync Quick Start answers with other categories (bi-directional)
   */
  syncQuickStartAnswers: async (
    userId: string,
    quickStartAnswers: QuickStartAnswer[]
  ): Promise<AutoPopulateResult> => {
    try {
      // Clear cache since we're updating data
      clearQuickStartCache(userId);

      // First, auto-populate from Quick Start to other categories
      const autoPopulateResult = await quickStartService.autoPopulateFromQuickStart(userId, quickStartAnswers);
      
      if (!autoPopulateResult.success) {
        return autoPopulateResult;
      }

      // Then, check if there are any conflicts or updates needed
      const existingAnswers = await quickStartService.getExistingQuickStartAnswers(userId);
      
      // Merge existing answers with new Quick Start answers
      const mergedAnswers = quickStartService.mergeAnswers(existingAnswers, quickStartAnswers);

      return {
        success: true,
        message: `Successfully synced Quick Start answers with ${autoPopulateResult.updatedCategories.length} categories`,
        updatedCategories: autoPopulateResult.updatedCategories
      };

    } catch (error) {
      console.error('Error syncing Quick Start answers:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Sync failed',
        updatedCategories: []
      };
    }
  },

  /**
   * Merge existing answers with new Quick Start answers
   */
  mergeAnswers: (existing: QuickStartAnswer[], newAnswers: QuickStartAnswer[]): QuickStartAnswer[] => {
    const merged = [...existing];
    
    newAnswers.forEach(newAnswer => {
      const existingIndex = merged.findIndex(a => a.questionId === newAnswer.questionId);
      if (existingIndex >= 0) {
        merged[existingIndex] = newAnswer; // Update existing
      } else {
        merged.push(newAnswer); // Add new
      }
    });

    return merged;
  },

  /**
   * Delete Quick Start answers and remove from other categories
   */
  deleteQuickStartAnswers: async (
    userId: string,
    questionIds: string[]
  ): Promise<AutoPopulateResult> => {
    try {
      // Clear cache since we're deleting data
      clearQuickStartCache(userId);

      const ownerId = await getCachedOwnerIdFromUser({ id: userId } as any);
      if (!ownerId) {
        throw new Error('Owner ID not found');
      }

      const questions = quickStartService.getQuickStartQuestions();
      const updatedCategories: string[] = [];

      // Find target fields for the questions to be deleted
      const targetFieldsToDelete: any[] = [];
      
      questionIds.forEach(questionId => {
        const question = questions.find(q => q.id === questionId);
        if (question) {
          question.targetFields.forEach(targetField => {
            targetFieldsToDelete.push({
              file: targetField.file,
              fieldId: targetField.fieldId
            });
          });
        }
      });

      // Remove answers from each category
      const filesToUpdate = [...new Set(targetFieldsToDelete.map(t => t.file))];
      
      for (const fileName of filesToUpdate) {
        const categoryId = quickStartService.getCategoryIdFromFileName(fileName);
        const existingInputs = await userInputService.getUserInputsByOwnerAndCategory(ownerId, categoryId);

        if (existingInputs && existingInputs.length > 0) {
          const fieldIdsToDelete = targetFieldsToDelete
            .filter(t => t.file === fileName)
            .map(t => t.fieldId);

          // Remove the specified fields from existing inputs
          for (const input of existingInputs) {
            if (input.answersBySection) {
              input.answersBySection.forEach(section => {
                if (section.answers) {
                  section.answers = section.answers.filter(
                    answer => !fieldIdsToDelete.includes(answer.originalQuestionId)
                  );
                }
              });
            }
            
            // Update the input - cast to the correct type
            await userInputService.updateUserInput(input._id!, input as any);
          }
          
          updatedCategories.push(fileName);
        }
      }

      return {
        success: true,
        message: `Successfully deleted Quick Start answers from ${updatedCategories.length} categories`,
        updatedCategories
      };

    } catch (error) {
      console.error('Error deleting Quick Start answers:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Deletion failed',
        updatedCategories: []
      };
    }
  },

  /**
   * Clear cache for a specific user (useful when data is updated externally)
   */
  clearCache: (userId: string) => {
    clearQuickStartCache(userId);
  }
};

export default quickStartService;