import React, { useEffect, useState } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';
import { useToast } from '@/contexts/ToastContext';

interface MobileEmergencyAccessNotificationProps {
  className?: string;
}

export const MobileEmergencyAccessNotification: React.FC<MobileEmergencyAccessNotificationProps> = ({ className }) => {
  const { userAccess, emergencyAccessStatus } = useAccessControl();
  const { toast } = useToast();
  const [lastStatus, setLastStatus] = useState<string>('');

  useEffect(() => {
    if (!userAccess || userAccess.subscriptionType !== 'spare_key' || userAccess.isOwner) {
      return;
    }

    const currentStatus = userAccess.emergencyAccessGranted 
      ? 'granted' 
      : userAccess.emergencyAccessDenied 
      ? 'denied' 
      : userAccess.emergencyAccessRequested 
      ? 'requested' 
      : 'none';

    // Show notification when status changes
    if (lastStatus && lastStatus !== currentStatus) {
      if (currentStatus === 'granted') {
        toast({
          title: "Emergency Access Granted",
          description: "You now have access to view the information.",
          variant: "custom"
        });
      } else if (currentStatus === 'denied') {
        toast({
          title: "Emergency Access Denied",
          description: "Your emergency access request was denied by the owner.",
          variant: "custom"
        });
      } else if (currentStatus === 'requested') {
        toast({
          title: "Emergency Access Requested",
          description: "Your request has been sent to the owner. You'll be notified of the decision.",
          variant: "custom"
        });
      }
    }

    setLastStatus(currentStatus);
  }, [userAccess, lastStatus, toast]);

  if (!userAccess || userAccess.subscriptionType !== 'spare_key' || userAccess.isOwner) {
    return null;
  }

  const formatTimeRemaining = (milliseconds: number): string => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  // Only show notification for important status changes
  if (userAccess.emergencyAccessGranted) {
    return (
      <Alert className={`border-green-200 bg-green-50 ${className}`}>
        <CheckCircle className="h-4 w-4 text-green-600" />
        <AlertDescription className="text-green-800">
          <div className="flex items-center justify-between">
            <span>Emergency access has been granted!</span>
            <Button
              size="sm"
              variant="outline"
              className="text-green-600 border-green-300 hover:bg-green-100"
              onClick={() => window.location.reload()}
            >
              Refresh
            </Button>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (userAccess.emergencyAccessDenied) {
    return (
      <Alert className={`border-red-200 bg-red-50 ${className}`}>
        <XCircle className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          Emergency access was denied by the owner.
        </AlertDescription>
      </Alert>
    );
  }

  if (userAccess.emergencyAccessRequested) {
    const timeRemaining = emergencyAccessStatus?.timeRemaining || 0;
    return (
      <Alert className={`border-yellow-200 bg-yellow-50 ${className}`}>
        <Clock className="h-4 w-4 text-yellow-600" />
        <AlertDescription className="text-yellow-800">
          <div className="space-y-1">
            <div>Emergency access request is pending approval.</div>
            {timeRemaining > 0 && (
              <div className="text-sm">
                Auto-grant in: {formatTimeRemaining(timeRemaining)}
              </div>
            )}
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  return null;
}; 