import React, { useEffect, useState } from 'react';
import { useLocation, useParams, useNavigate } from 'react-router-dom';
import { approveRequest } from '@/services/requestedCategoriesService';
import { useAuth } from '@/contexts/AuthContext';

const ApproveCategoryRequest: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const [message, setMessage] = useState<string>('Processing your request...');
  const [loading, setLoading] = useState<boolean>(true);
  const [success, setSuccess] = useState<boolean | null>(null);

  useEffect(() => {
    const query = new URLSearchParams(location.search);
    const action = query.get('action');

    if (!token || !action) {
      setMessage('Invalid request: missing token or action.');
      setSuccess(false);
      setLoading(false);
      return;
    }

    approveRequest(token, action as 'approve' | 'reject')
      .then((res) => {
        setMessage((res as { message: string }).message || 'Request processed successfully.');
        setSuccess(true);
      })
      .catch((err) => {
        setMessage(
          err?.response?.data?.message || 'Failed to process the request. The link may be expired or invalid.'
        );
        setSuccess(false);
      })
      .finally(() => setLoading(false));
  }, [token, location.search]);

  const handleLogin = () => {
    navigate('/auth/login');
  };

  const handleDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div style={{
      maxWidth: 500,
      margin: '60px auto',
      padding: 24,
      border: '1px solid #eee',
      borderRadius: 8,
      background: '#fff',
      textAlign: 'center',
      boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
    }}>
      <h2>Category Access Request</h2>
      {loading ? (
        <div>
          <div className="spinner" style={{ margin: '24px auto' }} />
          <p>Processing...</p>
        </div>
      ) : (
        <>
          <div style={{ fontSize: 48, margin: '16px 0' }}>
            {success === true && <span role="img" aria-label="success">✅</span>}
            {success === false && <span role="img" aria-label="error">❌</span>}
          </div>
          <p style={{ fontSize: 18 }}>{message}</p>
          <div style={{ marginTop: 24 }}>
            {isAuthenticated ? (
              <button onClick={handleDashboard} style={{
                padding: '10px 24px',
                background: '#4CAF50',
                color: '#fff',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer'
              }}>
                Go to Dashboard
              </button>
            ) : (
              <button onClick={handleLogin} style={{
                padding: '10px 24px',
                background: '#1976d2',
                color: '#fff',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer'
              }}>
                Login to Continue
              </button>
            )}
          </div>
        </>
      )}
      <style>
        {`
          .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
          }
          @keyframes spin {
            0% { transform: rotate(0deg);}
            100% { transform: rotate(360deg);}
          }
        `}
      </style>
    </div>
  );
};

export default ApproveCategoryRequest; 