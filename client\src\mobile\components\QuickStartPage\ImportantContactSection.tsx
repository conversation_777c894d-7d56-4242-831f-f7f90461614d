import React, { useEffect, useState } from 'react';
import { Formik, Form, FormikHelpers, FieldArray, Field } from 'formik';
import * as Yup from 'yup';
import { Button } from '@/components/ui/button';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  UserInput
} from '@/store/slices/importantContactsSlice';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import {
  Question,
  QuestionItem,
  buildValidationSchema,
  generateInitialValues,
  handleDependentAnswers
} from '@/web/components/Category/ImportantContacts/FormFields';
import ScrollToQuestion from '@/web/components/Category/ImportantContacts/ScrollToQuestion';
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';
import QuickStartCardWrapper from './QuickStartCardWrapper';

const ImportantContactSection: React.FC = () => {
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  // Get only the c1 question from importantContacts.json (section 207A)
  const allQuestions = useAppSelector((state: any) => state.importantContacts.questions['207'] || []);
  const questions = allQuestions.filter((q: Question) => q.id === 'c1' && q.sectionId === '207A');
  const loading = useAppSelector((state: any) => state.importantContacts.loading);
  const userInputs = useAppSelector((state: any) => state.importantContacts.userInputs);

  // Fetch user inputs on mount
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (ownerId) dispatch(fetchUserInputs(ownerId));
      }
    };
    fetchData();
  }, [dispatch, user]);

  // Process user inputs to get saved answers
  useEffect(() => {
    if (!loading) {
      if (userInputs.length > 0) {
        // Find the most recent userInput for 207A
        const userInput = userInputs
          .filter((input: UserInput) => input.originalSubCategoryId === '207A')
          .sort((a: any, b: any) =>
            new Date((b as any).updatedAt || (b as any).createdAt || 0).getTime() -
            new Date((a as any).updatedAt || (a as any).createdAt || 0).getTime()
          )[0];
        if (userInput) {
          setSavedAnswers(convertUserInputToFormValues(userInput));
          setExistingInputId(userInput._id || null);
        }
      }
      setIsDataLoaded(true);
    }
  }, [userInputs, loading]);

  // Handle form submission
  const handleSubmit = async (
    values: Record<string, string>,
    { setSubmitting }: FormikHelpers<Record<string, string>>
  ) => {
    try {
      if (!user || !user.id) throw new Error('You must be logged in to save answers');
      const ownerId = await getCachedOwnerIdFromUser(user);

      // Group answers by section
      const answersBySection = questions.reduce(
        (sections: Record<string, Array<any>>, question: Question) => {
          if (!sections[question.sectionId]) {
            sections[question.sectionId] = [];
          }
          let answer = values[question.id];
          if (question.id === CONTACTS_QUESTION_ID && Array.isArray(answer)) {
            // Filter out empty contacts
            const filtered = answer.filter((c: any) => c && (c.name?.trim() || c.phone?.trim()));
            answer = JSON.stringify(filtered);
          }
          if (answer) {
            sections[question.sectionId].push({
              index: sections[question.sectionId].length,
              originalQuestionId: question.id,
              question: question.text,
              type: question.type,
              answer,
            });
          }
          return sections;
        },
        {}
      );

      // Format answers data
      const formattedAnswersBySection = Object.entries(answersBySection).map(
        ([sectionId, answers]) => ({
          originalSectionId: sectionId,
          isCompleted: true,
          answers: answers as any[],
        })
      );

      if (existingInputId) {
        // Update existing record for 207A
        await dispatch(
          updateUserInput({
            id: existingInputId,
            userData: {
              userId: user.id,
              categoryId: generateObjectId(),
              originalCategoryId: '5',
              subCategoryId: generateObjectId(),
              originalSubCategoryId: '207A',
              answersBySection: formattedAnswersBySection,
            } as UserInput,
          })
        ).unwrap();
      } else {
        // Create new record for 207A
        const userData: Omit<UserInput, '_id'> = {
          userId: user.id,
          categoryId: generateObjectId(),
          originalCategoryId: '5',
          subCategoryId: generateObjectId(),
          originalSubCategoryId: '207A',
          answersBySection: formattedAnswersBySection,
        };
        await dispatch(saveUserInput(userData)).unwrap();
      }

      // Force re-fetch to update Redux state everywhere
      if (ownerId) {
        await dispatch(fetchUserInputs(ownerId));
      }

      setSubmitting(false);
      // Optionally, show a toast or redirect
    } catch (error) {
      setSubmitting(false);
    }
  };

  if (questions.length === 0 || loading || !isDataLoaded) {
    return <div className="flex justify-center items-center h-40">Loading...</div>;
  }

  // Find the contacts question ID
  const CONTACTS_QUESTION_ID = questions[0]?.id || '';

  // Now use CONTACTS_QUESTION_ID in validationSchema and initialValues
  const contactValidationSchema = Yup.object().shape({
    [CONTACTS_QUESTION_ID]: Yup.array().of(
      Yup.object().shape({
        name: Yup.string(),
        phone: Yup.string(),
      })
    )
  });

  // Prepare initial values for contacts array
  let contacts: any[] = [];
  if (savedAnswers[CONTACTS_QUESTION_ID] && typeof savedAnswers[CONTACTS_QUESTION_ID] === 'string') {
    try {
      contacts = JSON.parse(savedAnswers[CONTACTS_QUESTION_ID] as string);
      if (!Array.isArray(contacts)) contacts = [contacts];
    } catch {
      contacts = [];
    }
  }
  const initialValues: Record<string, any> = {
    [CONTACTS_QUESTION_ID]: contacts.length > 0 ? contacts : [{ name: '', phone: '' }]
  };

  return (
    <QuickStartCardWrapper>
      <Formik
        key={`${isDataLoaded}-${JSON.stringify(savedAnswers)}`}
        initialValues={initialValues}
        validationSchema={contactValidationSchema}
        onSubmit={handleSubmit}
        enableReinitialize={true}
      >
        {({ values, isSubmitting, isValid, dirty, setValues }) => {
          const handleDependentFields = () => {
            handleDependentAnswers(values, questions, setValues);
          };
          if (Object.keys(values).length > 0) setTimeout(handleDependentFields, 0);

          return (
            <Form>
              <ScrollToQuestion questions={questions}>
                {(refs) => (
                  <>
                    {[...questions]
                      .sort((a: any, b: any) => a.order - b.order)
                      .map((question: Question) => (
                        <div
                          key={question.id}
                          id={`question-${question.id}`}
                          ref={(el: HTMLDivElement | null) => {
                            refs[question.id] = el;
                          }}
                        >
                          {CONTACTS_QUESTION_ID && question.id === CONTACTS_QUESTION_ID ? (
                            <FieldArray name={CONTACTS_QUESTION_ID}>
                              {({ push, remove, form }) => {
                                const contacts = Array.isArray(form.values[CONTACTS_QUESTION_ID])
                                  ? form.values[CONTACTS_QUESTION_ID]
                                  : [];
                                return (
                                  <div>
                                    <div className="mb-2 font-medium">{question.text}</div>
                                    {contacts.length === 0 && (
                                      <div className="text-gray-400 mb-2">No contacts added.</div>
                                    )}
                                    {contacts.map((contact: any, idx: number) => (
                                      <div key={idx} className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 mb-2 w-full">
                                        <Field
                                          name={`${CONTACTS_QUESTION_ID}.${idx}.name`}
                                          placeholder="Name"
                                          className="border rounded px-2 py-1 flex-1 min-w-0"
                                        />
                                        <Field
                                          name={`${CONTACTS_QUESTION_ID}.${idx}.phone`}
                                          placeholder="Phone Number"
                                          className="border rounded px-2 py-1 flex-1 min-w-0"
                                        />
                                        <button
                                          type="button"
                                          className="text-red-500 hover:text-red-700 self-end sm:self-auto"
                                          onClick={() => remove(idx)}
                                          aria-label="Delete contact"
                                        >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3m5 0H6" />
                                        </svg>
                                        </button>
                                      </div>
                                    ))}
                                    <button 
                                      type="button" 
                                      onClick={() => push({ name: "", phone: "" })} 
                                      className="text-[#2BCFD5] mt-2 flex items-center gap-1"
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                      </svg>
                                      Add Contact
                                    </button>
                                  </div>
                                );
                              }}
                            </FieldArray>
                          ) : (
                            <QuestionItem question={question} values={values} />
                          )}
                        </div>
                      ))}
                  </>
                )}
              </ScrollToQuestion>
              <div className="mt-8 flex justify-end">
                <Button
                  type="submit"
                  disabled={isSubmitting || !isValid || !dirty}
                  className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                >
                  Save & Continue
                </Button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </QuickStartCardWrapper>
  );
};

export default ImportantContactSection; 