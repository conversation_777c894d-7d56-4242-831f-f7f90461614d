import jwt from 'jsonwebtoken';

interface JwtPayload {
  id: string;
  iat: number;
}

export const verifyToken = (token: string, secret: string): Promise<JwtPayload> => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, secret, (err, decoded) => {
      if (err) {
        return reject(err);
      }
      resolve(decoded as JwtPayload);
    });
  });
};

export const createToken = (userId: string, secret: string, expiresIn: string = '15d'): string => {
  return jwt.sign({ userId }, process.env.JWT_SECRET as jwt.Secret, { expiresIn: expiresIn as jwt.SignOptions['expiresIn'] });
}; 




