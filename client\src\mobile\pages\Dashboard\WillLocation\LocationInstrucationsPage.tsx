import { useState, useEffect, useRef, useMemo } from "react";
import type { UserInput, SectionAnswers, Answer } from '@/store/slices/willInstructionsSlice';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { Formik, Form, ErrorMessage } from "formik";
import { categoryTabsConfig } from "@/data/categoryTabsConfig";
import { CircularProgress } from "@/components/ui/CircularProgress";
import { useAuth } from '@/contexts/AuthContext';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  updateFormValues,
  selectQuestionsBySubcategoryId,
  selectUserInputsBySubcategoryId,
  selectFormValues,
  selectError
} from '@/store/slices/willInstructionsSlice';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import {
  QuestionItem,
  buildInitialValues,
  validate,
  isQuestionVisible
} from '@/mobile/components/dashboard/WillLocation/FormFields';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Filter and sort location questions
// This function is used by the Redux selector

const LocationInstrucationsPage = () => {
  const { categoryName } = useParams();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [step, setStep] = useState(0);
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const hasSetInitialStep = useRef(false);
  const location = useLocation();

  // Get questionId from URL query parameters
  const searchParams = new URLSearchParams(location.search);
  const targetQuestionId = searchParams.get('questionId');

  // Get data from Redux store 
  const locationQuestions = useAppSelector(selectQuestionsBySubcategoryId('105A'));
  const userInputs = useAppSelector(selectUserInputsBySubcategoryId('105A')); 
  const formValues = useAppSelector(selectFormValues);
  const reduxError = useAppSelector(selectError);


  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in LocationInstructionsPage component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in LocationInstructionsPage component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Set existing input ID if we have saved data
  useEffect(() => {
    if (userInputs && userInputs.length > 0 && userInputs[0]?._id && !existingInputId) {
      setExistingInputId(userInputs[0]._id);
    } else if ((!userInputs || userInputs.length === 0) && existingInputId) {
      setExistingInputId(null);
    }
  }, [userInputs, existingInputId]);

  // Build initial values from existing user inputs, form values, or empty values
  const initialValues = useMemo(() => {
    return userInputs.length > 0
      ? convertUserInputToFormValues(userInputs[0])
      : formValues && Object.keys(formValues).length > 0
        ? { ...buildInitialValues(locationQuestions), ...formValues }
        : buildInitialValues(locationQuestions);
  }, [userInputs, formValues, locationQuestions]);

  // Set the step to the target question if provided in URL - only once when component mounts
  useEffect(() => {
    if (targetQuestionId && locationQuestions.length > 0 && !hasSetInitialStep.current) {
      const questionIndex = locationQuestions.findIndex(q => q.id === targetQuestionId);
      if (questionIndex !== -1) {
        setStep(questionIndex);
        hasSetInitialStep.current = true;
      }
    }
  }, [targetQuestionId, locationQuestions]);

  // Show loading state if no questions are loaded yet
  if (locationQuestions.length === 0) {
    return (
      <div className="min-h-screen bg-white">
        <GradiantHeader
          showAvatar={true}
          title="Will Instructions"
        />
        <div className="container mx-auto px-4 py-6">
          <div className="flex mb-4 p-4">
            {((categoryTabsConfig as Record<string, { label: string; path: string }[]>)[categoryName || "willinstructions"] || []).map((tab) => {
              const isActive = tab.path === location.pathname;
              return (
                <button
                  key={tab.label}
                  type="button"
                  className={
                    "flex-1 py-2 rounded-md font-medium bg-gray-100 " +
                    (isActive
                      ? "bg-white text-[#2BCFD5] border border-[#2BCFD5]"
                      : "text-gray-500")
                  }
                  disabled={isActive}
                  onClick={() => {
                    if (!isActive) navigate(tab.path);
                  }}
                >
                  {tab.label}
                </button>
              );
            })}
          </div>
          <div className="max-w-md mx-auto space-y-6">
            <div className="text-center py-8">
              <div className="text-lg">Loading questions...</div>
              <div className="text-sm text-gray-500 mt-2">
                Location questions: {locationQuestions.length}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <GradiantHeader
        showAvatar={true}
        title="Will Instructions"
      />
      <div className="container mx-auto px-4 py-6">
        <div className="flex mb-4 p-4">
          {((categoryTabsConfig as Record<string, { label: string; path: string }[]>)[categoryName || "willinstructions"] || []).map((tab) => {
            const isActive = tab.path === location.pathname;
            return (
              <button
                key={tab.label}
                type="button"
                className={
                  "flex-1 py-2 rounded-md font-medium bg-gray-100 " +
                  (isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5]"
                    : "text-gray-500")
                }
                disabled={isActive}
                onClick={() => {
                  if (!isActive) navigate(tab.path);
                }}
              >
                {tab.label}
              </button>
            );
          })}
        </div>
        <div className="max-w-md mx-auto space-y-6">
          {/* Show error message if any */}
          {(error || reduxError) && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error || reduxError}</AlertDescription>
            </Alert>
          )}

          <Formik
            initialValues={initialValues}
            validate={validate(locationQuestions)}
            enableReinitialize={true}
            onSubmit={async (values: Record<string, string>) => {
              try {
                setError(null);

                // If w1 is "No", skip to next subcategory
                if (values['w1'] === 'No') {
                  // Clear w2 and w3 values
                  values['w2'] = '';
                  values['w3'] = '';

                  // Store form values in Redux for cross-page navigation
                  dispatch(updateFormValues(values));

                  // Prepare minimal data for saving
                  const sectionA: SectionAnswers = {
                    originalSectionId: '105A',
                    isCompleted: true,
                    answers: [{
                      index: 0,
                      questionId: generateObjectId(),
                      originalQuestionId: 'w1',
                      question: 'Do you have a will?',
                      type: 'boolean',
                      answer: 'No'
                    }]
                  };

                  const userData: Omit<UserInput, '_id'> = {
                    userId: user?.id || '',
                    originalCategoryId: '3',
                    originalSubCategoryId: '105A',
                    categoryId: existingInputId ? userInputs[0].categoryId : generateObjectId(),
                    subCategoryId: existingInputId ? userInputs[0].subCategoryId : generateObjectId(),
                    answersBySection: [sectionA]
                  };

                  // Save or update the data
                  let result;
                  if (existingInputId) {
                    result = await dispatch(updateUserInput({
                      id: existingInputId,
                      userData
                    }));
                  } else {
                    result = await dispatch(saveUserInput(userData));
                  }

                  // Navigate to next subcategory
                  setTimeout(() => {
                    navigate(`/category/${categoryName || 'willinstructions'}/legal`);
                  }, 100);
                  return;
                }

                if (values['w2'] === 'No') {
                  values['w3'] = '';
                }

                // Validate will location question only if w1 is "Yes" and w2 is "Yes"
                const willLocationQuestion = locationQuestions.find(q => q.id === 'w3');
                if (willLocationQuestion && values['w1'] === 'Yes' && values['w2'] === 'Yes' && (!values['w3'] || values['w3'].trim() === '')) {
                  setError('Please specify where your will is located before proceeding.');
                  return;
                }

                // Store form values in Redux for cross-page navigation
                dispatch(updateFormValues(values));

                // Prepare data for saving to backend
                const answers: Answer[] = [];

                locationQuestions.forEach((question, index) => {
                  if (isQuestionVisible(question, values)) {
                    const answer = values[question.id];
                    if (answer && answer.trim() !== '') {
                      answers.push({
                        index,
                        questionId: generateObjectId(),
                        originalQuestionId: question.id,
                        question: question.text,
                        type: question.type,
                        answer
                      });
                    }
                  }
                });

                const answersBySection: SectionAnswers[] = [];
                if (answers.length > 0) {
                  answersBySection.push({
                    originalSectionId: '105A',
                    isCompleted: true,
                    answers
                  });
                }

                // Create user input data object
                const userData: Omit<UserInput, '_id'> = {
                  userId: user?.id || '',
                  originalCategoryId: '3',
                  originalSubCategoryId: '105A',
                  answersBySection: answersBySection
                };
                
                // If updating an existing record, preserve its IDs
                if (existingInputId && userInputs.length > 0) {
                  userData.categoryId = userInputs[0].categoryId;
                  userData.subCategoryId = userInputs[0].subCategoryId;
                } else {
                  userData.categoryId = generateObjectId();
                  userData.subCategoryId = generateObjectId();
                }

                let result;
                // Update or create based on whether we have an existing record
                if (existingInputId) {
                  result = await dispatch(updateUserInput({
                    id: existingInputId,
                    userData
                  }));
                } else {
                  result = await dispatch(saveUserInput(userData));
                }

                // Store the new record ID for future updates
                const payload = result.payload as UserInput;
                if (payload && payload._id) {
                  setExistingInputId(payload._id);
                }

                // Navigate to next subcategory
                setTimeout(() => {
                  navigate(`/category/${categoryName || 'willinstructions'}/legal`);
                }, 100);
                return;
              } catch (err: unknown) {
                console.error('Error saving data:', err);
                const errorMessage = err instanceof Error ? err.message : 'Failed to save data. Please try again.';
                setError(errorMessage);
              }
            }}
          >
            {({ values, isSubmitting }: { values: Record<string, string>; isSubmitting: boolean }) => {
              // Find the current question to display
              const currentQuestion = locationQuestions[step];

              // Safety check: if no current question, show loading or redirect
              if (!currentQuestion) {
                if (step >= locationQuestions.length) {
                  // If we've gone past the last question, redirect to next page
                  setTimeout(() => {
                    navigate(`/category/${categoryName || 'willinstructions'}/legal`);
                  }, 100);
                }
                return <div>Loading...</div>;
              }

              // Skip to next subcategory if w1 is "No"
              if (currentQuestion.id === 'w2' && values['w1'] === 'No') {
                setTimeout(() => {
                  navigate(`/category/${categoryName || 'willinstructions'}/legal`);
                }, 100);
                return <div>Redirecting...</div>;
              }

              // Calculate total visible questions based on dependencies
              const getTotalQuestions = () => {
                if (values['w1'] === 'No') return 1;
                if (values['w2'] === 'No') return 2;
                return 3;
              };

              // Show all questions without dependency filtering
              return (
                <Form className="space-y-4">
                  {/* Progress */}
                  <div className="bg-gray-50 flex justify-between items-center rounded-xl shadow-sm border p-4">
                    <span className="text-black font-semibold text-lg">Will: <span className="text-[#2BCFD5]">Location</span></span>
                    <span>
                      <CircularProgress 
                        value={step + 1} 
                        max={getTotalQuestions()} 
                      />
                    </span>
                  </div>
                  {/* Question Card */}
                  <div
                    className={`bg-gray-100 rounded-xl shadow-sm border p-4 ${targetQuestionId === currentQuestion.id ? 'border-[#2BCFD5] border-2' : ''}`}
                    id={`question-${currentQuestion.id}`}
                  >
                    <QuestionItem question={currentQuestion} values={values} />
                    <ErrorMessage name={currentQuestion.id} component="div" className="text-red-500 text-sm mt-1" />
                  </div>
                  {/* Navigation */}
                  <div className="flex justify-between items-center mt-4">
                    <button
                      type="button"
                      onClick={() => setStep(Math.max(0, step - 1))}
                      disabled={step === 0}
                      className="text-[#2BCFD5] underline disabled:opacity-50"
                    >
                      ← Back
                    </button>
                    {step < getTotalQuestions() - 1 ? (
                      <button
                        type="button"
                        onClick={() => setStep(step + 1)}
                        className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                      >
                        Next →
                      </button>
                    ) : (
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                      >
                        {isSubmitting ? 'Saving...' : 'Save Location'}
                      </button>
                    )}
                  </div>
                </Form>
              );
            }}
          </Formik>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default LocationInstrucationsPage;
