import mongoose, { Schema } from 'mongoose';
import { IPricingPlan } from '../types/PricingPlan';

const pricingPlanSchema = new Schema<IPricingPlan>({
  type: {
    type: String,
    enum: ['temporary_key', 'spare_key', 'all_access_key'],
    required: true,
    unique: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  displayPrice: {
    type: String,
    required: true
  },
  tagline: {
    type: String,
    required: true
  },
  features: {
    type: [String],
    required: true,
    validate: {
      validator: function(features: string[]) {
        return features.length > 0;
      },
      message: 'At least one feature is required'
    }
  },
  priceIdStripe:String,
  duration: {
    type: Number,
    required: true,
    default: function() {
      // Default to 1 month for all plans except temporary_key which gets infinite (-1)
      return this.type === 'temporary_key' ? -1 : 1;
    },
    validate: {
      validator: function(duration: number) {
        // Duration must be -1 (infinite) or positive number
        return duration === -1 || duration > 0;
      },
      message: 'Duration must be -1 (infinite) or a positive number of months'
    }
  },
  categorylimit: {
    type: Number,
    required: true,
    default: function() {
      // Default category limits based on plan type
      if (this.type === 'temporary_key') return 2; // Home Instructions and Quick Start
      return -1; // Unlimited for spare_key and all_access_key
    },
    validate: {
      validator: function(limit: number) {
        // Category limit must be -1 (unlimited) or positive number
        return limit === -1 || limit > 0;
      },
      message: 'Category limit must be -1 (unlimited) or a positive number'
    }
  },
  active: {
    type: Boolean,
    default: true
  }
}, { 
  timestamps: true 
});

// Add index for efficient querying
pricingPlanSchema.index({ type: 1 });
pricingPlanSchema.index({ active: 1 });

const PricingPlan = mongoose.model<IPricingPlan>('PricingPlan', pricingPlanSchema);

export default PricingPlan;