import { useState, useEffect } from 'react';
import { useNavigate, useParams } from "react-router-dom";
import GradiantHeader from "@/mobile/components/header/gradiantHeader";
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import CategoryReviewPage from '@/mobile/components/category/CategoryReviewPage';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import {
  fetchUserInputs,
  selectUserInputs,
  selectQuestions,
  selectLoading,
  selectError
} from '@/store/slices/socialMediaSlice';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Button } from '@/components/ui/button';

// Map subcategory IDs to their routes
const subcategoryRoutes: Record<string, string> = {
  '206A': 'email',
  '206B': 'facebook',
  '206C': 'instagram',
  '206D': 'otheraccounts',
  '206E': 'cellphone',
};

interface Topic {
  id: string;
  title: string;
  subtitle?: string;
  data: string;
  onEdit: () => void;
}

export default function SocialMediaPhoneReviewPage() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { categoryName } = useParams<{ categoryName: string }>();
  const { user } = useAuth();
  const [topics, setTopics] = useState<Topic[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Get data from Redux store
  const userInputs = useAppSelector(selectUserInputs);
  const allQuestions = useAppSelector(selectQuestions);
  const isLoading = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Handle navigation to edit a specific question
  const handleEditQuestion = (questionId: string, subcategoryId: string) => {
    const route = subcategoryRoutes[subcategoryId];
    if (route) {
      const basePath = `/category/${categoryName || 'socialmedia'}/${route}`;
      navigate(`${basePath}?questionId=${questionId}`);
    }
  };

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (!isLoading && userInputs.length > 0) {
      // Process user inputs
      const allTopics: Topic[] = [];
      userInputs.forEach((userInput) => {
        const subcategoryId = userInput.originalSubCategoryId;
        if (userInput.answersBySection && Array.isArray(userInput.answersBySection)) {
          userInput.answersBySection.forEach((section) => {
            if (section.originalSectionId === '206D') {
              // Group all s13/s14/s15 sets
              const accountAnswers = section.answers.filter(a =>
                ['s13', 's14', 's15'].includes(a.originalQuestionId)
              );
              for (let i = 0; i < accountAnswers.length; i += 3) {
                const service = accountAnswers[i]?.answer;
                const login = accountAnswers[i + 1]?.answer;
                const password = accountAnswers[i + 2]?.answer;
                if (service || login || password) {
                  allTopics.push({
                    id: `other-social-${i / 3}`,
                    title: `Other Social Media Account #${i / 3 + 1}`,
                    subtitle: service ? `Service: ${service}` : undefined,
                    data: `Login: ${login || ''} | Password: ${password || ''}`,
                    onEdit: () => handleEditQuestion('s13', '206D')
                  });
                }
              }
              // Also show any other non-account answers in this section
              section.answers.forEach((answer) => {
                if (!['s13', 's14', 's15'].includes(answer.originalQuestionId)) {
                  const questionId = answer.originalQuestionId;
                  const subcategoryQuestions = allQuestions['206'] || [];
                  const questionData = subcategoryQuestions.find((q) => q.id === questionId);
                  if (questionData) {
                    allTopics.push({
                      id: questionId,
                      title: questionData.text,
                      subtitle: `Category: ${getSubcategoryName('206D')}`,
                      data: answer.answer,
                      onEdit: () => handleEditQuestion(questionId, '206D')
                    });
                  }
                }
              });
            } else if (section.answers && Array.isArray(section.answers)) {
              section.answers.forEach((answer) => {
                const questionId = answer.originalQuestionId;
                const subcategoryQuestions = allQuestions['206'] || [];
                const questionData = subcategoryQuestions.find((q) => q.id === questionId);
                if (questionData) {
                  allTopics.push({
                    id: questionId,
                    title: questionData.text,
                    subtitle: `Category: ${getSubcategoryName(subcategoryId)}`,
                    data: answer.answer,
                    onEdit: () => handleEditQuestion(questionId, subcategoryId)
                  });
                }
              });
            }
          });
        }
      });

      // If we didn't find any answers, use the questions as a template
      if (allTopics.length === 0) {
        const allQuestionsFlat = allQuestions['206'] || [];
        allQuestionsFlat.forEach((q) => {
          const subcategoryId = q.sectionId;
          if (subcategoryId) {
            allTopics.push({
              id: q.id,
              title: q.text,
              subtitle: `Category: ${getSubcategoryName(subcategoryId)}`,
              data: "No answer provided",
              onEdit: () => handleEditQuestion(q.id, subcategoryId)
            });
          }
        });
        if (allTopics.length === 0) {
          categoryTabsConfig.socialmedia.forEach((section) => {
            const subcategoryId = getSubcategoryIdFromLabel(section.label);
            if (subcategoryId) {
              allTopics.push({
                id: subcategoryId,
                title: section.label,
                subtitle: 'No answers provided yet',
                data: 'Click edit to add your information',
                onEdit: () => navigate(section.path)
              });
            }
          });
        }
      }

      setTopics(allTopics);
    }
  }, [userInputs, isLoading, allQuestions, navigate, categoryName]);

  // Helper function to get subcategory name from ID
  const getSubcategoryName = (subcategoryId: string): string => {
    switch (subcategoryId) {
      case '206A': return 'Email';
      case '206B': return 'Facebook';
      case '206C': return 'Instagram';
      case '206D': return 'Other Accounts';
      case '206E': return 'Cell Phone';
      default: return 'Unknown';
    }
  };

  // Helper function to get subcategory ID from label
  const getSubcategoryIdFromLabel = (label: string): string | null => {
    switch (label) {
      case 'Email': return '206A';
      case 'Facebook': return '206B';
      case 'Instagram': return '206C';
      case 'Other Accounts': return '206D';
      case 'Cell Phone': return '206E';
      default: return null;
    }
  };

  if (isLoading) {
    return (
      <>
        <GradiantHeader title="Social Media and Phone Review" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  if (error || reduxError) {
    return (
      <>
        <GradiantHeader title="Social Media and Phone Review" showAvatar={true} />
        <div className="p-4">
          <Alert variant="destructive">
            <AlertDescription>{error || reduxError}</AlertDescription>
          </Alert>
        </div>
      </>
    );
  }

  if (!user || !user.id) {
    return (
      <>
        <GradiantHeader title="Social Media and Phone Review" showAvatar={true} />
        <div className="p-4">
          <Alert variant="destructive">
            <AlertDescription>You must be logged in to view your answers</AlertDescription>
          </Alert>
        </div>
      </>
    );
  }

  if (userInputs.length === 0 && !isLoading) {
    return (
      <>
        <GradiantHeader title="Social Media and Phone Review" showAvatar={true} />
        <div className="p-4">
          <Alert>
            <AlertDescription>No social media and phone information found. Please complete some questions first.</AlertDescription>
          </Alert>
        </div>
      </>
    );
  }

  return (
    <CategoryReviewPage
      categoryTitle="Social Media and Phone"
      infoTitle="How to edit your information"
      infoDescription="Review the details about your social media and phone information. Tap Edit on any item to update it."
      topics={topics}
      onPrint={() => window.print()}
      afterTopics={
        <Button 
          onClick={() => navigate('/dashboard')}
          className="px-8 py-3 bg-[#2BCFD5] text-white rounded-md hover:bg-[#1F4168] transition-colors duration-200 shadow-md font-semibold text-md mt-1 mb-1"
        >
          Continue to Dashboard
        </Button>
      }
    />
  );
}
