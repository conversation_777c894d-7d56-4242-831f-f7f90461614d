import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik, Form, Field, ErrorMessage } from "formik";
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import ownershipInfoData from '@/data/ownershipInfo.json';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectUserInputsBySubcategoryId,
  updateUserInput,
  selectError,
} from '@/store/slices/ownershipInfoSlice';
import ScrollToQuestion from '@/mobile/components/dashboard/OwnershipInfo/ScrollToQuestion';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Get Vehicle questions (402B)
const questions = (ownershipInfoData["402"]?.filter(q => q.sectionId === "402B") || []) as any[];

function splitVehicleSteps(questions: any[], values: Record<string, any> = {}) {
  // Step 0: Basic vehicle question
  const step0 = questions.filter(q => q.id === "o15");

  // Dynamic steps based on own/lease selection
  if (values["o15"] === "Lease") {
    // For lease: combine all insurance questions in one step
    const leaseStep = questions.filter(q =>
      ["o23", "o24", "o25", "o26", "o27"].includes(q.id)
    );
    return [step0, leaseStep];
  } else if (values["o15"] === "Own") {
    // For own: car loan questions in step 1, insurance in step 2
    const step1 = questions.filter(q =>
      ["o16", "o17", "o18", "o19", "o20", "o21", "o22"].includes(q.id)
    );
    const step2 = questions.filter(q =>
      ["o23", "o24", "o25", "o26", "o27"].includes(q.id)
    );
    return [step0, step1, step2];
  } else {
    // Default: just the first step until selection is made
    return [step0];
  }
}



export default function VehiclePage() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [formError, setError] = useState<string | null>(null);
  const [step, setStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Get data from Redux store
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId('402B')(state));
  const isLoadingRedux = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');
  const fromReview = queryParams.get('fromReview');

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (userInputs && userInputs.length > 0) {
      const userInput = userInputs[0];

      if (userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      } else if (!existingInputId && userInput._id) {
        setExistingInputId(userInput._id);
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      }
    }
  }, [userInputs, existingInputId]);

  // Set step to the one containing the target question when coming from review
  useEffect(() => {
    if (targetQuestionId && savedAnswers && Object.keys(savedAnswers).length > 0) {
      // Calculate steps based on current saved answers
      const steps = splitVehicleSteps(questions, savedAnswers);

      // Find which step contains the target question
      const targetStepIndex = steps.findIndex(stepQuestions =>
        stepQuestions.some(q => q.id === targetQuestionId)
      );

      if (targetStepIndex !== -1 && targetStepIndex !== step) {
        setStep(targetStepIndex);
      }
    }
  }, [targetQuestionId, savedAnswers, step]);

  // Show loading state if data is being fetched
  if (isLoading || isLoadingRedux) {
    return (
      <>
        <GradiantHeader title="Ownership Information" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  // Steps will be calculated dynamically inside Formik based on current values

  return (
    <>
      <GradiantHeader title="Ownership Information" showAvatar={true} />
      <div className="p-4">
        {/* Tab Bar */}
        <div className="flex flex-row flex-nowrap gap-3 mb-4 bg-gray-50 rounded-lg p-1 overflow-x-auto scrollbar-hide">
          {categoryTabsConfig.ownershipinfo.map(tab => {
            const isActive = tab.label === "Vehicle Information";
            return (
              <button
                key={tab.label}
                className={`flex-1 py-1 px-1 rounded-md font-medium whitespace-nowrap ${
                  isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500"
                }`}
                disabled={isActive}
                onClick={() => !isActive && navigate(tab.path)}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Error message */}
        {(formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        {/* Form */}
        <Formik
          initialValues={Object.keys(savedAnswers).length > 0 ? savedAnswers : {}}
          enableReinitialize={true}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              if (!user || !user.id) {
                setError('You must be logged in to save answers');
                return;
              }

              // Group answers by sectionId
              const answersBySection: Record<string, any[]> = {};
              questions.forEach(q => {
                const value = values[q.id];
                if (value !== "" && value !== undefined) {
                  if (!answersBySection[q.sectionId]) answersBySection[q.sectionId] = [];
                  answersBySection[q.sectionId].push({
                    index: q.order,
                    originalQuestionId: q.id,
                    question: q.text,
                    type: q.type === 'dropdown' ? 'choice' : q.type === 'currency' ? 'text' : q.type,
                    answer: Array.isArray(value) ? value.join(', ') : value,
                    is_encrypted: q.text.toLowerCase().includes('password')
                  });
                }
              });

              const formattedAnswersBySection = Object.entries(answersBySection).map(
                ([sectionId, answers]) => ({
                  originalSectionId: sectionId,
                  isCompleted: true,
                  answers
                })
              );

              const userData = {
                userId: user.id,
                categoryId: generateObjectId(),
                originalCategoryId: '10',
                subCategoryId: generateObjectId(),
                originalSubCategoryId: '402B',
                answersBySection: formattedAnswersBySection
              };

              if (existingInputId) {
                await dispatch(updateUserInput({
                  id: existingInputId,
                  userData
                })).unwrap();
              } else {
                await dispatch(saveUserInput(userData)).unwrap();
              }
              
              if (fromReview) {
                navigate('/category/ownershipinfo/review');
              } else {
                navigate("/category/ownershipinfo/driverinformation");
              }
              setSubmitting(false);
            } catch (err) {
              setError("Failed to save your answers. At least one question must be answered.");
              setSubmitting(false);
            }
          }}
        >
          {({ values, isSubmitting, setValues }) => {
            // Calculate steps dynamically based on current values
            const steps = splitVehicleSteps(questions, values);
            const currentStepQuestions = steps[step] || [];

            const prevValuesRef = useRef<Record<string, any>>({});

            // Handle dependent answers when specific values change
            useEffect(() => {
              const prevValues = prevValuesRef.current;
              let shouldCleanDependents = false;

              // Check if any dependency-triggering questions have changed
              questions.forEach(question => {
                if (question.dependsOn) {
                  const { questionId } = question.dependsOn;
                  if (prevValues[questionId] !== values[questionId]) {
                    shouldCleanDependents = true;
                  }
                }
              });

              // Dependencies are handled by the visibleQuestions filter

              prevValuesRef.current = { ...values };
            }, [values, setValues]);

            const visibleQuestions = currentStepQuestions.filter((q: any) => {
              if (!q.dependsOn) return true;
              return values[q.dependsOn.questionId] === q.dependsOn.value;
            });

            // Reset step when own/lease selection changes
            useEffect(() => {
              if (step > 0 && values["o15"]) {
                // If user changes from own to lease or vice versa, reset to step 1
                const newSteps = splitVehicleSteps(questions, values);
                if (step >= newSteps.length) {
                  setStep(1);
                }
              }
            }, [values["o15"]]);

            const handleNext = async () => {
              setStep(s => s + 1);
            };

            const handleSave = async () => {
              document.querySelector('form')?.requestSubmit();
            };

            return (
              <Form>
                <div className="bg-gray-50 p-5 rounded-xl shadow-sm border mb-4">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-semibold">
                      Ownership Info: <span className="text-[#2BCFD5]">Vehicle</span>
                    </p>
                    <CircularProgress
                      value={step + 1}
                      max={steps.length}
                      size={40}
                      stroke={3}
                      color="#2BCFD5"
                    />
                  </div>
                </div>
                <ScrollToQuestion questions={visibleQuestions}>
                  {(questionRefs) => (
                    <div className="space-y-4 mt-8 bg-gray-50 p-5 rounded-xl shadow-sm border">
                      {visibleQuestions.map(q => (
                        <div key={q.id} ref={el => { questionRefs[q.id] = el; }}>
                          <label className="block font-medium text-gray-700 mb-2">
                            {q.text}
                          </label>
                          {q.type === "boolean" ? (
                            <div className="flex space-x-4">
                              {["yes", "no"].map(option => (
                                <label
                                  key={option}
                                  className={
                                    "flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer " +
                                    (values[q.id] === option
                                      ? "bg-[#2BCFD5] text-white border-[#2BCFD5]"
                                      : "bg-gray-50 hover:bg-[#25b6bb] hover:text-white")
                                  }
                                  style={{ transition: "all 0.2s" }}
                                >
                                  <Field type="radio" name={q.id} value={option} className="hidden" />
                                  {option.charAt(0).toUpperCase() + option.slice(1)}
                                </label>
                              ))}
                            </div>
                          ) : q.type === "dropdown" && Array.isArray(q.options) ? (
                            <Field as="select" name={q.id} className="w-full border rounded-lg px-3 py-2">
                              <option value="">Select...</option>
                              {q.options.map((option: string) => (
                                <option key={option} value={option}>{option}</option>
                              ))}
                            </Field>
                          ) : (
                            <Field
                              name={q.id}
                              type={
                                q.text.toLowerCase().includes("password") ? "password"
                                : q.type === "currency" ? "number"
                                : "text"
                              }
                              className="w-full border rounded-lg px-3 py-2"
                            />
                          )}
                          <ErrorMessage name={q.id} component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollToQuestion>

                <div className="mt-6 flex justify-between items-center">
                  <button
                    type="button"
                    onClick={() => setStep(s => s - 1)}
                    disabled={step === 0}
                    className="text-[#2BCFD5] underline disabled:opacity-50"
                  >
                    ← Back
                  </button>
                  {step < steps.length - 1 ? (
                    <button
                      type="button"
                      onClick={handleNext}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      Next →
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={handleSave}
                      disabled={isSubmitting}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      Save
                    </button>
                  )}
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <Footer />
    </>
  );
}
