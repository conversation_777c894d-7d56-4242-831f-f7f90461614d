import { useState, useEffect, useRef, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Label } from '@/components/ui/label';
import defaultAvatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import Header from '@/web/components/Layout/AppHeader';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, UserPen, BadgeCheck, ChevronDown, ChevronUp } from 'lucide-react';
import authService from '@/services/authService';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import Select from 'react-select';
import countryList from 'react-select-country-list';
import { useDispatch, useSelector } from "react-redux";
import { fetchOwnerSubscription, fetchPricingPlans } from "@/store/slices/subscriptionSlice";
import { RootState, AppDispatch } from "@/store";
import ownerService from '@/services/ownerService';
import ImageCropper from '@/components/common/ImageCropper';
import { EmergencyAccessManagement } from '@/components/common/EmergencyAccessManagement';
import { useAccessControl } from '@/hooks/useAccessControl';

// Define the option type for react-select
interface CountryOption {
  value: string;
  label: string;
}

export default function WebUserProfile() {
  const { user, logout, isLoading, setUser } = useAuth();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [firstName, setFirstName] = useState(user?.firstName || '');
  const [lastName, setLastName] = useState(user?.lastName || '');
  const [email, setEmail] = useState(user?.email || '');
  const [username, setUsername] = useState(user?.username || '');
  const [phone, setPhone] = useState(user?.phone || '');
  const [address, setAddress] = useState(user?.address || '');
  const [zipCode, setZipCode] = useState(user?.zipCode || '');
  const [country, setCountry] = useState(user?.country || '');

  // Get country options using the countryList package
  const countryOptions = useMemo(() => countryList().getData(), []);
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Image cropper states
  const [showCropper, setShowCropper] = useState(false);
  const [originalImageSrc, setOriginalImageSrc] = useState<string>('');

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // For real-time validation
  const [phoneError, setPhoneError] = useState<string | null>(null);
  const [zipCodeError, setZipCodeError] = useState<string | null>(null);

  const dispatch = useDispatch<AppDispatch>();
  const { currentSubscription, plans, loading: subLoading } = useSelector((state: RootState) => state.subscription);

  const [ownerId, setOwnerId] = useState<string | null>(null);
  const [ownerLoading, setOwnerLoading] = useState(true);

  // Add a derived state to check if any key holder has emergency access granted
  const { userAccess, handleRequestEmergencyAccess } = useAccessControl();
  // Emergency Access Management toggle state
  const [showEmergency, setShowEmergency] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth >= 1024; // open by default on desktop
    }
    return false;
  });
  // Responsive update on resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) setShowEmergency(false);
      else setShowEmergency(true);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (user) {
      setFirstName(user.firstName || '');
      setLastName(user.lastName || '');
      setEmail(user.email || '');
      setUsername(user.username || '');
      setPhone(user.phone || '');
      setAddress(user.address || '');
      setZipCode(user.zipCode || '');
      setCountry(user.country || '');
      if (user.image) {
        // Construct the full URL for the uploaded image
        const imageUrl = user.image.startsWith('http')
          ? user.image
          : `${import.meta.env.VITE_API_URL}/uploads/${user.image}`;
        setImagePreview(imageUrl);
      } else {
        // Explicitly set to null when no image so defaultAvatar is used
        setImagePreview(null);
      }
    }
  }, [user]);

  useEffect(() => {
    const fetchOwner = async () => {
      if (user?.id) {
        setOwnerLoading(true);
        try {
          // If user has an ownerId, use it directly (for nominees/keyholders)
          if (user.ownerId) {
            setOwnerId(user.ownerId);
          } else {
            // Otherwise, fetch the owner by userId (for owners)
            const owner = await ownerService.getOwnerByUserId(user.id);
            setOwnerId(owner._id);
          }
        } catch (err) {
          setOwnerId(null);
        } finally {
          setOwnerLoading(false);
        }
      }
    };
    fetchOwner();
  }, [user]);

  useEffect(() => {
    if (ownerId) {
      dispatch(fetchOwnerSubscription(ownerId));
    }
    dispatch(fetchPricingPlans());
  }, [dispatch, ownerId]);

  const subscribedPlan = plans.find(plan => plan._id === currentSubscription?.planId);

  // Cleanup timeouts on component unmount
  useEffect(() => {
    return () => {
      // Clear all pending timeouts when component unmounts
      Object.values(timeoutRef.current).forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (5MB = 5 * 1024 * 1024 bytes)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        setError(`Upload image less than 5MB. Current file size: ${formatFileSize(file.size)}`);
        setSuccess(null);
        // Clear the input
        e.target.value = '';
        return;
      }

      // Check file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setError('Please upload a valid image file (JPEG, PNG, GIF, WebP)');
        setSuccess(null);
        // Clear the input
        e.target.value = '';
        return;
      }

      // Clear any previous errors
      setError(null);
      setSuccess(null);

      // Create a preview for cropping
      const reader = new FileReader();
      reader.onloadend = () => {
        setOriginalImageSrc(reader.result as string);
        setShowCropper(true);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCropComplete = (croppedImageBlob: Blob) => {
    // Convert blob to file
    const croppedFile = new File([croppedImageBlob], 'profile-image.jpg', {
      type: 'image/jpeg',
    });

    setProfileImage(croppedFile);

    // Create preview URL for the cropped image
    const previewUrl = URL.createObjectURL(croppedImageBlob);
    setImagePreview(previewUrl);
    setShowCropper(false);
  };

  // Validation functions
  const validatePhone = (value: string): boolean => {
    // With react-phone-input-2, the validation is handled by the component
    // We just need to check if the phone number has a minimum length
    const isValid = value && value.length >= 8 ? true : false;
    setPhoneError(isValid ? null : 'Please enter a valid phone number');
    return isValid;
  };

  const validateZipCode = (value: string): boolean => {
    // Basic ZIP code validation - can be enhanced based on country
    const zipRegex = /^[0-9a-zA-Z\s\-]{3,10}$/;
    const isValid = zipRegex.test(value);
    setZipCodeError(isValid ? null : 'Please enter a valid ZIP/postal code');
    return isValid;
  };

  // Create a reference for timeout IDs
  const timeoutRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Handle field changes with validation and real-time update
  const handleFieldChange = (field: string, value: string) => {
    switch (field) {
      case 'phone':
        setPhone(value);
        validatePhone(value);
        break;
      case 'zipCode':
        setZipCode(value);
        validateZipCode(value);
        break;
      case 'address':
        setAddress(value);
        break;
      case 'country':
        setCountry(value);
        break;
      default:
        break;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEditing) return;
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    // Validate fields
    const isPhoneValid = phone ? validatePhone(phone) : true;
    const isZipValid = zipCode ? validateZipCode(zipCode) : true;

    if (!isPhoneValid || !isZipValid) {
      setIsSubmitting(false);
      setError('Please correct the errors before submitting');
      return;
    }

    try {
      // First update the profile image if changed
      if (profileImage) {
        const formData = new FormData();
        formData.append('image', profileImage);
        const updatedUser = await authService.updateProfileImage(formData);
        setUser(updatedUser);
                  // Update the image preview with the new image URL
          if (updatedUser.image) {
            const imageUrl = updatedUser.image.startsWith('http') 
              ? updatedUser.image 
              : `${import.meta.env.VITE_API_URL}/uploads/${updatedUser.image}`;
            setImagePreview(imageUrl);
          }
      }

      // Then update the profile information
      const updatedUser = await authService.updateProfile({
        firstName: firstName || undefined,
        lastName: lastName || undefined,
        username: username || undefined,
        phone: phone || undefined,
        address: address || undefined,
        zipCode: zipCode || undefined,
        country: country || undefined
      });

      setUser(updatedUser);
      setSuccess('Profile updated successfully!');

      setIsEditing(false);
      setTimeout(() => {
        navigate('/dashboard');
      }, 1500);
    } catch (err: any) {
      console.error('Profile update error:', err);

      // Handle specific error messages from backend
      let errorMessage = 'Failed to update profile';

      if (err?.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err?.message) {
        errorMessage = err.message;
      }

      // Handle specific file upload errors
      if (err?.response?.data?.error === 'FILE_TOO_LARGE') {
        errorMessage = 'Upload image less than 5MB';
      } else if (err?.response?.data?.error === 'INVALID_FILE_TYPE') {
        errorMessage = 'Please upload a valid image file (JPEG, PNG, GIF, WebP)';
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get initials for avatar fallback
  const getInitials = () => {
    if (firstName && lastName) {
      return `${firstName[0]}${lastName[0]}`.toUpperCase();
    } else if (username) {
      return username.substring(0, 2).toUpperCase();
    }
    return 'UN';
  };

  return (
    <div className="min-h-screen bg-[#f8f9fb]">
      <Header />
      {/* Welcome Banner */}
      <div className="w-full bg-gradient-to-r from-[#1F4168] to-[#22BBCC] text-white">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold">Welcome, {firstName || username || 'User'}</h1>
        </div>
      </div>
      <div className="max-w-7xl mx-auto py-4 px-4 flex flex-col lg:flex-row gap-6">
        <div className="flex-1">
          <div className="bg-white rounded-lg shadow-sm p-6">
            {/* Emergency Request Sent Status for Invited Users */}
            {userAccess && !userAccess.isOwner && userAccess.subscriptionType === 'spare_key' && (
              <div className="mb-6 p-4 rounded-lg border border-blue-200 bg-blue-50">
                <h3 className="font-semibold text-blue-900 mb-2">Emergency Access Request</h3>
                {userAccess.emergencyAccessRequested && !userAccess.emergencyAccessGranted && !userAccess.emergencyAccessDenied && (
                  <div className="flex items-center gap-2 text-blue-700">
                    <span className="w-3 h-3 rounded-full bg-yellow-400 inline-block" />
                    <span>Pending (waiting for owner approval)</span>
                  </div>
                )}
                {userAccess.emergencyAccessGranted && (
                  <div className="flex items-center gap-2 text-green-700">
                    <span className="w-3 h-3 rounded-full bg-green-500 inline-block" />
                    <span>Granted (you have emergency access)</span>
                  </div>
                )}
                {userAccess.emergencyAccessDenied && (
                  <div className="flex items-center gap-2 text-red-700">
                    <span className="w-3 h-3 rounded-full bg-red-500 inline-block" />
                    <span>Denied (owner rejected your request)</span>
                  </div>
                )}
                {!userAccess.emergencyAccessRequested && !userAccess.emergencyAccessGranted && !userAccess.emergencyAccessDenied && (
                  <div className="flex items-center gap-2">
                    <span className="w-3 h-3 rounded-full bg-gray-400 inline-block" />
                    <span>No emergency access request sent.</span>
                    <Button
                      size="sm"
                      className="ml-2 bg-blue-600 text-white hover:bg-blue-700"
                      onClick={handleRequestEmergencyAccess}
                    >
                      Request Emergency Access
                    </Button>
                  </div>
                )}
              </div>
            )}
            {/* Emergency Access Management for Owner with hide/open toggle */}
            {userAccess?.isOwner && userAccess.subscriptionType === 'spare_key' && (
              <div className="mb-8 w-full">
                <button
                  className="flex items-center gap-2 px-4 py-2 rounded-md bg-[#f8f9fb] border border-[#e2e8f0] hover:bg-[#f1f5f9] transition-colors font-medium mb-2"
                  onClick={() => setShowEmergency((prev) => !prev)}
                  aria-expanded={showEmergency}
                  aria-controls="emergency-access-section"
                >
                  {showEmergency ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                  {showEmergency ? 'Hide Emergency Access Management' : 'Show Emergency Access Management'}
                </button>
                <div
                  id="emergency-access-section"
                  style={{
                    maxHeight: showEmergency ? 1000 : 0,
                    overflow: 'hidden',
                    transition: 'max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                  }}
                  aria-hidden={!showEmergency}
                >
                  <div className="min-w-[320px] max-w-full">
                    <EmergencyAccessManagement className="w-full" />
                  </div>
                </div>
              </div>
            )}
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-900">Profile Settings</h2>
              {!isEditing && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => setIsEditing(true)}
                  disabled={isLoading}
                >
                  <UserPen size={14} /> Edit
                </Button>
              )}
            </div>

            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="mb-4 bg-green-50 border-green-200">
                <AlertDescription className="text-green-700">{success}</AlertDescription>
              </Alert>
            )}

            <form className="space-y-5" onSubmit={handleSubmit}>
              <div className="flex items-center gap-6 mb-2">
                <div className="relative group">
                  <div className="h-20 w-20 border-2 border-[#22BBCC] shadow rounded-full overflow-hidden bg-white">
                    <img
                      src={imagePreview || defaultAvatar}
                      alt="Profile"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = defaultAvatar;
                      }}
                    />
                  </div>
                  <div className="absolute inset-0 flex items-center border border-[#22BBCC] shadow-md justify-center bg-black bg-opacity-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {isEditing && (
                      <Label htmlFor="profileImage" className="cursor-pointer text-white text-xs font-medium">
                        Change
                      </Label>
                    )}
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-medium">{firstName} {lastName}</h3>
                  <p className="text-sm text-gray-500">{email}</p>
                  {isEditing && (
                    <>
                      <Input
                        id="profileImage"
                        name="profileImage"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleImageChange}
                      />
                      {/* <p className="text-xs text-gray-500 mt-1">
                        Max file size: 5MB. Supported formats: JPEG, PNG, GIF, WebP
                      </p> */}
                    </>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">First Name</label>
                  <Input
                    id="firstName"
                    name="firstName"
                    type="text"
                    placeholder="First name"
                    className={`h-10 ${!isEditing ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    readOnly={!isEditing}
                    disabled={!isEditing}
                    maxLength={30}
                  />
                </div>

                <div className="space-y-1">
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">Last Name</label>
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    placeholder="Last name"
                    className={`h-10 ${!isEditing ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    readOnly={!isEditing}
                    disabled={!isEditing}
                    maxLength={30}
                  />
                </div>
              </div>

              <div className="space-y-1">
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700">Phone Number</label>
                <PhoneInput
                  country={'us'}
                  value={phone}
                  onChange={(value) => isEditing && handleFieldChange('phone', value)}
                  inputProps={{
                    name: 'phone',
                    id: 'phone',
                    required: true,
                    readOnly: !isEditing,
                    disabled: !isEditing
                  }}
                  containerClass={`${phoneError ? 'border-red-500' : ''} ${!isEditing ? 'opacity-60' : ''}`}
                  inputClass="w-full h-18"
                  buttonClass="h-18"
                  containerStyle={{ 
                    height: '35px'
                  }}
                  disabled={!isEditing}
                />
                {phoneError && <p className="text-xs text-red-500 mt-1">{phoneError}</p>}
              </div>

              <div className="space-y-1">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700">Address</label>
                <Input
                  id="address"
                  name="address"
                  type="text"
                  placeholder="Full address"
                  className={`h-10 ${!isEditing ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                  value={address}
                  onChange={(e) => handleFieldChange('address', e.target.value)}
                  readOnly={!isEditing}
                  disabled={!isEditing}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700">ZIP/Postal Code</label>
                  <Input
                    id="zipCode"
                    name="zipCode"
                    type="text"
                    placeholder="ZIP/postal code"
                    className={`h-10 ${zipCodeError ? 'border-red-500' : ''} ${!isEditing ? 'bg-gray-50 cursor-not-allowed' : ''}`}
                    value={zipCode}
                    onChange={(e) => handleFieldChange('zipCode', e.target.value)}
                    onBlur={(e) => validateZipCode(e.target.value)}
                    readOnly={!isEditing}
                    disabled={!isEditing}
                  />
                  {zipCodeError && <p className="text-xs text-red-500 mt-1">{zipCodeError}</p>}
                </div>

                <div className="space-y-1">
                  <label htmlFor="country" className="block text-sm font-medium text-gray-700">Country</label>
                  <Select<CountryOption>
                    id="country"
                    name="country"
                    options={countryOptions}
                    placeholder="Select country"
                    className="h-10"
                    classNamePrefix="select"
                    value={countryOptions.find((option: CountryOption) => option.label === country) || null}
                    onChange={isEditing ? (selectedOption: CountryOption | null) => {
                      if (selectedOption) {
                        handleFieldChange('country', selectedOption.label);
                      }
                    } : undefined}
                    isDisabled={!isEditing}
                    styles={{
                      control: (baseStyles: any) => ({
                        ...baseStyles,
                        height: '40px',
                        minHeight: '40px',
                        borderColor: '#e2e8f0',
                        backgroundColor: !isEditing ? '#f3f4f6' : 'white',
                        opacity: !isEditing ? 0.6 : 1,
                        cursor: !isEditing ? 'not-allowed' : 'default',
                      }),
                      valueContainer: (base: any) => ({
                        ...base,
                        padding: '0 8px',
                      }),
                      menu: (baseStyles: any) => ({
                        ...baseStyles,
                        zIndex: 50,
                      }),
                    }}
                  />
                </div>
              </div>

              <div className="flex justify-end pt-2">
                {isEditing && (
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      className="h-10 px-6"
                      onClick={() => {
                        setIsEditing(false);
                        setFirstName(user?.firstName || '');
                        setLastName(user?.lastName || '');
                        setEmail(user?.email || '');
                        setUsername(user?.username || '');
                        setPhone(user?.phone || '');
                        setAddress(user?.address || '');
                        setZipCode(user?.zipCode || '');
                        setCountry(user?.country || '');
                        // Reset image preview to user's current image or null
                        if (user?.image) {
                          const imageUrl = user.image.startsWith('http')
                            ? user.image
                            : `${import.meta.env.VITE_API_URL}/uploads/${user.image}`;
                          setImagePreview(imageUrl);
                        } else {
                          setImagePreview(null);
                        }
                        setProfileImage(null);
                        setError(null);
                        setSuccess(null);
                        setPhoneError(null);
                        setZipCodeError(null);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="h-10 px-6 bg-[#22BBCC] text-white hover:bg-[#22BBCA] transition-colors duration-200"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
                        </>
                      ) : (
                        "Update Profile"
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </form>

            {/* Subscription Section (REMOVED) */}
          </div>
        </div>
        <div className="w-full lg:w-[350px] flex-shrink-0">
          <SearchPanel />
        </div>
      </div>

      {/* Image Cropper Modal */}
      <ImageCropper
        isOpen={showCropper}
        onClose={() => setShowCropper(false)}
        imageSrc={originalImageSrc}
        onCropComplete={handleCropComplete}
        aspectRatio={1}
      />
    </div>
  );
}