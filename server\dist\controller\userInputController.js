"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDashboardStats = exports.getUserInputByOwnerId = exports.getUserInputByUserId = exports.updateUserInput = exports.getUserInput = exports.createUserInput = void 0;
const userInput_1 = __importDefault(require("../models/userInput"));
const User_1 = __importDefault(require("../models/User"));
const mongoose_1 = __importDefault(require("mongoose"));
const createUserInput = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId } = req.body;
        if (!userId) {
            res.status(400).json({ message: 'userId is required' });
            return;
        }
        // Get the user to find their ownerId
        const user = yield User_1.default.findById(userId);
        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }
        // Add ownerId to the request body if user has one
        const userInputData = Object.assign({}, req.body);
        if (user.ownerId) {
            userInputData.ownerId = user.ownerId;
        }
        else {
            // Log warning for users without owner ID
            console.warn(`User ${userId} does not have an associated owner. Creating UserInput without ownerId.`);
        }
        const userInput = new userInput_1.default(userInputData);
        yield userInput.save();
        res.status(201).json(userInput);
    }
    catch (error) {
        res.status(500).json({ message: 'Error creating user input', error });
    }
});
exports.createUserInput = createUserInput;
const getUserInput = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const userInput = yield userInput_1.default.findById(req.params.id);
        res.status(200).json(userInput);
    }
    catch (error) {
        res.status(500).json({ message: 'Error fetching user input', error });
    }
});
exports.getUserInput = getUserInput;
const updateUserInput = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId } = req.body;
        // If userId is provided in the update, ensure ownerId is also set
        let updateData = Object.assign({}, req.body);
        if (userId) {
            // Get the user to find their ownerId
            const user = yield User_1.default.findById(userId);
            if (!user) {
                res.status(404).json({ message: 'User not found' });
                return;
            }
            // Add ownerId to the update data if user has one
            if (user.ownerId) {
                updateData.ownerId = user.ownerId;
            }
        }
        const userInput = yield userInput_1.default.findByIdAndUpdate(req.params.id, updateData, { new: true });
        res.status(200).json(userInput);
    }
    catch (error) {
        res.status(500).json({ message: 'Error updating user input', error });
    }
});
exports.updateUserInput = updateUserInput;
const getUserInputByUserId = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { userId, categoryId } = req.query;
        if (!userId || !categoryId) {
            res.status(400).json({ message: 'userId and categoryId are required.' });
            return;
        }
        const userInputs = yield userInput_1.default.find({
            userId: new mongoose_1.default.Types.ObjectId(userId),
            originalCategoryId: categoryId
            // categoryId: new mongoose.Types.ObjectId(categoryId as string)
        });
        res.status(200).json(userInputs);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
exports.getUserInputByUserId = getUserInputByUserId;
// export const getUserInputsByUserAndCategoryAndSubcategory: RequestHandler = async (req, res) => {
//   try {
//     const { categoryId, subcategoryId, id } = req.query;
//     // const userId = req.user._id
//     //it should decode from protected route
//     console.log(categoryId, subcategoryId, id);
//     if (  !categoryId || !subcategoryId || !id) {
//       res.status(400).json({ message: 'userId, categoryId, and subcategoryId are required.' });
//       return;
//     }
//     const userInputs = await UserInput.findOne({
//       _id:id,
//       // userId: new mongoose.Types.ObjectId(userId as string),
//       categoryId: new mongoose.Types.ObjectId(categoryId as string),
//       subcategoryId: new mongoose.Types.ObjectId(subcategoryId as string)
//     });
//     res.status(200).json(userInputs);
//   } catch (error) {
//     res.status(500).json({ error: (error as Error).message });
//   }
// };
/**
 * Get dashboard stats for a user
 * This endpoint returns the count of answered questions for each category
 * without fetching all the actual question data
 */
// New function to get user inputs by owner ID
const getUserInputByOwnerId = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { ownerId, categoryId } = req.query;
        if (!ownerId || !categoryId) {
            res.status(400).json({ message: 'ownerId and categoryId are required.' });
            return;
        }
        const userInputs = yield userInput_1.default.find({
            ownerId: new mongoose_1.default.Types.ObjectId(ownerId),
            originalCategoryId: categoryId
        });
        res.status(200).json(userInputs);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
exports.getUserInputByOwnerId = getUserInputByOwnerId;
/**
 * Get dashboard stats by owner ID
 * This endpoint returns the count of answered questions for each category for an owner
 * Replaces the old user_id based dashboard stats
 */
const getDashboardStats = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { ownerId } = req.query;
        if (!ownerId) {
            res.status(400).json({ message: 'ownerId is required' });
            return;
        }
        // Get all user inputs for this owner
        const userInputs = yield userInput_1.default.find({
            ownerId: new mongoose_1.default.Types.ObjectId(ownerId)
        });
        // Calculate stats for each category with proper deduplication
        const categoryStats = {};
        // Process each user input
        userInputs.forEach(input => {
            const categoryId = input.originalCategoryId || '';
            const subCategoryId = input.originalSubCategoryId || '';
            if (!categoryStats[categoryId]) {
                categoryStats[categoryId] = {
                    categoryId,
                    answeredQuestions: 0
                };
            }
            // For each category, we need to deduplicate answers by question ID
            // to avoid counting the same question multiple times if there are duplicate user inputs
            const answeredQuestionIds = new Set();
            // Count unique answered questions in this input
            if (input.answersBySection && Array.isArray(input.answersBySection)) {
                input.answersBySection.forEach(section => {
                    if (section.answers && Array.isArray(section.answers)) {
                        section.answers.forEach(answer => {
                            if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
                                answeredQuestionIds.add(answer.originalQuestionId);
                            }
                        });
                    }
                });
            }
            // Add the count of unique answered questions for this input
            categoryStats[categoryId].answeredQuestions += answeredQuestionIds.size;
        });
        // For categories that might have multiple user inputs (like insurance with multiple sections),
        // we need to deduplicate across all user inputs for that category
        const finalCategoryStats = {};
        Object.entries(categoryStats).forEach(([categoryId, stats]) => {
            // Get all user inputs for this category
            const categoryInputs = userInputs.filter(input => input.originalCategoryId === categoryId);
            // Collect all unique answered question IDs across all inputs for this category
            const allAnsweredQuestionIds = new Set();
            categoryInputs.forEach(input => {
                if (input.answersBySection && Array.isArray(input.answersBySection)) {
                    input.answersBySection.forEach(section => {
                        if (section.answers && Array.isArray(section.answers)) {
                            section.answers.forEach(answer => {
                                if (answer.originalQuestionId && answer.answer && answer.answer.trim() !== '') {
                                    allAnsweredQuestionIds.add(answer.originalQuestionId);
                                }
                            });
                        }
                    });
                }
            });
            finalCategoryStats[categoryId] = {
                categoryId,
                answeredQuestions: allAnsweredQuestionIds.size
            };
        });
        // Convert to array for response
        const statsArray = Object.values(finalCategoryStats);
        res.status(200).json(statsArray);
    }
    catch (error) {
        console.error('Error getting dashboard stats:', error);
        res.status(500).json({
            message: 'Error getting dashboard stats',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getDashboardStats = getDashboardStats;
//# sourceMappingURL=userInputController.js.map