"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const db_1 = __importDefault(require("./config/db"));
const app_1 = __importDefault(require("./app"));
const User_1 = __importDefault(require("./models/User"));
const Owner_1 = __importDefault(require("./models/Owner"));
const email_1 = require("./utils/email");
dotenv_1.default.config();
const PORT = process.env.PORT || 3000;
// const PUBLIC_IP = '*************';
const PUBLIC_IP = '*************';
(0, db_1.default)();
// Auto-grant emergency access after 20 minutes
setInterval(() => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const now = Date.now();
        const twentyFourHours = 24 * 60 * 60 * 1000;
        const usersToAutoGrant = yield User_1.default.find({
            emergencyAccessRequested: true,
            emergencyAccessGranted: false,
            emergencyAccessDenied: false,
            emergencyAccessRequestedAt: { $lte: new Date(now - twentyFourHours) }
        });
        for (const user of usersToAutoGrant) {
            user.emergencyAccessGranted = true;
            user.emergencyAccessGrantedAt = new Date();
            yield user.save();
            // Find owner for email
            const owner = yield Owner_1.default.findById(user.ownerId);
            const ownerName = (owner === null || owner === void 0 ? void 0 : owner.firstName) || (owner === null || owner === void 0 ? void 0 : owner.username) || 'Owner';
            const keyHolderName = user.firstName || user.username || user.email || 'Key Holder';
            try {
                yield (0, email_1.sendEmergencyAccessAutoGrantEmail)(user.email, keyHolderName, ownerName);
                console.log(`Auto-granted emergency access for ${user.email}`);
            }
            catch (err) {
                console.error('Failed to send auto-grant email:', err);
            }
        }
    }
    catch (err) {
        console.error('Auto-grant interval error:', err);
    }
}), 60 * 1000); // Check every minute
app_1.default.listen(Number(PORT), '0.0.0.0', () => {
    console.log(`Server is running on http://localhost:${PORT}`);
    console.log(`Server is running on http://0.0.0.0:${PORT}`);
    console.log(`You can access it at http://${PUBLIC_IP}:${PORT}`);
});
//# sourceMappingURL=server.js.map