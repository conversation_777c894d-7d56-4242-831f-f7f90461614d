import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import User from '../models/User';
import { CustomError } from '../utils/customError';


export const protect = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const testToken = req.headers.authorization;
    let token;

    if (testToken && testToken.startsWith("Bearer")) {
      token = testToken.split(" ")[1];
    }

    if (!token) {
      const error = new CustomError("You are not logged in", 401);
      return next(error);
    }

    const decodedToken: any = jwt.verify(
      token,
      process.env.JWT_SECRET as string
    );

    const user = await User.findById(decodedToken.userId);

    if (!user) {
      const error = new CustomError("The user with the given token does not exist", 401);
      return next(error);
    }
    
    (req as any).user = user;
    next();
  } catch (error) {
    next(error);
  }
};
