import mongoose, { Schema } from 'mongoose';
import { ISubscribedPlan } from '../types/SubscribedPlan';

const subscribedPlanSchema = new Schema<ISubscribedPlan>({
  planId: {
    type: Schema.Types.ObjectId,
    ref: 'PricingPlan',
    required: true
  },
  ownerId: {
    type: Schema.Types.ObjectId,
    ref: 'Owner',
    required: true
  },
  previousPlans: {
    type: [Schema.Types.ObjectId],
    ref: 'PricingPlan',
    default: []
  },
  currentPlan: {
    type: Schema.Types.ObjectId,
    ref: 'PricingPlan',
    required: true
  },
  expiryAt: {
    type: Date,
    required: false // Will be set by pre-save middleware
  }
}, { 
  timestamps: true 
});

// Pre-save middleware to calculate expiryAt based on plan duration
subscribedPlanSchema.pre('save', async function(next) {
  try {
    // Always calculate expiryAt if it's not set or if currentPlan changed
    if (!this.expiryAt || this.isModified('currentPlan') || this.isNew) {
      // Get the current plan details to calculate expiry
      const PricingPlan = mongoose.model('PricingPlan');
      const plan = await PricingPlan.findById(this.currentPlan);

      if (plan) {
        const now = new Date();

        if (plan.duration === -1) {
          // Infinite duration - set expiry to far future (100 years from now)
          this.expiryAt = new Date(now.getFullYear() + 100, now.getMonth(), now.getDate());
        } else {
          // Calculate expiry based on plan duration in months
          const expiryDate = new Date(now);
          expiryDate.setMonth(expiryDate.getMonth() + (plan.duration || 1));
          this.expiryAt = expiryDate;
        }
      } else {
        // Fallback: set expiry to 1 month from now if plan not found
        const fallbackExpiry = new Date();
        fallbackExpiry.setMonth(fallbackExpiry.getMonth() + 1);
        this.expiryAt = fallbackExpiry;
      }
    }
  } catch (error) {
    console.error('Error calculating expiry date:', error);
    // Fallback: set expiry to 1 month from now
    const fallbackExpiry = new Date();
    fallbackExpiry.setMonth(fallbackExpiry.getMonth() + 1);
    this.expiryAt = fallbackExpiry;
  }
  next();
});

// Add indexes for efficient querying
subscribedPlanSchema.index({ ownerId: 1 });
subscribedPlanSchema.index({ currentPlan: 1 });
subscribedPlanSchema.index({ expiryAt: 1 });
subscribedPlanSchema.index({ ownerId: 1, currentPlan: 1 }, { unique: true }); // One active subscription per owner

// Virtual to check if subscription is active
subscribedPlanSchema.virtual('isActive').get(function() {
  return new Date() < this.expiryAt;
});

// Virtual to get days remaining
subscribedPlanSchema.virtual('daysRemaining').get(function() {
  const now = new Date();
  const diffTime = this.expiryAt.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays > 0 ? diffDays : 0;
});

// Ensure virtuals are included in JSON output
subscribedPlanSchema.set('toJSON', { virtuals: true });
subscribedPlanSchema.set('toObject', { virtuals: true });

const SubscribedPlan = mongoose.model<ISubscribedPlan>('SubscribedPlan', subscribedPlanSchema);

export default SubscribedPlan;