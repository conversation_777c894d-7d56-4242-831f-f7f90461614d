"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const pricingPlanController_1 = require("../controller/pricingPlanController");
const pricingPlanValidation_1 = require("../validation/pricingPlanValidation");
const router = express_1.default.Router();
// Initialize default pricing plans (should be called once during setup)
router.post('/initialize', pricingPlanController_1.initializeDefaultPricingPlans);
// Update all existing plans with duration (migration endpoint)
router.post('/update-duration', pricingPlanController_1.updateAllPlansWithDuration);
// Update all existing plans with category limits (migration endpoint)
router.post('/update-category-limits', pricingPlanController_1.updateAllPlansWithCategoryLimits);
// Get all pricing plans (with optional active filter)
router.get('/', pricingPlanController_1.getAllPricingPlans);
// Get pricing plan by type
router.get('/type/:type', pricingPlanController_1.getPricingPlanByType);
// Get pricing plan by ID
router.get('/:id', pricingPlanController_1.getPricingPlanById);
// Create new pricing plan
router.post('/', pricingPlanValidation_1.validatePricingPlan, pricingPlanController_1.createPricingPlan);
// Update pricing plan
router.patch('/:id', pricingPlanValidation_1.validatePricingPlanUpdate, pricingPlanController_1.updatePricingPlan);
router.put('/:id', pricingPlanValidation_1.validatePricingPlanUpdate, pricingPlanController_1.updatePricingPlan);
// Delete pricing plan
router.delete('/:id', pricingPlanController_1.deletePricingPlan);
exports.default = router;
//# sourceMappingURL=pricingPlanRoutes.js.map