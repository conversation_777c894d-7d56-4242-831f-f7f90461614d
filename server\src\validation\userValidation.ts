import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

const registerSchema = Joi.object({
    username: Joi.string().required().min(3),
    email: Joi.string().required().email(),
    password: Joi.string()
        .required()
        .min(8)
        .pattern(/[^A-Za-z0-9]/)
        .messages({
            'string.min': 'Password must be at least 8 characters',
            'string.pattern.base': 'Password must contain at least one special character'
        }),
    firstName: Joi.string().optional(),
    lastName: Joi.string().optional()
});

const loginSchema = Joi.object({
    email: Joi.string().required().email(),
    password: Joi.string().required()
});

const profileUpdateSchema = Joi.object({
    firstName: Joi.string().optional(),
    lastName: Joi.string().optional(),
    username: Joi.string().optional(),
    phone: Joi.string().optional(),
    address: Joi.string().optional(),
    zipCode: Joi.string().optional(),
    country: Joi.string().optional()
});

export const registerValidation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        await registerSchema.validateAsync(req.body);
        next();
    } catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
};

export const loginValidation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        await loginSchema.validateAsync(req.body);
        next();
    } catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
};

export const profileUpdateValidation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        await profileUpdateSchema.validateAsync(req.body);
        next();
    } catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
};
