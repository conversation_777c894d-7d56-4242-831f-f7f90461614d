name: HRKY-<PERSON><PERSON>KEND-DEPLOYMENTS-BUILD-PUSH

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Which environment to deploy to?'
        required: true
        type: choice
        options:
          - prod
          - develop

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to EC2 via SSH
        env:
          EC2_IP: ${{ secrets.EC2_PUBLIC_IP }}
          PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
          GITHUB_USERNAME: ${{ secrets.MY_GITHUB_USERNAME }}
          GITHUB_TOKEN: ${{ secrets.MY_GITHUB_TOKEN }}
          BRANCH: ${{ github.event.inputs.environment }}
          BACKEND_URL: ${{ secrets.BACKEND_URL }}
          FRONTEND_URL: ${{ secrets.FRONTEND_URL }}
          MONGODB_URI: ${{ secrets.MONGODB_URI }}
          JWT_SECRET: ${{ secrets.JWT_SECRET }}
          GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID }}
          GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET }}
          STEP_GMAIL: ${{ secrets.STEP_GMAIL }}
          STEP_PASSWORD: ${{ secrets.STEP_PASSWORD }}
          PORT: ${{ secrets.PORT }}
          STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
          STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_WEBHOOK_SECRET }}
        run: |
          echo "$PRIVATE_KEY" | tr -d '\r' > private-key.pem
          chmod 400 private-key.pem

          ssh -o StrictHostKeyChecking=no -i private-key.pem ubuntu@$EC2_IP "
          set -e
            echo '✅ Connected to EC2'

            cd /home/<USER>/project/hierarchy/backend/heirkey-backend

            echo 'Pulling branch: $BRANCH'
            git fetch origin
            git checkout $BRANCH
            git reset --hard origin/$BRANCH
            git clean -fd

            echo '⚙️ Writing .env file...'
            cat <<EOF > .env
            BACKEND_URL=$BACKEND_URL
            FRONTEND_URL=$FRONTEND_URL
            MONGODB_URI=$MONGODB_URI
            JWT_SECRET=$JWT_SECRET
            GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID
            GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET
            STEP_GMAIL=$STEP_GMAIL
            STEP_PASSWORD=$STEP_PASSWORD
            PORT=$PORT
            STRIPE_SECRET_KEY=$STRIPE_SECRET_KEY
            STRIPE_WEBHOOK_SECRET=$STRIPE_WEBHOOK_SECRET
          EOF

            echo 'Installing dependencies...'
            npm install

            echo 'Building...'
            npm run build

            echo 'Restarting backend with PM2...'
            pm2 restart heirkey-backend --update-env || pm2 start dist/index.js --name heirkey-backend
          "

          rm private-key.pem

      - name: Notify Slack on Success
        if: success()
        run: |
          curl -X POST -H 'Content-type: application/json' \
          --data '{"text":"✅ *HeirKey Backend Deploy Succeeded!* Branch: ${{ github.event.inputs.environment }} Commit: ${{ github.sha }}"}' \
          ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on Failure
        if: failure()
        run: |
          curl -X POST -H 'Content-type: application/json' \
          --data '{"text":"❌ *HeirKey Backend Deploy Failed!* Branch: ${{ github.event.inputs.environment }} Commit: ${{ github.sha }}"}' \
          ${{ secrets.SLACK_WEBHOOK_URL }}
