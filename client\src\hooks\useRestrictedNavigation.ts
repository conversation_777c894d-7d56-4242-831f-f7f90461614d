import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useAccessControl } from '@/hooks/useAccessControl';
import { getCategoryIdFromPath } from '@/utils/footerUtils';
import { useState } from 'react';

export const useRestrictedNavigation = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const { canAccessCategory, userAccess } = useAccessControl();
  const [upgradeDialogOpen, setUpgradeDialogOpen] = useState(false);
  const [restrictedLink, setRestrictedLink] = useState<string>('');

  const handleNavigation = (e: React.MouseEvent, href: string) => {
    if (!isAuthenticated) {
      e.preventDefault();
      navigate("/auth/login");
      return;
    }

    const categoryId = getCategoryIdFromPath(href);
    if (categoryId && !canAccessCategory(categoryId)) {
      e.preventDefault();
      setRestrictedLink(href);
      setUpgradeDialogOpen(true);
    }
  };

  const handleDisabledClick = (e: React.MouseEvent, href: string) => {
    e.preventDefault();
    if (isAuthenticated) {
      setRestrictedLink(href);
      setUpgradeDialogOpen(true);
    } else {
      navigate("/auth/login");
    }
  };

  const handleUpgradeClick = () => {
    setUpgradeDialogOpen(false);
    navigate("/subscription");
  };

  const closeUpgradeDialog = () => {
    setUpgradeDialogOpen(false);
  };

  return {
    handleNavigation,
    handleDisabledClick,
    handleUpgradeClick,
    closeUpgradeDialog,
    upgradeDialogOpen,
    setUpgradeDialogOpen,
    restrictedLink,
    userAccess
  };
}; 