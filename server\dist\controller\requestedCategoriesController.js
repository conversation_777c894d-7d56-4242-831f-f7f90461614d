"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.approveRequestFromDashboard = exports.approveRequest = exports.getOwnerRequests = exports.getMyRequests = exports.requestCategories = void 0;
const crypto_1 = __importDefault(require("crypto"));
const User_1 = __importDefault(require("../models/User"));
const Owner_1 = __importDefault(require("../models/Owner"));
const RequestedCategories_1 = __importDefault(require("../models/RequestedCategories"));
const RequestedCategories_2 = require("../types/RequestedCategories");
const email_1 = __importDefault(require("../utils/email"));
const requestCategories = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    try {
        const { categoryIds, message } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            res.status(401).json({ message: 'Unauthorized' });
            return;
        }
        // Find the user making the request
        const user = yield User_1.default.findById(userId).populate('roleId');
        if (!user || !user.ownerId) {
            res.status(404).json({ message: 'User not found or not associated with an owner' });
            return;
        }
        // Verify user is Nominee or Family (not Owner)
        const userRole = (_b = user.roleId) === null || _b === void 0 ? void 0 : _b.name;
        if (userRole === 'Owner') {
            res.status(403).json({ message: 'Owners cannot request category access' });
            return;
        }
        // Find the owner
        const owner = yield Owner_1.default.findById(user.ownerId);
        if (!owner) {
            res.status(404).json({ message: 'Owner not found' });
            return;
        }
        // No validation needed - just store the category IDs as provided by frontend
        // Check if there's already a pending request for any of these categories
        const existingRequest = yield RequestedCategories_1.default.findOne({
            ownerId: owner._id,
            requestedUserId: user._id,
            status: RequestedCategories_2.RequestStatus.PENDING,
            categoryIds: { $in: categoryIds }
        });
        if (existingRequest) {
            res.status(400).json({ message: 'You already have a pending request for one or more of these categories' });
            return;
        }
        // Generate approval token
        const approvalToken = crypto_1.default.randomBytes(32).toString('hex');
        const hashedToken = crypto_1.default.createHash('sha256').update(approvalToken).digest('hex');
        // Create category request
        const categoryRequest = new RequestedCategories_1.default({
            ownerId: owner._id,
            requestedUserId: user._id,
            categoryIds: categoryIds,
            status: RequestedCategories_2.RequestStatus.PENDING,
            requestMessage: message,
            approvalToken: hashedToken,
            approvalTokenExpire: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
        });
        yield categoryRequest.save();
        // Send email to owner for approval
        const categoryNames = categoryIds.join(', ');
        const approvalUrl = `${process.env.FRONTEND_URL}/approve-category-request/${approvalToken}`;
        const emailMessage = `
            <h2>Category Access Request</h2>
            <p>Hello ${owner.getFullName()},</p>
            <p><strong>${user.firstName} ${user.lastName}</strong> (${userRole}) has requested access to the following categories:</p>
            <ul>
                ${categoryIds.map(id => `<li><strong>Category ${id}</strong></li>`).join('')}
            </ul>
            ${message ? `<p><strong>Message:</strong> ${message}</p>` : ''}
            <p>Please review and approve or reject this request:</p>
            <div style="margin: 20px 0;">
                <a href="${approvalUrl}?action=approve" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; display: inline-block; border-radius: 4px; margin-right: 10px;">Approve Request</a>
                <a href="${approvalUrl}?action=reject" style="background-color: #f44336; color: white; padding: 12px 24px; text-decoration: none; display: inline-block; border-radius: 4px;">Reject Request</a>
            </div>
            <p>This request will expire in 7 days.</p>
        `;
        yield (0, email_1.default)({
            to: owner.email,
            subject: 'Category Access Request - Heirkey',
            html: emailMessage
        });
        res.status(201).json({
            message: 'Category access request sent successfully',
            request: {
                id: categoryRequest._id,
                categories: categoryNames,
                status: categoryRequest.status,
                createdAt: categoryRequest.createdAt
            },
            debugToken: approvalToken // Raw token for testing (remove in production)
        });
    }
    catch (error) {
        console.error('Request categories error:', error);
        res.status(500).json({
            message: 'Error requesting category access',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.requestCategories = requestCategories;
const getMyRequests = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            res.status(401).json({ message: 'Unauthorized' });
            return;
        }
        // Get all requests made by this user
        const requests = yield RequestedCategories_1.default.findByUserId(userId);
        res.status(200).json({
            message: 'Requests retrieved successfully',
            requests: requests.map(req => ({
                id: req._id,
                categoryIds: req.categoryIds,
                status: req.status,
                requestMessage: req.requestMessage,
                createdAt: req.createdAt,
                updatedAt: req.updatedAt
            }))
        });
    }
    catch (error) {
        console.error('Get my requests error:', error);
        res.status(500).json({
            message: 'Error retrieving requests',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getMyRequests = getMyRequests;
const getOwnerRequests = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            res.status(401).json({ message: 'Unauthorized' });
            return;
        }
        // Find the owner
        const owner = yield Owner_1.default.findOne({ userId });
        if (!owner) {
            res.status(404).json({ message: 'Owner not found' });
            return;
        }
        // Get all requests for this owner
        const requests = yield RequestedCategories_1.default.findByOwnerId(owner._id);
        res.status(200).json({
            message: 'Owner requests retrieved successfully',
            requests: requests.map(req => {
                var _a, _b, _c;
                return ({
                    id: req._id,
                    requester: {
                        name: `${((_a = req.requestedUser) === null || _a === void 0 ? void 0 : _a.firstName) || ''} ${((_b = req.requestedUser) === null || _b === void 0 ? void 0 : _b.lastName) || ''}`.trim(),
                        email: (_c = req.requestedUser) === null || _c === void 0 ? void 0 : _c.email
                    },
                    categoryIds: req.categoryIds,
                    status: req.status,
                    requestMessage: req.requestMessage,
                    createdAt: req.createdAt,
                    updatedAt: req.updatedAt
                });
            })
        });
    }
    catch (error) {
        console.error('Get owner requests error:', error);
        res.status(500).json({
            message: 'Error retrieving owner requests',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getOwnerRequests = getOwnerRequests;
const approveRequest = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { token, action } = req.body;
        // Hash the token to match stored version
        const hashedToken = crypto_1.default.createHash('sha256').update(token).digest('hex');
        // Find request by token
        const categoryRequest = yield RequestedCategories_1.default.findOne({
            approvalToken: hashedToken,
            approvalTokenExpire: { $gt: new Date() },
            status: RequestedCategories_2.RequestStatus.PENDING
        }).populate('owner').populate('requestedUser');
        if (!categoryRequest) {
            res.status(400).json({ message: 'Invalid or expired approval token' });
            return;
        }
        const owner = categoryRequest.owner;
        const requester = categoryRequest.requestedUser;
        const categoryIds = categoryRequest.categoryIds;
        // Update request status
        const newStatus = action === 'approve' ? RequestedCategories_2.RequestStatus.APPROVED : RequestedCategories_2.RequestStatus.REJECTED;
        categoryRequest.status = newStatus;
        categoryRequest.approvalToken = undefined;
        categoryRequest.approvalTokenExpire = undefined;
        yield categoryRequest.save();
        // Send confirmation email to requester
        const statusMessage = action === 'approve' ? 'approved' : 'rejected';
        const statusColor = action === 'approve' ? '#4CAF50' : '#f44336';
        const confirmationEmail = `
            <h2>Category Access Request ${statusMessage.charAt(0).toUpperCase() + statusMessage.slice(1)}</h2>
            <p>Hello ${requester.firstName} ${requester.lastName},</p>
            <p>Your request for access to the following categories has been <strong style="color: ${statusColor};">${statusMessage}</strong> by ${owner.getFullName()}:</p>
            <ul>
                ${categoryIds.map(id => `<li><strong>Category ${id}</strong></li>`).join('')}
            </ul>
            ${action === 'approve' ?
            '<p>You can now access these categories in your Heirkey dashboard.</p>' :
            '<p>If you have questions about this decision, please contact the owner directly.</p>'}
            <p>Thank you for using Heirkey.</p>
        `;
        yield (0, email_1.default)({
            to: requester.email,
            subject: `Category Access Request ${statusMessage.charAt(0).toUpperCase() + statusMessage.slice(1)} - Heirkey`,
            html: confirmationEmail
        });
        res.status(200).json({
            message: `Request ${statusMessage} successfully`,
            request: {
                id: categoryRequest._id,
                categoryIds: categoryIds,
                status: newStatus,
                requester: `${requester.firstName} ${requester.lastName}`
            }
        });
    }
    catch (error) {
        console.error('Approve request error:', error);
        res.status(500).json({
            message: 'Error processing approval',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.approveRequest = approveRequest;
const approveRequestFromDashboard = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const { requestId, action } = req.body;
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            res.status(401).json({ message: 'Unauthorized' });
            return;
        }
        // Find the owner
        const owner = yield Owner_1.default.findOne({ userId });
        if (!owner) {
            res.status(404).json({ message: 'Owner not found' });
            return;
        }
        // Find the request and verify it belongs to this owner
        const categoryRequest = yield RequestedCategories_1.default.findOne({
            _id: requestId,
            ownerId: owner._id,
            status: RequestedCategories_2.RequestStatus.PENDING
        }).populate('owner').populate('requestedUser');
        if (!categoryRequest) {
            res.status(404).json({ message: 'Request not found or not pending' });
            return;
        }
        const requester = categoryRequest.requestedUser;
        const categoryIds = categoryRequest.categoryIds;
        // Update request status
        const newStatus = action === 'approve' ? RequestedCategories_2.RequestStatus.APPROVED : RequestedCategories_2.RequestStatus.REJECTED;
        categoryRequest.status = newStatus;
        categoryRequest.approvalToken = undefined;
        categoryRequest.approvalTokenExpire = undefined;
        yield categoryRequest.save();
        // Send confirmation email to requester
        const statusMessage = action === 'approve' ? 'approved' : 'rejected';
        const statusColor = action === 'approve' ? '#4CAF50' : '#f44336';
        const confirmationEmail = `
            <h2>Category Access Request ${statusMessage.charAt(0).toUpperCase() + statusMessage.slice(1)}</h2>
            <p>Hello ${requester.firstName} ${requester.lastName},</p>
            <p>Your request for access to the following categories has been <strong style="color: ${statusColor};">${statusMessage}</strong> by ${owner.getFullName()}:</p>
            <ul>
                ${categoryIds.map(id => `<li><strong>Category ${id}</strong></li>`).join('')}
            </ul>
            ${action === 'approve' ?
            '<p>You can now access these categories in your Heirkey dashboard.</p>' :
            '<p>If you have questions about this decision, please contact the owner directly.</p>'}
            <p>Thank you for using Heirkey.</p>
        `;
        yield (0, email_1.default)({
            to: requester.email,
            subject: `Category Access Request ${statusMessage.charAt(0).toUpperCase() + statusMessage.slice(1)} - Heirkey`,
            html: confirmationEmail
        });
        res.status(200).json({
            message: `Request ${statusMessage} successfully`,
            request: {
                id: categoryRequest._id,
                categoryIds: categoryIds,
                status: newStatus,
                requester: `${requester.firstName} ${requester.lastName}`
            }
        });
    }
    catch (error) {
        console.error('Dashboard approve request error:', error);
        res.status(500).json({
            message: 'Error processing approval',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.approveRequestFromDashboard = approveRequestFromDashboard;
//# sourceMappingURL=requestedCategoriesController.js.map