import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { convertUserInputToFormValues, generateObjectId, normalizeContactData } from '@/services/userInputService';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectQuestionsBySectionId,
  selectUserInputsBySubcategoryId,
  updateUserInput,
} from '@/store/slices/financialPlansSlice';
import {
  QuestionItem,
  buildValidationSchema,
  generateInitialValues
} from '@/web/components/Category/FinancialPlans/FormFields';
import ScrollToQuestion from '@/web/components/Category/FinancialPlans/ScrollToQuestion';
import GoodToKnowBox from '@/web/components/Global/GoodToKnowBox';
import SubCategoryFooterNav from '@/web/components/Global/SubCategoryFooterNav';
import SubCategoryHeader from '@/web/components/Global/SubCategoryHeader';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import SubCategoryTitle from '@/web/components/Global/SubCategoryTitle';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { Form, Formik, FormikHelpers } from 'formik';
import { useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import * as Yup from 'yup';
import { createUserInfo } from '@/utils/avatarUtils';

const MONEY_MARKET_SECTION_ID = '501E';

const MoneyMarket = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();

  // Get data from Redux store using memoized selectors
  const storeQuestions = useAppSelector(selectQuestionsBySectionId(MONEY_MARKET_SECTION_ID));
  const loading = useAppSelector(selectLoading);
  const userInputs = useAppSelector(selectUserInputsBySubcategoryId('501E'));

  // Find the userInput for this subcategory
  const userInput = useMemo(
    () => userInputs.find((input: UserInput) => input.originalSubCategoryId === '501E'),
    [userInputs]
  );

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in MoneyMarket component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in MoneyMarket component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  // Scroll to the target question if specified in URL
  useEffect(() => {
    if (!loading && targetQuestionId) {
      setTimeout(() => {
        const element = document.getElementById(`question-${targetQuestionId}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.classList.add('bg-yellow-100');
          setTimeout(() => {
            element.classList.remove('bg-yellow-100');
          }, 2000);
        }
      }, 500);
    }
  }, [loading, targetQuestionId]);

  // Prepare form configuration
  const validationSchema = buildValidationSchema(storeQuestions as any, Yup);
  const baseInitialValues = generateInitialValues(storeQuestions as any);

  // Cross-platform data reading logic (following ImportantContacts pattern)
  const initialValues = useMemo(() => {
    if (userInput) {
      const processedValues = { ...baseInitialValues };

      // Process each question to handle cross-platform data
      storeQuestions.forEach((question: any) => {
        if (question.type === 'contacts') {
          // Handle contacts with cross-platform logic
          let contacts: any[] = [];
          const section = userInput.answersBySection.find((s: any) => s.originalSectionId === MONEY_MARKET_SECTION_ID);
          if (section) {
            const contactAnswers = section.answers.filter((a: any) => a.originalQuestionId === question.id);
            if (contactAnswers.length === 1) {
              // Web style: one answer, which is an array
              try {
                const parsed = JSON.parse(contactAnswers[0].answer);
                if (Array.isArray(parsed)) {
                  contacts = parsed.map(normalizeContactData);
                } else if (parsed && typeof parsed === 'object') {
                  contacts = [normalizeContactData(parsed)];
                }
              } catch {}
            } else if (contactAnswers.length > 1) {
              // Mobile style: multiple answers, each a contact
              contacts = contactAnswers.map((a: any) => {
                try {
                  return normalizeContactData(JSON.parse(a.answer));
                } catch {
                  return null;
                }
              }).filter(Boolean);
            }
          }
          processedValues[question.id] = contacts.length > 0 ? contacts : [{ name: "", phone: "" }];
        } else {
          // Handle regular fields
          const formValues = convertUserInputToFormValues(userInput);
          if (formValues[question.id] !== undefined) {
            processedValues[question.id] = formValues[question.id];
          }
        }
      });

      return processedValues;
    }
    return baseInitialValues;
  }, [userInput, storeQuestions, baseInitialValues]);

  const existingInputId = userInput?._id || null;

  const handleSubmit = async (values: Record<string, any>, { setSubmitting }: FormikHelpers<Record<string, any>>) => {
    try {
      if (!user || !user.id) {
        throw new Error('You must be logged in to save answers');
      }
      const answersBySection = storeQuestions
        .reduce((sections: Record<string, Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: any;
        }>>, question) => {
          if (!sections[question.sectionId]) {
            sections[question.sectionId] = [];
          }
          let answer = values[question.id];

          // Special handling for contact arrays
          if (question.type === 'contacts' && Array.isArray(answer)) {
            // Filter out empty contacts and stringify for storage
            answer = answer.filter((c: any) => c && (c.name?.trim() || c.phone?.trim()));
            answer = JSON.stringify(answer);
          }

          if (answer !== undefined && answer !== '' && !(Array.isArray(answer) && answer.length === 0)) {
            sections[question.sectionId].push({
              index: sections[question.sectionId].length,
              originalQuestionId: question.id,
              question: question.text,
              type: question.type === 'contacts' ? 'text' : question.type, // Map contacts to text for backend
              answer
            });
          }
          return sections;
        }, {});
      const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
        originalSectionId: sectionId,
        isCompleted: true,
        answers: answers as Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: any;
        }>
      }));
      const hasAnswers = formattedAnswersBySection.some(section => section.answers.length > 0);
      if (!hasAnswers) {
        setSubmitting(false);
        return;
      }
      if (existingInputId) {
        await dispatch(updateUserInput({
          id: existingInputId,
          userData: {
            userId: user.id,
            categoryId: generateObjectId(),
            originalCategoryId: '9',
            subCategoryId: generateObjectId(),
            originalSubCategoryId: '501E',
            answersBySection: formattedAnswersBySection
          } as UserInput
        })).unwrap();
      } else {
        const userData: Omit<UserInput, '_id'> = {
          userId: user.id,
          categoryId: generateObjectId(),
          originalCategoryId: '9',
          subCategoryId: generateObjectId(),
          originalSubCategoryId: '501E',
          answersBySection: formattedAnswersBySection
        };
        const result = await dispatch(saveUserInput(userData)).unwrap();
      }
      setSubmitting(false);
      navigate('/category/financialplans/review');
    } catch (error: any) {
      setSubmitting(false);
    }
  };

  if (storeQuestions.length === 0 || loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      <SubCategoryHeader
        title="Financial Plans"
        backTo="/dashboard"
        user={{
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
          email: user?.email || '<EMAIL>',
          avatar: createUserInfo(user).avatar
        }}
      />

      <SubCategoryTabs tabs={categoryTabsConfig.financialplans} />
      <div className="container mx-auto px-6">
        <SubCategoryTitle
          mainCategory="Financial Plans"
          category="Money Market"
          description="These files contain questions to help you record your details so they're easy to find later."
        />
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
              >
                {({ isSubmitting, isValid, dirty }) => (
                  <Form>
                    <ScrollToQuestion questions={storeQuestions as any}>
                      {(refs) => (
                        <>
                          {[...storeQuestions]
                            .sort((a: any, b: any) => a.order - b.order)
                            .map((question: any) => (
                              <div
                                key={question.id}
                                id={`question-${question.id}`}
                              >
                                <QuestionItem
                                  question={question}
                                  questionRef={(el) => {
                                    if (el) refs[question.id] = el;
                                  }}
                                />
                              </div>
                            ))}
                        </>
                      )}
                    </ScrollToQuestion>
                    <div className="mt-8 flex justify-end">
                      <Button
                        type="submit"
                        disabled={isSubmitting || !isValid || !dirty}
                        className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                      >
                        Save & Continue
                      </Button>
                    </div>
                    <GoodToKnowBox
                      title="Editing my Answers"
                      description="Each topic below is a part of your financial plans, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you."
                    />
                    <SubCategoryFooterNav
                      leftLabel="Bonds"
                      leftTo="/category/financialplans/bonds"
                      rightLabel="Review"
                      rightTo="/category/financialplans/review"
                    />
                  </Form>
                )}
              </Formik>
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default MoneyMarket;
