import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

const inviteUserSchema = Joi.object({
    name: Joi.string().required().min(2).max(100).trim(),
    email: Joi.string().required().email(),
    relation: Joi.string().required().min(2).max(50).trim(),
    phone: Joi.string().optional().pattern(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/).messages({
        'string.pattern.base': 'Please provide a valid phone number'
    })
});

const acceptInvitationSchema = Joi.object({
    token: Joi.string().required(),
    password: Joi.string()
        .required()
        .min(8)
        .pattern(/[^A-Za-z0-9]/)
        .messages({
            'string.min': 'Password must be at least 8 characters',
            'string.pattern.base': 'Password must contain at least one special character'
        })
});

const updateInvitationStatusSchema = Joi.object({
    status: Joi.string().valid('pending', 'accepted', 'rejected').required()
});

export const inviteUserValidation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        await inviteUserSchema.validateAsync(req.body);
        next();
    } catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
};

export const acceptInvitationValidation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        await acceptInvitationSchema.validateAsync(req.body);
        next();
    } catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
};

export const updateInvitationStatusValidation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        await updateInvitationStatusSchema.validateAsync(req.body);
        next();
    } catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
};
