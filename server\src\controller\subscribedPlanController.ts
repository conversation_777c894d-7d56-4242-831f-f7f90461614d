import { Request, Response } from 'express';
import SubscribedPlan from '../models/SubscribedPlan';
import PricingPlan from '../models/PricingPlan';
import Owner from '../models/Owner';
import User from '../models/User';
import mongoose from 'mongoose';

// Create a new subscription
export const createSubscription = async (req: Request, res: Response): Promise<void> => {
  try {
    const { planId, ownerId, currentPlan } = req.body;

    // Use currentPlan as the primary field, fallback to planId
    const selectedPlanId = currentPlan || planId;

    // Validate that the plan exists
    const plan = await PricingPlan.findById(selectedPlanId);
    if (!plan) {
      res.status(404).json({
        status: 'fail',
        message: 'Pricing plan not found'
      });
      return;
    }

    // Validate that the owner exists
    const owner = await Owner.findById(ownerId);
    if (!owner) {
      res.status(404).json({
        status: 'fail',
        message: 'Owner not found'
      });
      return;
    }

    // Check if owner already has an active subscription
    const existingSubscription = await SubscribedPlan.findOne({ ownerId });
    if (existingSubscription) {
      res.status(400).json({
        status: 'fail',
        message: 'Owner already has an active subscription. Use change plan endpoint to modify.'
      });
      return;
    }

    const subscription = new SubscribedPlan({
      planId: selectedPlanId,
      ownerId,
      currentPlan: selectedPlanId,
      previousPlans: []
    });

    await subscription.save();

    // Update the owner with the subscription ID
    await Owner.findByIdAndUpdate(ownerId, { subscribedPlanId: subscription._id });

    // Update all users associated with this owner to have the new subscription type
    await User.updateMany(
      { ownerId: ownerId },
      { 
        subscriptionType: plan.type,
        allowedCategoryId: null // Reset allowed category for new subscription type
      }
    );

    // Populate the response with plan and owner details
    const populatedSubscription = await SubscribedPlan.findById(subscription._id)
      .populate('currentPlan', 'type price displayPrice tagline duration')
      .populate('ownerId', 'firstName lastName email')
      .populate('previousPlans', 'type price displayPrice');

    res.status(201).json({
      status: 'success',
      data: {
        subscription: populatedSubscription
      }
    });
  } catch (error) {
    console.error('Error creating subscription:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error creating subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all subscriptions
export const getAllSubscriptions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { active, ownerId } = req.query;
    
    // Build filter object
    const filter: any = {};
    if (ownerId) {
      if (!mongoose.Types.ObjectId.isValid(ownerId as string)) {
        res.status(400).json({
          status: 'fail',
          message: 'Invalid owner ID'
        });
        return;
      }
      filter.ownerId = ownerId;
    }

    let subscriptions = await SubscribedPlan.find(filter)
      .populate('currentPlan', 'type price displayPrice tagline duration')
      .populate('ownerId', 'firstName lastName email')
      .populate('previousPlans', 'type price displayPrice')
      .sort({ createdAt: -1 });

    // Filter by active status if specified
    if (active !== undefined) {
      const isActiveFilter = active === 'true';
      subscriptions = subscriptions.filter(sub => sub.isActive === isActiveFilter);
    }

    res.status(200).json({
      status: 'success',
      results: subscriptions.length,
      data: {
        subscriptions
      }
    });
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error fetching subscriptions',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get subscription by ID
export const getSubscriptionById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({
        status: 'fail',
        message: 'Invalid subscription ID'
      });
      return;
    }

    const subscription = await SubscribedPlan.findById(id)
      .populate('currentPlan', 'type price displayPrice tagline duration features')
      .populate('ownerId', 'firstName lastName email')
      .populate('previousPlans', 'type price displayPrice tagline');

    if (!subscription) {
      res.status(404).json({
        status: 'fail',
        message: 'Subscription not found'
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        subscription
      }
    });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error fetching subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get owner's current subscription
export const getOwnerSubscription = async (req: Request, res: Response): Promise<void> => {
  try {
    const { ownerId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(ownerId)) {
      res.status(400).json({
        status: 'fail',
        message: 'Invalid owner ID'
      });
      return;
    }

    const subscription = await SubscribedPlan.findOne({ ownerId })
      .populate('currentPlan', 'type price displayPrice tagline duration features')
      .populate('ownerId', 'firstName lastName email')
      .populate('previousPlans', 'type price displayPrice tagline');

    if (!subscription) {
      res.status(404).json({
        status: 'fail',
        message: 'No subscription found for this owner'
      });
      return;
    }

    res.status(200).json({
      status: 'success',
      data: {
        subscription,
        isActive: subscription.isActive,
        daysRemaining: subscription.daysRemaining
      }
    });
  } catch (error) {
    console.error('Error fetching owner subscription:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error fetching owner subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Change/upgrade plan
export const changePlan = async (req: Request, res: Response): Promise<void> => {
  try {
    const { ownerId } = req.params;
    const { newPlanId } = req.body;

    if (!mongoose.Types.ObjectId.isValid(ownerId)) {
      res.status(400).json({
        status: 'fail',
        message: 'Invalid owner ID'
      });
      return;
    }

    // Validate that the new plan exists
    const newPlan = await PricingPlan.findById(newPlanId);
    if (!newPlan) {
      res.status(404).json({
        status: 'fail',
        message: 'New pricing plan not found'
      });
      return;
    }

    // Find existing subscription
    const subscription = await SubscribedPlan.findOne({ ownerId });
    if (!subscription) {
      res.status(404).json({
        status: 'fail',
        message: 'No active subscription found for this owner'
      });
      return;
    }

    // Add current plan to previous plans array
    subscription.previousPlans.push(subscription.currentPlan);

    // Update current plan
    subscription.currentPlan = newPlanId;
    subscription.planId = newPlanId;

    await subscription.save(); // This will trigger the pre-save hook to recalculate expiryAt

    // Update all users associated with this owner to have the new subscription type
    await User.updateMany(
      { ownerId: ownerId },
      { 
        subscriptionType: newPlan.type,
        allowedCategoryId: null // Reset allowed category for new subscription type
      }
    );

    // Populate the response
    const updatedSubscription = await SubscribedPlan.findById(subscription._id)
      .populate('currentPlan', 'type price displayPrice tagline duration features')
      .populate('ownerId', 'firstName lastName email')
      .populate('previousPlans', 'type price displayPrice tagline');

    res.status(200).json({
      status: 'success',
      message: 'Plan changed successfully',
      data: {
        subscription: updatedSubscription
      }
    });
  } catch (error) {
    console.error('Error changing plan:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error changing plan',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Cancel subscription (soft delete)
export const cancelSubscription = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({
        status: 'fail',
        message: 'Invalid subscription ID'
      });
      return;
    }

    const subscription = await SubscribedPlan.findByIdAndDelete(id);
    if (!subscription) {
      res.status(404).json({
        status: 'fail',
        message: 'Subscription not found'
      });
      return;
    }

    // Remove the subscription reference from the owner
    await Owner.findByIdAndUpdate(subscription.ownerId, { subscribedPlanId: null });

    // Reset all users associated with this owner back to temporary_key
    await User.updateMany(
      { ownerId: subscription.ownerId },
      { 
        subscriptionType: 'temporary_key',
        allowedCategoryId: null // Reset allowed category for temporary_key
      }
    );

    res.status(200).json({
      status: 'success',
      message: 'Subscription cancelled successfully'
    });
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error cancelling subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get expired subscriptions
export const getExpiredSubscriptions = async (_req: Request, res: Response): Promise<void> => {
  try {
    const now = new Date();

    const expiredSubscriptions = await SubscribedPlan.find({
      expiryAt: { $lt: now }
    })
      .populate('currentPlan', 'type price displayPrice tagline duration')
      .populate('ownerId', 'firstName lastName email')
      .sort({ expiryAt: -1 });

    res.status(200).json({
      status: 'success',
      results: expiredSubscriptions.length,
      data: {
        expiredSubscriptions
      }
    });
  } catch (error) {
    console.error('Error fetching expired subscriptions:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error fetching expired subscriptions',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Renew subscription (extend current plan)
export const renewSubscription = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({
        status: 'fail',
        message: 'Invalid subscription ID'
      });
      return;
    }

    const subscription = await SubscribedPlan.findById(id);
    if (!subscription) {
      res.status(404).json({
        status: 'fail',
        message: 'Subscription not found'
      });
      return;
    }

    // Trigger recalculation of expiry date by modifying updatedAt
    subscription.markModified('updatedAt');
    await subscription.save(); // This will recalculate expiryAt

    const renewedSubscription = await SubscribedPlan.findById(subscription._id)
      .populate('currentPlan', 'type price displayPrice tagline duration features')
      .populate('ownerId', 'firstName lastName email')
      .populate('previousPlans', 'type price displayPrice tagline');

    res.status(200).json({
      status: 'success',
      message: 'Subscription renewed successfully',
      data: {
        subscription: renewedSubscription
      }
    });
  } catch (error) {
    console.error('Error renewing subscription:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error renewing subscription',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Sync user subscription types with their actual subscriptions
export const syncUserSubscriptionTypes = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get all active subscriptions
    const subscriptions = await SubscribedPlan.find()
      .populate('currentPlan', 'type')
      .populate('ownerId', '_id');

    let updatedCount = 0;
    let errors: string[] = [];

    for (const subscription of subscriptions) {
      try {
        const plan = subscription.currentPlan as any;
        const owner = subscription.ownerId as any;
        
        if (plan && owner) {
          // Update all users associated with this owner
          const result = await User.updateMany(
            { ownerId: owner._id },
            { 
              subscriptionType: plan.type,
              allowedCategoryId: null
            }
          );
          
          updatedCount += result.modifiedCount;
          console.log(`Updated ${result.modifiedCount} users for owner ${owner._id} to subscription type: ${plan.type}`);
        }
      } catch (error) {
        const errorMsg = `Error updating users for subscription ${subscription._id}: ${error}`;
        console.error(errorMsg);
        errors.push(errorMsg);
      }
    }

    // Also reset users who don't have active subscriptions back to temporary_key
    const ownersWithSubscriptions = subscriptions.map(sub => (sub.ownerId as any)._id.toString());
    const resetResult = await User.updateMany(
      { 
        ownerId: { 
          $exists: true, 
          $ne: null,
          $nin: ownersWithSubscriptions 
        }
      },
      { 
        subscriptionType: 'temporary_key',
        allowedCategoryId: null
      }
    );

    res.status(200).json({
      status: 'success',
      message: 'User subscription types synced successfully',
      data: {
        usersUpdated: updatedCount,
        usersResetToTemporary: resetResult.modifiedCount,
        totalSubscriptionsProcessed: subscriptions.length,
        errors: errors.length > 0 ? errors : undefined
      }
    });
  } catch (error) {
    console.error('Error syncing user subscription types:', error);
    res.status(500).json({
      status: 'error',
      message: 'Error syncing user subscription types',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};