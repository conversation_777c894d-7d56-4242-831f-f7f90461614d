"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testCorsEndpoint = exports.corsDebugMiddleware = void 0;
// Debug middleware to log CORS requests
const corsDebugMiddleware = (req, res, next) => {
    console.log('=== CORS Debug Info ===');
    console.log('Request URL:', req.url);
    console.log('Request Method:', req.method);
    console.log('Request Origin:', req.headers.origin);
    console.log('Request Headers:', req.headers);
    console.log('=======================');
    // Add CORS headers for debugging
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, stripe-signature');
    res.header('Access-Control-Allow-Credentials', 'true');
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        console.log('Handling OPTIONS preflight request');
        res.status(200).end();
        return;
    }
    next();
};
exports.corsDebugMiddleware = corsDebugMiddleware;
// Test endpoint to verify CORS is working
const testCorsEndpoint = (req, res) => {
    console.log('CORS test endpoint hit');
    res.json({
        message: 'CORS is working!',
        timestamp: new Date().toISOString(),
        origin: req.headers.origin,
        method: req.method
    });
};
exports.testCorsEndpoint = testCorsEndpoint;
//# sourceMappingURL=corsDebug.js.map