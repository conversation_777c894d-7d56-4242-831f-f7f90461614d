import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { RoleType } from '../types/Role';

const createRoleSchema = Joi.object({
  name: Joi.string()
    .valid(...Object.values(RoleType))
    .required()
    .messages({
      'any.only': 'Role name must be one of: Owner, Nominee, Family',
      'any.required': 'Role name is required'
    }),
  description: Joi.string()
    .required()
    .min(10)
    .max(500)
    .messages({
      'string.min': 'Description must be at least 10 characters',
      'string.max': 'Description cannot exceed 500 characters',
      'any.required': 'Description is required'
    }),
  permissions: Joi.array()
    .items(Joi.string().valid(
      'canViewAll',
      'canEditAll',
      'canDeleteAll',
      'canCreateAll'
    ))
    .min(1)
    .required()
    .messages({
      'array.min': 'At least one permission is required',
      'any.required': 'Permissions are required'
    }),
  isActive: Joi.boolean().optional()
});

const updateRoleSchema = Joi.object({
  description: Joi.string()
    .optional()
    .min(10)
    .max(500)
    .messages({
      'string.min': 'Description must be at least 10 characters',
      'string.max': 'Description cannot exceed 500 characters'
    }),
  permissions: Joi.array()
    .items(Joi.string().valid(
      'canViewAll',
      'canEditAll',
      'canDeleteAll',
      'canCreateAll'
    ))
    .min(1)
    .optional()
    .messages({
      'array.min': 'At least one permission is required'
    }),
  isActive: Joi.boolean().optional()
});

export const validateCreateRole = (req: Request, res: Response, next: NextFunction): void => {
  const { error } = createRoleSchema.validate(req.body);
  if (error) {
    res.status(400).json({
      status: 'fail',
      message: 'Validation error',
      errors: error.details.map(detail => detail.message)
    });
    return;
  }
  next();
};

export const validateUpdateRole = (req: Request, res: Response, next: NextFunction): void => {
  const { error } = updateRoleSchema.validate(req.body);
  if (error) {
    res.status(400).json({
      status: 'fail',
      message: 'Validation error',
      errors: error.details.map(detail => detail.message)
    });
    return;
  }
  next();
};
