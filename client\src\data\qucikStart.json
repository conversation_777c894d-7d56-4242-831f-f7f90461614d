{"quickstartToCategories": [{"quickstartQuestion": "What is your home address?", "sharedKeys": ["home_address_street", "home_address_city", "home_address_state", "home_address_zip"], "target": {"file": "homeInstructions.json", "fields": [{"id": "l2", "sharedKey": "home_address_street"}, {"id": "l3", "sharedKey": "home_address_city"}, {"id": "l4", "sharedKey": "home_address_state"}, {"id": "l5", "sharedKey": "home_address_zip"}]}}, {"quickstartQuestion": "Do you have a will?", "sharedKey": "has_will", "target": {"file": "willInstructions.json", "fieldId": "w1"}}, {"quickstartQuestion": "Is your will located in your house?", "sharedKey": "will_in_home", "target": {"file": "willInstructions.json", "fieldId": "w2"}}, {"quickstartQuestion": "Where is your will located?", "sharedKey": "will_location_detail", "target": {"file": "willInstructions.json", "fieldId": "w3"}}, {"quickstartQuestion": "Do you have a lawyer?", "sharedKey": "has_attorney", "target": {"file": "willInstructions.json", "fieldId": "w5"}}, {"quickstartQuestion": "Lawyer's Name", "sharedKey": "attorney_name", "target": {"file": "willInstructions.json", "fieldId": "w6"}}, {"quickstartQuestion": "Lawyer's Firm", "sharedKey": "attorney_firm", "target": {"file": "willInstructions.json", "fieldId": "w7"}}, {"quickstartQuestion": "Lawyer's Email", "sharedKey": "attorney_email", "target": {"file": "willInstructions.json", "fieldId": "w8"}}, {"quickstartQuestion": "Lawyer's Phone", "sharedKey": "attorney_phone", "target": {"file": "willInstructions.json", "fieldId": "w9"}}, {"quickstartQuestion": "Do you have a Funeral Home Preference?", "sharedKey": "funeral_home_preference", "target": {"file": "funeralArrangements.json", "fieldId": "f1"}}, {"quickstartQuestion": "Name of Funeral Home", "sharedKey": "funeral_home_name", "target": {"file": "funeralArrangements.json", "fieldId": "f2"}}, {"quickstartQuestion": "Essential Contact (death or injury)", "sharedKey": "essential_contact", "target": {"file": "importantContacts.json", "fieldId": "c1"}}, {"quickstartQuestion": "Do you rent or own your house?", "sharedKey": "housing_status", "target": {"file": "future_category", "fieldId": "housing_status", "category": "housing_ownership"}}, {"quickstartQuestion": "Do you have a mortgage?", "sharedKey": "has_mortgage", "target": {"file": "future_category", "fieldId": "has_mortgage", "category": "housing_ownership", "dependsOn": {"question": "housing_status", "value": "own"}}}, {"quickstartQuestion": "What company holds your mortgage?", "sharedKey": "mortgage_company", "target": {"file": "future_category", "fieldId": "mortgage_company", "category": "housing_ownership", "dependsOn": {"question": "has_mortgage", "value": "yes"}}}, {"quickstartQuestion": "What is your monthly mortgage payment?", "sharedKey": "monthly_mortgage_payment", "target": {"file": "future_category", "fieldId": "monthly_mortgage_payment", "category": "housing_ownership", "dependsOn": {"question": "has_mortgage", "value": "yes"}}}, {"quickstartQuestion": "How much is your rent?", "sharedKey": "monthly_rent", "target": {"file": "future_category", "fieldId": "monthly_rent", "category": "housing_ownership", "dependsOn": {"question": "housing_status", "value": "rent"}}}, {"quickstartQuestion": "Who is your landlord/management company?", "sharedKey": "landlord_company", "target": {"file": "future_category", "fieldId": "landlord_company", "category": "housing_ownership", "dependsOn": {"question": "housing_status", "value": "rent"}}}, {"quickstartQuestion": "What is your landlord/management company's contact information?", "sharedKey": "landlord_contact", "target": {"file": "future_category", "fieldId": "landlord_contact", "category": "housing_ownership", "dependsOn": {"question": "housing_status", "value": "rent"}}}]}