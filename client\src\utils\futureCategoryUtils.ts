import futureCategoryService from '@/services/futureCategoryService';

/**
 * Utility functions for managing future category data
 */
export const futureCategoryUtils = {
  /**
   * Display future category data in a readable format
   */
  displayFutureCategoryData: async (userId: string) => {
    try {
      const data = await futureCategoryService.getFutureCategoryData(userId);
      
      console.log('=== Future Category Data ===');
      
      if (Object.keys(data).length === 0) {
        console.log('No future category data found.');
        return;
      }

      Object.entries(data).forEach(([category, items]) => {
        console.log(`\n📁 ${category.toUpperCase()}:`);
        items.forEach(item => {
          console.log(`  • ${item.question || item.fieldId}: ${item.answer}`);
        });
      });
    } catch (error) {
      console.error('Error displaying future category data:', error);
    }
  },

  /**
   * Get housing ownership data specifically
   */
  getHousingData: async (userId: string) => {
    try {
      const housingData = await futureCategoryService.getCategoryData(userId, 'housing_ownership');
      
      if (housingData.length === 0) {
        return null;
      }

      // Organize data into a structured format
      const structuredData: any = {};
      
      housingData.forEach(item => {
        structuredData[item.fieldId] = {
          answer: item.answer,
          question: item.question,
          timestamp: item.timestamp
        };
      });

      return structuredData;
    } catch (error) {
      console.error('Error getting housing data:', error);
      return null;
    }
  },

  /**
   * Check if user has any future category data
   */
  hasFutureCategoryData: async (userId: string): Promise<boolean> => {
    try {
      const data = await futureCategoryService.getFutureCategoryData(userId);
      return Object.keys(data).length > 0;
    } catch (error) {
      console.error('Error checking future category data:', error);
      return false;
    }
  },

  /**
   * Get summary of future category data
   */
  getFutureCategorySummary: async (userId: string) => {
    try {
      const data = await futureCategoryService.getFutureCategoryData(userId);
      
      const summary = {
        totalCategories: Object.keys(data).length,
        categories: Object.keys(data),
        totalQuestions: 0,
        categoryBreakdown: {} as Record<string, number>
      };

      Object.entries(data).forEach(([category, items]) => {
        summary.totalQuestions += items.length;
        summary.categoryBreakdown[category] = items.length;
      });

      return summary;
    } catch (error) {
      console.error('Error getting future category summary:', error);
      return null;
    }
  },

  /**
   * Prepare migration mapping for housing ownership
   * This will be used when you implement the actual housing category
   */
  getHousingMigrationMapping: () => {
    return {
      'housing_status': 'h1', // Adjust these IDs based on your new category structure
      'has_mortgage': 'h2',
      'mortgage_company': 'h3',
      'monthly_mortgage_payment': 'h4',
      'monthly_rent': 'h5',
      'landlord_company': 'h6',
      'landlord_contact': 'h7'
    };
  },

  /**
   * Example migration function for housing data
   */
  migrateHousingData: async (userId: string, newCategoryId: string) => {
    try {
      const fieldMapping = futureCategoryUtils.getHousingMigrationMapping();
      
      const success = await futureCategoryService.migrateToNewCategory(
        userId,
        'housing_ownership',
        newCategoryId,
        fieldMapping
      );

      if (success) {
        console.log('✅ Housing data migrated successfully!');
        console.log('You can now delete the future category data if needed.');
      } else {
        console.log('❌ Housing data migration failed.');
      }

      return success;
    } catch (error) {
      console.error('Error migrating housing data:', error);
      return false;
    }
  }
};

export default futureCategoryUtils; 