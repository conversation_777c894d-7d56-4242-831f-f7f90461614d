import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const ScrollToQuestion = () => {
  const location = useLocation();

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const questionId = searchParams.get('questionId');
    
    if (questionId) {
      // Small delay to ensure the DOM is fully rendered
      const timer = setTimeout(() => {
        const element = document.getElementById(`question-${questionId}`);
        if (element) {
          element.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
          });
          
          // Add a highlight effect
          element.classList.add('ring-2', 'ring-blue-500', 'ring-opacity-50', 'rounded-lg');
          
          // Remove highlight after 3 seconds
          setTimeout(() => {
            element.classList.remove('ring-2', 'ring-blue-500', 'ring-opacity-50', 'rounded-lg');
          }, 3000);
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [location.search]);

  return null;
};

export default ScrollToQuestion;
