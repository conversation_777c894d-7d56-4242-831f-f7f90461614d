import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Formik, Form, Field, FieldArray, ErrorMessage } from "formik";
import GradiantHeader from "@/mobile/components/header/gradiantHeader";
import Footer from "@/mobile/components/layout/Footer";
import { CircularProgress } from "@/components/ui/CircularProgress";
import { categoryTabsConfig } from "@/data/categoryTabsConfig";
import { useAuth } from '@/contexts/AuthContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  updateUserInput,
  selectUserInputsBySubcategoryId,
  selectLoading,
  selectError
} from '@/store/slices/importantContactsSlice';
import importantcontacts from "@/data/importantContacts.json";
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert';
import * as Yup from 'yup';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

interface Contact {
  name: string;
  phone: string;
}

interface FormValues {
  [key: string]: Contact[];
}

const CONTACTS_QUESTION_ID = "c3";
const subcategoryId = "207C";

const contactValidationSchema = Yup.object().shape({
  [CONTACTS_QUESTION_ID]: Yup.array().of(
    Yup.object().shape({
      name: Yup.string()
        .min(2, 'Name must be at least 2 characters'),
      phone: Yup.string()
        .matches(/^\d{10,15}$/, 'Phone number must be 10-15 digits'),
    })
  )
});

export default function ReligiousAffiliation() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [formError, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get questions for this section
  const questions = (importantcontacts["207"] || []).filter(q => q.sectionId === subcategoryId);
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId(subcategoryId)(state));
  const isLoadingRedux = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Load saved answers and set existing input ID
  const initialValues: FormValues = (() => {
    if (userInputs && userInputs.length > 0) {
      const userInput = userInputs[0];
      const formValues = convertUserInputToFormValues(userInput);
      // Parse contacts JSON if needed
      let contacts: Contact[] = [];
      if (formValues[CONTACTS_QUESTION_ID]) {
        const answers = Array.isArray(formValues[CONTACTS_QUESTION_ID])
          ? formValues[CONTACTS_QUESTION_ID]
          : [formValues[CONTACTS_QUESTION_ID]];
        contacts = answers.flatMap(ans => {
          try {
            const parsed = JSON.parse(ans);
            // Convert any existing 'info' fields to 'phone'
            const converted = Array.isArray(parsed) ? parsed : [parsed];
            return converted.map(contact => ({
              name: contact.name || "",
              phone: contact.info || contact.phone || ""
            }));
          } catch {
            return [];
          }
        });
      }
      return { [CONTACTS_QUESTION_ID]: contacts };
    }
    return { [CONTACTS_QUESTION_ID]: [{ name: "", phone: "" }] };
  })();

  // Set existing input ID when user inputs change
  useEffect(() => {
    if (
      userInputs &&
      userInputs.length > 0 &&
      userInputs[0]._id &&
      userInputs[0]._id !== existingInputId
    ) {
      setExistingInputId(userInputs[0]._id);
    }
  }, [userInputs, existingInputId]);

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  if (isLoading) {
    return (
      <>
        <GradiantHeader title="Contacts" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <GradiantHeader title="Contacts" showAvatar={true} />
      <div className="container mx-auto px-4 py-4 max-w-md">
        {/* Tabs */}
        <div className="bg-gray-50 rounded-xl shadow-sm border border-gray-100 flex flex-row">
          {categoryTabsConfig.importantcontacts.map(tab => {
            const isActive = tab.path === "/category/importantcontacts/religiousaffiliation";
            return (
              <button
                key={tab.label}
                type="button"
                className={
                  "flex-1 py-2 rounded-lg font-medium max-w-xs mx-0 " +
                  (isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500")
                }
                style={{ flexShrink: 0 }}
                disabled={isActive}
                onClick={() => {
                  if (!isActive) navigate(tab.path);
                }}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Error Alert */}
        {(formError || reduxError) && (
          <Alert variant="destructive" className="mt-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        <div className="flex bg-white rounded-xl shadow-sm border border-gray-100 items-center justify-between mt-4 py-4 p-3">
          <div>
            <span className="text-lg font-bold text-gray-500">Contacts:</span>
            <span className="text-[#2BCFD5] font-semibold ml-1">Religious Affiliations</span>
          </div>
        </div>

        {/* Section Title */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mt-4">
          <p className="text-sm text-gray-600 mb-2">{questions[0]?.text}</p>
          <Formik
            initialValues={initialValues}
            validationSchema={contactValidationSchema}
            onSubmit={async (values, { setSubmitting }) => {
              try {
                if (!user?.id) {
                  setError('You must be logged in to save answers');
                  return;
                }

                // Ensure we have an array of contacts
                const contacts = Array.isArray(values[CONTACTS_QUESTION_ID]) 
                  ? values[CONTACTS_QUESTION_ID] 
                  : [];

                // Format the answers for the backend
                const answers = contacts
                  .filter((contact: Contact) => contact.name.trim() || contact.phone.trim()) // Filter out empty contacts
                  .map((contact: Contact, index: number) => ({
                    index,
                    originalQuestionId: CONTACTS_QUESTION_ID,
                    question: questions[0]?.text || "",
                    type: "text",
                    answer: JSON.stringify(contact)
                  }));

                // Format the answers by section
                const formattedAnswersBySection = [{
                  originalSectionId: subcategoryId,
                  isCompleted: true,
                  answers
                }];

                const userData = {
                  userId: user.id,
                  categoryId: generateObjectId(), // Generate a valid MongoDB ObjectId
                  originalCategoryId: '5', // Our manual category ID for Important Contacts
                  subCategoryId: generateObjectId(), // Generate a valid MongoDB ObjectId
                  originalSubCategoryId: subcategoryId,
                  answersBySection: formattedAnswersBySection
                };

                if (existingInputId) {
                  await dispatch(updateUserInput({ id: existingInputId, userData })).unwrap();
                } else {
                  const result = await dispatch(saveUserInput(userData)).unwrap();
                  if (result && result._id) {
                    setExistingInputId(result._id);
                  }
                }

                navigate('/category/importantcontacts/clubs');
              } catch (error) {
                setError('Failed to save your answers. Atleast one question must be answered.');
              } finally {
                setSubmitting(false);
              }
            }}
          >
            {({ values }) => (
              <Form>
                <FieldArray name={CONTACTS_QUESTION_ID}>
                  {({ push, remove }) => (
                    <div>
                      {Array.isArray(values[CONTACTS_QUESTION_ID]) && values[CONTACTS_QUESTION_ID].map((contact: Contact, idx: number) => (
                        <div key={idx} className="flex gap-2 mb-2">
                          <div className="flex-1">
                            <Field
                              name={`${CONTACTS_QUESTION_ID}.${idx}.name`}
                              placeholder="Name"
                              className="w-full border rounded p-2"
                            />
                            <ErrorMessage name={`${CONTACTS_QUESTION_ID}.${idx}.name`} component="div" className="text-red-500 text-xs mt-1" />
                          </div>
                          <div className="relative flex-1">
                            <Field
                              name={`${CONTACTS_QUESTION_ID}.${idx}.phone`}
                              placeholder="Phone"
                              className="w-full border rounded p-2 pr-8"
                            />
                            <ErrorMessage name={`${CONTACTS_QUESTION_ID}.${idx}.phone`} component="div" className="text-red-500 text-xs mt-1" />
                            <button
                              type="button"
                              onClick={() => remove(idx)}
                              className="absolute right-1 top-1/2 transform -translate-y-1/2 text-red-400 hover:text-red-600"
                              aria-label="Delete contact"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3m5 0H6" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      ))}
                      <button 
                        type="button" 
                        onClick={() => push({ name: "", phone: "" })} 
                        className="text-[#2BCFD5] mt-2 flex items-center gap-1"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                        </svg>
                        Add Contact
                      </button>
                    </div>
                  )}
                </FieldArray>
                <button type="submit" className="w-full bg-[#2BCFD5] text-white py-2 rounded-lg font-semibold mt-2">Save & Continue</button>
              </Form>
            )}
          </Formik>
        </div>
      </div>
      <Footer />
    </div>
  );
}
