import React from 'react';
import Layout from '@/mobile/components/layout/Layout';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import HomeInstructionSection from '@/mobile/components/QuickStartPage/HomeInstructionSection';
import WillInstructionSection from '@/mobile/components/QuickStartPage/WillInstructionSection';
import FuneralSection from '@/mobile/components/QuickStartPage/FuneralSection';
import ImportantContactSection from '@/mobile/components/QuickStartPage/ImportantContactSection';
import HousingSection from '@/mobile/components/QuickStartPage/HousingSection';
import { Button } from '@/components/ui/button';

function QuickStartFormPage() {
  const scrollToHomeInstructions = () => {
    const homeInstructionsSection = document.getElementById('home-instructions-section');
    if (homeInstructionsSection) {
      homeInstructionsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <>  
      <GradiantHeader
        title="Quick Start"
        showAvatar={true}
      />
      <div className="flex-1 bg-gray-50 py-6">
        <div className="space-y-6">
          {/* Home Instructions Section */}
          <div id="home-instructions-section">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 px-4">Home Instructions</h2>
            <HomeInstructionSection />
          </div>
          
          {/* Housing & Ownership Section */}
          <div>
            <h2 className="text-xl font-semibold mb-4 text-gray-800 px-4">Housing & Ownership</h2>
            <HousingSection />
          </div>
          
          {/* Will Instructions Section */}
          <div>
            <h2 className="text-xl font-semibold mb-4 text-gray-800 px-4">Will Instructions</h2>
            <WillInstructionSection />
          </div>
          {/* Funeral Arrangements Section */}
          <div>
            <h2 className="text-xl font-semibold mb-4 text-gray-800 px-4">Funeral Arrangements</h2>
            <FuneralSection />
          </div>
          {/* Important Contacts Section */}
          <div>
            <h2 className="text-xl font-semibold mb-4 text-gray-800 px-4">Important Contacts</h2>
            <ImportantContactSection />
          </div>
          
          {/* Navigation Button to Home Instructions */}
          <div className="px-4 mt-8">
            <Button
              onClick={scrollToHomeInstructions}
              className="w-full bg-[#2BCFD5] text-white py-3 rounded-lg font-semibold hover:bg-[#25b8be] transition-colors"
            >
              Go to Home Instructions
            </Button>
          </div>
        </div>
      </div>
      </>
  );
}

export default QuickStartFormPage;
