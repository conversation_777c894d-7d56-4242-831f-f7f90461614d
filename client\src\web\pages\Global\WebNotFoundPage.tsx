import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertTriangle, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';

const WebNotFoundPage: React.FC = () => {
  const navigate = useNavigate();
  const [countdown, setCountdown] = useState(5);

  useEffect(() => {
    // Auto-redirect to dashboard after 5 seconds
    const timer = setTimeout(() => {
      navigate('/dashboard');
    }, 5000);

    // Update countdown every second
    const countdownTimer = setInterval(() => {
      setCountdown((prev) => prev - 1);
    }, 1000);

    return () => {
      clearTimeout(timer);
      clearInterval(countdownTimer);
    };
  }, [navigate]);

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-[#f8f9fb] flex items-center justify-center">
      <div className="max-w-md w-full mx-auto px-6">
        <div className="bg-white rounded-lg shadow-lg p-8 text-center space-y-6">
          {/* Error Icon */}
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto">
            <AlertTriangle className="w-10 h-10 text-red-500" />
          </div>

          {/* Error Message */}
          <div className="space-y-3">
            <h1 className="text-3xl font-bold text-gray-900">
              404 - Page Not Found
            </h1>
            <p className="text-gray-600 leading-relaxed">
              The page you're looking for doesn't exist or has been moved. 
              You'll be automatically redirected to your dashboard.
            </p>
          </div>

          {/* Countdown */}
          <div className="bg-gray-50 rounded-lg p-4">
            <p className="text-sm text-gray-600">
              Redirecting to dashboard in{' '}
              <span className="font-semibold text-[#1F4168]">{countdown}</span>{' '}
              seconds...
            </p>
          </div>

          {/* Action Button */}
          <Button 
            onClick={handleGoToDashboard}
            className="w-full bg-[#1F4168] hover:bg-[#1F4168]/90 text-white py-3 rounded-lg flex items-center justify-center gap-2 transition-colors"
          >
            <Home className="w-4 h-4" />
            Go to Dashboard Now
          </Button>

          {/* Additional Help */}
          <div className="text-xs text-gray-400 border-t pt-4">
            If you continue to experience issues, please contact our support team.
          </div>
        </div>
      </div>
    </div>
  );
};

export default WebNotFoundPage;
