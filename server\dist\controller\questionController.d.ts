import { Request, Response } from 'express';
export declare const createQuestion: (req: Request, res: Response) => Promise<void>;
export declare const getQuestions: (req: Request, res: Response) => Promise<void>;
export declare const getQuestionById: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const updateQuestion: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const deleteQuestion: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getQuestionsByCategory: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const getQuestionsBySubCategory: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const saveAnswers: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
