"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getKeyHoldersForOwner = exports.getEmergencyAccessStatus = exports.denyEmergencyAccess = exports.grantEmergencyAccess = exports.requestEmergencyAccess = void 0;
const User_1 = __importDefault(require("../models/User"));
const Owner_1 = __importDefault(require("../models/Owner"));
const email_1 = require("../utils/email");
const email_2 = require("../utils/email");
// Request emergency access (for spare_key key holders)
const requestEmergencyAccess = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user) {
            res.status(401).json({ message: 'Authentication required' });
            return;
        }
        const user = yield User_1.default.findById(req.user._id);
        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }
        // Check if user is a key holder (not an owner)
        const owner = yield Owner_1.default.findOne({ userId: user._id });
        if (owner) {
            res.status(400).json({ message: 'Owners cannot request emergency access' });
            return;
        }
        // Check if user has spare_key subscription
        if (user.subscriptionType !== 'spare_key') {
            res.status(400).json({ message: 'Emergency access is only available for spare_key users' });
            return;
        }
        // Check if emergency access is already requested
        if (user.emergencyAccessRequested) {
            res.status(400).json({ message: 'Emergency access already requested' });
            return;
        }
        // Check if emergency access is already granted or denied
        if (user.emergencyAccessGranted || user.emergencyAccessDenied) {
            res.status(400).json({ message: 'Emergency access request already processed' });
            return;
        }
        // Request emergency access
        user.emergencyAccessRequested = true;
        user.emergencyAccessRequestedAt = new Date();
        yield user.save();
        // Find the owner for this key holder
        const ownerRecord = yield Owner_1.default.findById(user.ownerId);
        if (ownerRecord && ownerRecord.email) {
            // Compose names
            const ownerName = ownerRecord.firstName || ownerRecord.username || 'Owner';
            const keyHolderName = user.firstName || user.username || user.email || 'Key Holder';
            // Send email notification
            (0, email_1.sendEmergencyAccessRequestEmail)(ownerRecord.email, ownerName, keyHolderName)
                .then(() => console.log('Emergency access request email sent to owner.'))
                .catch((err) => console.error('Failed to send emergency access request email:', err));
        }
        res.status(200).json({
            message: 'Emergency access requested successfully',
            requestedAt: user.emergencyAccessRequestedAt,
            waitTime: '24 hours'
        });
    }
    catch (error) {
        console.error('Error requesting emergency access:', error);
        res.status(500).json({
            message: 'Error requesting emergency access',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.requestEmergencyAccess = requestEmergencyAccess;
// Grant emergency access (for owners)
const grantEmergencyAccess = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user) {
            res.status(401).json({ message: 'Authentication required' });
            return;
        }
        const { keyHolderId } = req.body;
        if (!keyHolderId) {
            res.status(400).json({ message: 'Key holder ID is required' });
            return;
        }
        // Check if current user is an owner
        const owner = yield Owner_1.default.findOne({ userId: req.user._id });
        if (!owner) {
            res.status(403).json({ message: 'Only owners can grant emergency access' });
            return;
        }
        // Find the key holder user
        const keyHolder = yield User_1.default.findById(keyHolderId);
        if (!keyHolder) {
            res.status(404).json({ message: 'Key holder not found' });
            return;
        }
        // Check if key holder has spare_key subscription
        if (keyHolder.subscriptionType !== 'spare_key') {
            res.status(400).json({ message: 'Emergency access is only available for spare_key users' });
            return;
        }
        // Check if emergency access is already granted or denied
        if (keyHolder.emergencyAccessGranted || keyHolder.emergencyAccessDenied) {
            res.status(400).json({ message: 'Emergency access request already processed' });
            return;
        }
        // Grant emergency access
        keyHolder.emergencyAccessGranted = true;
        keyHolder.emergencyAccessGrantedAt = new Date();
        yield keyHolder.save();
        // Send email notification to key holder
        const ownerName = owner.firstName || owner.username || 'Owner';
        const keyHolderName = keyHolder.firstName || keyHolder.username || keyHolder.email || 'Key Holder';
        (0, email_2.sendEmergencyAccessApprovalEmail)(keyHolder.email, keyHolderName, ownerName)
            .then(() => console.log('Emergency access approval email sent to key holder.'))
            .catch((err) => console.error('Failed to send emergency access approval email:', err));
        res.status(200).json({
            message: 'Emergency access granted successfully',
            grantedAt: keyHolder.emergencyAccessGrantedAt
        });
    }
    catch (error) {
        console.error('Error granting emergency access:', error);
        res.status(500).json({
            message: 'Error granting emergency access',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.grantEmergencyAccess = grantEmergencyAccess;
// Deny emergency access (for owners)
const denyEmergencyAccess = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user) {
            res.status(401).json({ message: 'Authentication required' });
            return;
        }
        const { keyHolderId } = req.body;
        if (!keyHolderId) {
            res.status(400).json({ message: 'Key holder ID is required' });
            return;
        }
        // Check if current user is an owner
        const owner = yield Owner_1.default.findOne({ userId: req.user._id });
        if (!owner) {
            res.status(403).json({ message: 'Only owners can deny emergency access' });
            return;
        }
        // Find the key holder user
        const keyHolder = yield User_1.default.findById(keyHolderId);
        if (!keyHolder) {
            res.status(404).json({ message: 'Key holder not found' });
            return;
        }
        // Check if key holder has spare_key subscription
        if (keyHolder.subscriptionType !== 'spare_key') {
            res.status(400).json({ message: 'Emergency access is only available for spare_key users' });
            return;
        }
        // Check if emergency access is already granted or denied
        if (keyHolder.emergencyAccessGranted || keyHolder.emergencyAccessDenied) {
            res.status(400).json({ message: 'Emergency access request already processed' });
            return;
        }
        // Deny emergency access
        keyHolder.emergencyAccessDenied = true;
        keyHolder.emergencyAccessDeniedAt = new Date();
        yield keyHolder.save();
        // Send email notification to key holder
        const ownerName = owner.firstName || owner.username || 'Owner';
        const keyHolderName = keyHolder.firstName || keyHolder.username || keyHolder.email || 'Key Holder';
        (0, email_2.sendEmergencyAccessRejectionEmail)(keyHolder.email, keyHolderName, ownerName)
            .then(() => console.log('Emergency access rejection email sent to key holder.'))
            .catch((err) => console.error('Failed to send emergency access rejection email:', err));
        res.status(200).json({
            message: 'Emergency access denied successfully',
            deniedAt: keyHolder.emergencyAccessDeniedAt
        });
    }
    catch (error) {
        console.error('Error denying emergency access:', error);
        res.status(500).json({
            message: 'Error denying emergency access',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.denyEmergencyAccess = denyEmergencyAccess;
// Get emergency access status
const getEmergencyAccessStatus = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user) {
            res.status(401).json({ message: 'Authentication required' });
            return;
        }
        const user = yield User_1.default.findById(req.user._id);
        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }
        // Check if user is an owner
        const owner = yield Owner_1.default.findOne({ userId: user._id });
        const isOwner = !!owner;
        let status = {
            subscriptionType: user.subscriptionType,
            isOwner,
            emergencyAccessRequested: user.emergencyAccessRequested,
            emergencyAccessRequestedAt: user.emergencyAccessRequestedAt,
            emergencyAccessGranted: user.emergencyAccessGranted,
            emergencyAccessGrantedAt: user.emergencyAccessGrantedAt,
            emergencyAccessDenied: user.emergencyAccessDenied,
            emergencyAccessDeniedAt: user.emergencyAccessDeniedAt,
            canRequestAccess: false,
            canGrantAccess: false,
            canDenyAccess: false,
            timeRemaining: null
        };
        if (user.subscriptionType === 'spare_key') {
            if (isOwner) {
                // Owner can grant/deny access for their key holders
                status.canGrantAccess = true;
                status.canDenyAccess = true;
            }
            else {
                // Key holder can request access if not already requested/granted/denied
                if (!user.emergencyAccessRequested && !user.emergencyAccessGranted && !user.emergencyAccessDenied) {
                    status.canRequestAccess = true;
                }
                // Calculate time remaining if access is requested but not yet granted/denied
                if (user.emergencyAccessRequested && !user.emergencyAccessGranted && !user.emergencyAccessDenied) {
                    const requestTime = new Date(user.emergencyAccessRequestedAt).getTime();
                    const currentTime = Date.now();
                    const timeElapsed = currentTime - requestTime;
                    const waitTime = 24 * 60 * 60 * 1000; // 24 hours in ms
                    const timeRemaining = Math.max(0, waitTime - timeElapsed);
                    status.timeRemaining = timeRemaining;
                }
            }
        }
        res.status(200).json(status);
    }
    catch (error) {
        console.error('Error getting emergency access status:', error);
        res.status(500).json({
            message: 'Error getting emergency access status',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getEmergencyAccessStatus = getEmergencyAccessStatus;
// Get all key holders for an owner (for emergency access management)
const getKeyHoldersForOwner = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!req.user) {
            res.status(401).json({ message: 'Authentication required' });
            return;
        }
        // Check if current user is an owner
        const owner = yield Owner_1.default.findOne({ userId: req.user._id });
        if (!owner) {
            res.status(403).json({ message: 'Only owners can view key holders' });
            return;
        }
        // Find all users who have this owner as their ownerId
        const keyHolders = yield User_1.default.find({
            ownerId: owner._id,
            subscriptionType: 'spare_key'
        }).select('-password');
        const keyHoldersWithStatus = keyHolders.map(keyHolder => ({
            _id: keyHolder._id,
            firstName: keyHolder.firstName,
            lastName: keyHolder.lastName,
            email: keyHolder.email,
            subscriptionType: keyHolder.subscriptionType,
            emergencyAccessRequested: keyHolder.emergencyAccessRequested,
            emergencyAccessRequestedAt: keyHolder.emergencyAccessRequestedAt,
            emergencyAccessGranted: keyHolder.emergencyAccessGranted,
            emergencyAccessGrantedAt: keyHolder.emergencyAccessGrantedAt,
            emergencyAccessDenied: keyHolder.emergencyAccessDenied,
            emergencyAccessDeniedAt: keyHolder.emergencyAccessDeniedAt
        }));
        res.status(200).json(keyHoldersWithStatus);
    }
    catch (error) {
        console.error('Error getting key holders:', error);
        res.status(500).json({
            message: 'Error getting key holders',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.getKeyHoldersForOwner = getKeyHoldersForOwner;
//# sourceMappingURL=emergencyAccessController.js.map