import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Clock, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { useAccessControl } from '@/hooks/useAccessControl';

interface EmergencyAccessRequestProps {
  className?: string;
}

export const EmergencyAccessRequest: React.FC<EmergencyAccessRequestProps> = ({ className }) => {
  const { 
    userAccess, 
    emergencyAccessStatus, 
    handleRequestEmergencyAccess,
    loading 
  } = useAccessControl();

  if (!userAccess || userAccess.subscriptionType !== 'spare_key' || userAccess.isOwner) {
    return null;
  }

  const formatTimeRemaining = (milliseconds: number): string => {
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const renderStatus = () => {
    if (userAccess.emergencyAccessGranted) {
      return (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Emergency access has been granted. You can now view the information.
          </AlertDescription>
        </Alert>
      );
    }

    if (userAccess.emergencyAccessDenied) {
      return (
        <Alert className="border-red-200 bg-red-50">
          <XCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            Emergency access was denied by the owner.
          </AlertDescription>
        </Alert>
      );
    }

    if (userAccess.emergencyAccessRequested) {
      const timeRemaining = emergencyAccessStatus?.timeRemaining;
      return (
        <Alert className="border-yellow-200 bg-yellow-50">
          <Clock className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            Emergency access requested. Waiting for owner approval or auto-grant.
            {timeRemaining && timeRemaining > 0 && (
              <div className="mt-2 text-sm">
                Time remaining: {formatTimeRemaining(timeRemaining)}
              </div>
            )}
          </AlertDescription>
        </Alert>
      );
    }

    return (
      <Alert className="border-blue-200 bg-blue-50">
        <AlertTriangle className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          You need to request emergency access to view the information.
        </AlertDescription>
      </Alert>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          Emergency Access Required
        </CardTitle>
        <CardDescription>
          As a Spare Key holder, you need to request emergency access to view information.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {renderStatus()}
        
        {!userAccess.emergencyAccessRequested && 
         !userAccess.emergencyAccessGranted && 
         !userAccess.emergencyAccessDenied && (
          <div className="space-y-3">
            <p className="text-sm text-gray-600">
              When you request emergency access:
            </p>
            <ul className="text-sm text-gray-600 space-y-1 ml-4">
              <li>• The owner will be notified of your request</li>
              <li>• The owner can grant or deny access immediately</li>
              <li>• If no action is taken within 24 hours, access will be automatically granted</li>
              <li>• You will only be able to view information, not edit it</li>
            </ul>
            <Button 
              onClick={handleRequestEmergencyAccess}
              disabled={loading}
              className="w-full"
            >
              {loading ? 'Requesting...' : 'Request Emergency Access'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 