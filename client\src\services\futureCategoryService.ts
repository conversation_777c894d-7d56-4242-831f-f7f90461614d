import userInputService, { generateObjectId } from './userInputService';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

export interface FutureCategoryData {
  category: string;
  fieldId: string;
  answer: string;
  question: string;
  timestamp: Date;
}

/**
 * Service for managing future category data that will eventually be migrated to proper categories
 */
const futureCategoryService = {
  /**
   * Get all future category data for a user
   */
  getFutureCategoryData: async (userId: string): Promise<Record<string, FutureCategoryData[]>> => {
    try {
      const ownerId = await getCachedOwnerIdFromUser({ id: userId } as any);
      if (!ownerId) {
        return {};
      }

      const futureCategories = ['999', '998', '997', '996']; // Future category IDs
      const futureData: Record<string, FutureCategoryData[]> = {};

      for (const categoryId of futureCategories) {
        const inputs = await userInputService.getUserInputsByOwnerAndCategory(ownerId, categoryId);
        
        if (inputs && inputs.length > 0) {
          const categoryData: FutureCategoryData[] = [];
          
          inputs.forEach(input => {
            if (input.answersBySection) {
              input.answersBySection.forEach(section => {
                if (section.answers) {
                  section.answers.forEach(answer => {
                    const categoryName = futureCategoryService.getCategoryNameFromId(categoryId);
                    if (categoryName) {
                      categoryData.push({
                        category: categoryName,
                        fieldId: answer.originalQuestionId || '',
                        answer: answer.answer || '',
                        question: answer.question || '',
                        timestamp: new Date((input as any).createdAt || Date.now())
                      });
                    }
                  });
                }
              });
            }
          });

          if (categoryData.length > 0) {
            const categoryName = futureCategoryService.getCategoryNameFromId(categoryId);
            if (categoryName) {
              futureData[categoryName] = categoryData;
            }
          }
        }
      }

      return futureData;
    } catch (error) {
      console.error('Error getting future category data:', error);
      return {};
    }
  },

  /**
   * Get specific future category data
   */
  getCategoryData: async (userId: string, categoryName: string): Promise<FutureCategoryData[]> => {
    const allData = await futureCategoryService.getFutureCategoryData(userId);
    return allData[categoryName] || [];
  },

  /**
   * Migrate future category data to a new proper category
   * This will be used when you implement the actual housing/ownership category
   */
  migrateToNewCategory: async (
    userId: string,
    fromCategory: string,
    toCategoryId: string,
    fieldMapping: Record<string, string> // Maps old field IDs to new field IDs
  ): Promise<boolean> => {
    try {
      const ownerId = await getCachedOwnerIdFromUser({ id: userId } as any);
      if (!ownerId) {
        throw new Error('Owner ID not found');
      }

      const futureData = await futureCategoryService.getCategoryData(userId, fromCategory);
      if (futureData.length === 0) {
        return true; // Nothing to migrate
      }

      // Group data by section (you can customize this based on your new category structure)
      const answersBySection = [{
        originalSectionId: '100A', // Adjust based on your new category structure
        isCompleted: true,
        answers: futureData.map((data, index) => ({
          index,
          originalQuestionId: fieldMapping[data.fieldId] || data.fieldId,
          question: data.question,
          type: 'text',
          answer: data.answer
        }))
      }];

      const userInputData = {
        userId: ownerId,
        ownerId,
        categoryId: generateObjectId(), // Generate proper ObjectId
        originalCategoryId: toCategoryId,
        subCategoryId: generateObjectId(), // Generate proper ObjectId
        originalSubCategoryId: '100', // Adjust based on your new category structure
        answersBySection
      };

      // Check if new category already has data
      const existingInputs = await userInputService.getUserInputsByOwnerAndCategory(ownerId, toCategoryId);

      if (existingInputs && existingInputs.length > 0) {
        // Merge with existing data
        const existingData = existingInputs[0];
        const mergedAnswers = [...(existingData.answersBySection || []), ...answersBySection].map(section => ({
          sectionId: (section as any).sectionId,
          originalSectionId: section.originalSectionId,
          isCompleted: section.isCompleted ?? true,
          answers: section.answers.map(answer => ({
            index: answer.index || 0,
            questionId: (answer as any).questionId,
            originalQuestionId: answer.originalQuestionId || '',
            question: answer.question || '',
            type: answer.type || 'text',
            answer: answer.answer || '',
            is_encrypted: (answer as any).is_encrypted || false
          }))
        }));
        
        await userInputService.updateUserInput(existingData._id!, {
          ...userInputData,
          answersBySection: mergedAnswers
        });
      } else {
        // Create new category data
        await userInputService.createUserInput(userInputData);
      }

      // Optionally, you can delete the future category data after successful migration
      // await futureCategoryService.deleteFutureCategoryData(userId, fromCategory);

      return true;
    } catch (error) {
      console.error('Error migrating future category data:', error);
      return false;
    }
  },

  /**
   * Delete future category data (use after successful migration)
   */
  deleteFutureCategoryData: async (userId: string, categoryName: string): Promise<boolean> => {
    try {
      const ownerId = await getCachedOwnerIdFromUser({ id: userId } as any);
      if (!ownerId) {
        throw new Error('Owner ID not found');
      }

      const categoryId = futureCategoryService.getCategoryIdFromName(categoryName);
      const inputs = await userInputService.getUserInputsByOwnerAndCategory(ownerId, categoryId);

      // Delete all inputs for this future category
      for (const input of inputs) {
        if (input._id) {
          // You'll need to add a delete method to userInputService
          // await userInputService.deleteUserInput(input._id);
        }
      }

      return true;
    } catch (error) {
      console.error('Error deleting future category data:', error);
      return false;
    }
  },

  /**
   * Get category name from ID
   */
  getCategoryNameFromId: (categoryId: string): string => {
    const categoryMap: Record<string, string> = {
      '999': 'housing_ownership',
      '998': 'insurance',
      '997': 'investments',
      '996': 'business'
    };
    return categoryMap[categoryId] || 'unknown';
  },

  /**
   * Get category ID from name
   */
  getCategoryIdFromName: (categoryName: string): string => {
    const categoryMap: Record<string, string> = {
      'housing_ownership': '999',
      'insurance': '998',
      'investments': '997',
      'business': '996'
    };
    return categoryMap[categoryName] || '999';
  },

  /**
   * Get available future categories
   */
  getAvailableFutureCategories: (): string[] => {
    return ['housing_ownership', 'insurance', 'investments', 'business'];
  }
};

export default futureCategoryService; 