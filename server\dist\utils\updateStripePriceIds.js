"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateSinglePlanPriceId = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const PricingPlan_1 = __importDefault(require("../models/PricingPlan"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
// Connect to MongoDB
const connectDB = () => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield mongoose_1.default.connect(process.env.MONGODB_URI);
        console.log('MongoDB connected successfully');
    }
    catch (error) {
        console.error('MongoDB connection error:', error);
        process.exit(1);
    }
});
// Update Stripe price IDs for existing pricing plans
const updateStripePriceIds = (priceIdUpdates) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Default price ID updates - replace these with your actual Stripe price IDs
        const defaultUpdates = [
            //   {
            //     type: 'temporary_key',
            //     priceIdStripe: 'price_1OqX8X2eZvKYlo2C9Q9Q9Q9Q' // Replace with your actual Stripe price ID
            //   },
            {
                type: 'spare_key',
                priceIdStripe: 'price_1RdU2KGad8zVVg7iPPG6JqCD' // Replace with your actual Stripe price ID
            },
            {
                type: 'all_access_key',
                priceIdStripe: 'price_1RdU4KGad8zVVg7iL29J0maX' // Replace with your actual Stripe price ID
            }
        ];
        const updates = priceIdUpdates || defaultUpdates;
        console.log('Starting to update Stripe price IDs...');
        // First, let's see what plans currently exist
        const existingPlans = yield PricingPlan_1.default.find({}, { type: 1, priceIdStripe: 1, price: 1, displayPrice: 1 });
        console.log('\n📋 Current pricing plans before update:');
        existingPlans.forEach(plan => {
            console.log(`- ${plan.type}: $${plan.displayPrice} (Stripe ID: ${plan.priceIdStripe || 'Not set'})`);
        });
        console.log('\n🔄 Updating price IDs...');
        for (const update of updates) {
            // Validate the price ID format (basic validation)
            if (!update.priceIdStripe.startsWith('price_')) {
                console.log(`⚠️  Warning: Price ID for ${update.type} doesn't start with 'price_': ${update.priceIdStripe}`);
            }
            const result = yield PricingPlan_1.default.updateOne({ type: update.type }, { $set: { priceIdStripe: update.priceIdStripe } });
            if (result.matchedCount > 0) {
                console.log(`✅ Updated ${update.type} with price ID: ${update.priceIdStripe}`);
            }
            else {
                console.log(`❌ No pricing plan found for type: ${update.type}`);
            }
        }
        // Display updated pricing plans
        console.log('\n📋 Updated pricing plans:');
        const updatedPlans = yield PricingPlan_1.default.find({}, { type: 1, priceIdStripe: 1, price: 1, displayPrice: 1 });
        updatedPlans.forEach(plan => {
            console.log(`- ${plan.type}: $${plan.displayPrice} (Stripe ID: ${plan.priceIdStripe || 'Not set'})`);
        });
        console.log('\n✅ Stripe price ID update completed successfully!');
    }
    catch (error) {
        console.error('❌ Error updating Stripe price IDs:', error);
        throw error;
    }
    finally {
        yield mongoose_1.default.disconnect();
        console.log('MongoDB disconnected');
    }
});
// Function to update a single plan's price ID
const updateSinglePlanPriceId = (planType, priceId) => __awaiter(void 0, void 0, void 0, function* () {
    yield connectDB();
    try {
        const result = yield PricingPlan_1.default.updateOne({ type: planType }, { $set: { priceIdStripe: priceId } });
        if (result.matchedCount > 0) {
            console.log(`✅ Updated ${planType} with price ID: ${priceId}`);
        }
        else {
            console.log(`❌ No pricing plan found for type: ${planType}`);
        }
    }
    catch (error) {
        console.error('❌ Error updating single plan:', error);
        throw error;
    }
    finally {
        yield mongoose_1.default.disconnect();
    }
});
exports.updateSinglePlanPriceId = updateSinglePlanPriceId;
// Run the update if this script is executed directly
if (require.main === module) {
    connectDB().then(() => {
        updateStripePriceIds();
    });
}
exports.default = updateStripePriceIds;
//# sourceMappingURL=updateStripePriceIds.js.map