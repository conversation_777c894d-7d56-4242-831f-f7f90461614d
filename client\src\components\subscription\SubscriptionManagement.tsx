import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  Calendar, 
  Settings, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  ExternalLink
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import stripeService from '@/services/stripeService';
import subscriptionService from '@/services/subscriptionService';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { Subscription, PricingPlan } from '@/types/subscription';

interface SubscriptionManagementProps {
  className?: string;
}

export default function SubscriptionManagement({ className = "" }: SubscriptionManagementProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isManaging, setIsManaging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [plans, setPlans] = useState<PricingPlan[]>([]);

  useEffect(() => {
    const fetchSubscription = async () => {
      if (!user?.id) {
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (ownerId) {
          const sub = await subscriptionService.getOwnerSubscription(ownerId);
          setSubscription(sub);
        }
        // Fetch all plans
        const allPlans = await subscriptionService.getPricingPlans();
        setPlans(allPlans);
      } catch (err) {
        console.error('Error fetching subscription:', err);
        setError('Failed to load subscription details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubscription();
  }, [user]);

  const handleManageSubscription = async () => {
    if (!subscription) return;

    setIsManaging(true);
    try {
      // This would need the Stripe customer ID from your subscription
      // For now, we'll show a placeholder
      toast({
        title: "Subscription Management",
        description: "Redirecting to Stripe customer portal...",
      });
      
      // In a real implementation, you'd get the customer ID from your subscription
      // await stripeService.redirectToCustomerPortal(customerId);
      
      // For now, just show a message
      toast({
        title: "Feature Coming Soon",
        description: "Subscription management portal will be available soon!",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to open subscription management",
        variant: "destructive",
      });
    } finally {
      setIsManaging(false);
    }
  };

  const getStatusBadge = (subscription: Subscription) => {
    if (!subscription.expiryAt) {
      return <Badge variant="secondary">Unknown</Badge>;
    }

    const now = new Date();
    const expiry = new Date(subscription.expiryAt);
    const isActive = expiry > now;

    if (isActive) {
      const daysRemaining = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysRemaining <= 7) {
        return <Badge variant="destructive">Expires Soon</Badge>;
      } else if (daysRemaining <= 30) {
        return <Badge variant="outline">Expires Soon</Badge>;
      } else {
        return <Badge variant="default">Active</Badge>;
      }
    } else {
      return <Badge variant="destructive">Expired</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysRemaining = (expiryAt: string) => {
    const now = new Date();
    const expiry = new Date(expiryAt);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <span className="ml-2">Loading subscription...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <AlertCircle className="h-6 w-6 text-red-500 mr-2" />
          <span className="text-red-600">{error}</span>
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Subscription
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-gray-600 mb-4">No active subscription found.</p>
            <Button onClick={() => window.location.href = '/auth/subscribe'}>
              View Plans
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Subscription Details
          </div>
          {getStatusBadge(subscription)}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Subscription Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>Created</span>
            </div>
            <p className="font-medium">{formatDate(subscription.createdAt)}</p>
          </div>
          
          {subscription.expiryAt && (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <CheckCircle className="h-4 w-4" />
                <span>Expires</span>
              </div>
              <p className="font-medium">{formatDate(subscription.expiryAt)}</p>
              {subscription.expiryAt && (
                <p className="text-sm text-gray-500">
                  {getDaysRemaining(subscription.expiryAt)} days remaining
                </p>
              )}
            </div>
          )}
        </div>

        {/* Plan Details */}
        {subscription.planId && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium mb-2">Current Plan</h4>
            <p className="text-sm text-gray-600">
              Plan: {plans.find(plan => plan._id === subscription.planId)?.type.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase()) || 'Unknown'}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          <Button
            onClick={handleManageSubscription}
            disabled={isManaging}
            className="flex-1"
          >
            {isManaging ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <Settings className="mr-2 h-4 w-4" />
                Manage Subscription
              </>
            )}
          </Button>
          
          <Button
            variant="outline"
            onClick={() => window.location.href = '/auth/subscribe'}
            className="flex-1"
          >
            <ExternalLink className="mr-2 h-4 w-4" />
            Upgrade Plan
          </Button>
        </div>

        {/* Warning for expiring subscriptions */}
        {subscription.expiryAt && getDaysRemaining(subscription.expiryAt) <= 7 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">
                Your subscription expires soon
              </span>
            </div>
            <p className="text-sm text-yellow-700 mt-1">
              Renew your subscription to continue accessing all features.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 