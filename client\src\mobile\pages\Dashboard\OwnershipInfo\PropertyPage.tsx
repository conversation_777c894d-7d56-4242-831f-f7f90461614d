import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Formik, Form, Field, ErrorMessage } from "formik";
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import ownershipInfoData from '@/data/ownershipInfo.json';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchUserInputs,
  saveUserInput,
  selectLoading,
  selectUserInputsBySubcategoryId,
  updateUserInput,
  selectError,
} from '@/store/slices/ownershipInfoSlice';
import ScrollToQuestion from '@/mobile/components/dashboard/OwnershipInfo/ScrollToQuestion';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

// Get Property questions (402A)
const questions = (ownershipInfoData["402"]?.filter(q => q.sectionId === "402A") || []) as any[];

function splitPropertySteps(questions: any[], values: Record<string, any> = {}) {
  // Step 0: Basic property question
  const step0 = questions.filter(q => q.id === "o1");

  // Dynamic steps based on rent/own selection
  if (values["o1"] === "Rent") {
    // For rent: combine rent questions + general property questions in one step
    const rentStep = questions.filter(q =>
      ["o8", "o9", "o10", "o11", "o12", "o13", "o14"].includes(q.id)
    );
    return [step0, rentStep];
  } else if (values["o1"] === "Own") {
    // For own: mortgage questions in step 1, online payment + general in step 2
    const step1 = questions.filter(q => ["o2", "o3", "o4"].includes(q.id));
    const step2 = questions.filter(q =>
      ["o5", "o6", "o7", "o11", "o12", "o13", "o14"].includes(q.id)
    );
    return [step0, step1, step2];
  } else {
    // Default: just the first step until selection is made
    return [step0];
  }
}



export default function PropertyPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [savedAnswers, setSavedAnswers] = useState<Record<string, string | string[]>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [formError, setError] = useState<string | null>(null);
  const [step, setStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Get data from Redux store
  const userInputs = useAppSelector((state) => selectUserInputsBySubcategoryId('402A')(state));
  const isLoadingRedux = useAppSelector(selectLoading);
  const reduxError = useAppSelector(selectError);

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');
  const fromReview = queryParams.get('fromReview');

  // Fetch user inputs when component mounts
  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  // Process user inputs when they are loaded
  useEffect(() => {
    if (userInputs && userInputs.length > 0) {
      const userInput = userInputs[0];

      if (userInput._id && userInput._id !== existingInputId) {
        setExistingInputId(userInput._id);
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      } else if (!existingInputId && userInput._id) {
        setExistingInputId(userInput._id);
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
      }
    }
  }, [userInputs, existingInputId]);

  // Set step to the one containing the target question when coming from review
  useEffect(() => {
    if (targetQuestionId && savedAnswers && Object.keys(savedAnswers).length > 0) {
      // Calculate steps based on current saved answers
      const steps = splitPropertySteps(questions, savedAnswers);

      // Find which step contains the target question
      const targetStepIndex = steps.findIndex(stepQuestions =>
        stepQuestions.some(q => q.id === targetQuestionId)
      );

      if (targetStepIndex !== -1 && targetStepIndex !== step) {
        setStep(targetStepIndex);
      }
    }
  }, [targetQuestionId, savedAnswers, step]);

  // Show loading state if data is being fetched
  if (isLoading || isLoadingRedux) {
    return (
      <>
        <GradiantHeader title="Ownership Information" showAvatar={true} />
        <div className="p-4 text-center">Loading your answers...</div>
      </>
    );
  }

  // Steps will be calculated dynamically inside Formik based on current values

  return (
    <>
      <GradiantHeader title="Ownership Information" showAvatar={true} />
      <div className="p-4">
        {/* Tab Bar */}
        <div className="flex flex-row flex-nowrap gap-3 mb-4 bg-gray-50 rounded-lg p-1 overflow-x-auto scrollbar-hide">
          {categoryTabsConfig.ownershipinfo.map(tab => {
            const isActive = tab.label === "Property Information";
            return (
              <button
                key={tab.label}
                className={`flex-1 py-1 px-1 rounded-md font-medium whitespace-nowrap ${
                  isActive
                    ? "bg-white text-[#2BCFD5] border border-[#2BCFD5] shadow"
                    : "text-gray-500"
                }`}
                disabled={isActive}
                onClick={() => !isActive && navigate(tab.path)}
              >
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Error message */}
        {(formError || reduxError) && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{formError || reduxError}</AlertDescription>
          </Alert>
        )}

        {/* Form */}
        <Formik
          initialValues={Object.keys(savedAnswers).length > 0 ? savedAnswers : {}}
          enableReinitialize={true}
          onSubmit={async (values, { setSubmitting }) => {
            try {
              if (!user || !user.id) {
                setError('You must be logged in to save answers');
                return;
              }

              // Group answers by sectionId
              const answersBySection: Record<string, any[]> = {};
              questions.forEach(q => {
                const value = values[q.id];
                if (value !== "" && value !== undefined) {
                  if (!answersBySection[q.sectionId]) answersBySection[q.sectionId] = [];
                  answersBySection[q.sectionId].push({
                    index: q.order,
                    originalQuestionId: q.id,
                    question: q.text,
                    type: q.type === 'dropdown' ? 'choice' : q.type === 'currency' ? 'text' : q.type,
                    answer: Array.isArray(value) ? value.join(', ') : value,
                    is_encrypted: q.text.toLowerCase().includes('password')
                  });
                }
              });

              const formattedAnswersBySection = Object.entries(answersBySection).map(
                ([sectionId, answers]) => ({
                  originalSectionId: sectionId,
                  isCompleted: true,
                  answers
                })
              );

              const userData = {
                userId: user.id,
                categoryId: generateObjectId(),
                originalCategoryId: '10',
                subCategoryId: generateObjectId(),
                originalSubCategoryId: '402A',
                answersBySection: formattedAnswersBySection
              };

              if (existingInputId) {
                await dispatch(updateUserInput({
                  id: existingInputId,
                  userData
                })).unwrap();
              } else {
                await dispatch(saveUserInput(userData)).unwrap();
              }
              
              if (fromReview) {
                navigate('/category/ownershipinfo/review');
              } else {
                navigate("/category/ownershipinfo/vehicleinformation");
              }
              setSubmitting(false);
            } catch (err) {
              setError("Failed to save your answers. At least one question must be answered.");
              setSubmitting(false);
            }
          }}
        >
          {({ values, isSubmitting }) => {
            // Calculate steps dynamically based on current values
            const steps = splitPropertySteps(questions, values);
            const currentStepQuestions = steps[step] || [];

            const visibleQuestions = currentStepQuestions.filter((q: any) => {
              if (!q.dependsOn) return true;
              return values[q.dependsOn.questionId] === q.dependsOn.value;
            });

            const handleNext = async () => {
              setStep(s => s + 1);
            };

            const handleSave = async () => {
              // Submit the form
              document.querySelector('form')?.requestSubmit();
            };

            // Reset step when rent/own selection changes
            useEffect(() => {
              if (step > 0 && values["o1"]) {
                // If user changes from own to rent or vice versa, reset to step 1
                const newSteps = splitPropertySteps(questions, values);
                if (step >= newSteps.length) {
                  setStep(1);
                }
              }
            }, [values["o1"]]);

            return (
              <Form>
                <div className="bg-gray-50 p-5 rounded-xl shadow-sm border mb-4">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-semibold">
                      Ownership Info: <span className="text-[#2BCFD5]">Property</span>
                    </p>
                    <CircularProgress
                      value={step + 1}
                      max={steps.length}
                      size={40}
                      stroke={3}
                      color="#2BCFD5"
                    />
                  </div>
                </div>
                <ScrollToQuestion questions={visibleQuestions}>
                  {(questionRefs) => (
                    <div className="space-y-4 mt-8 bg-gray-50 p-5 rounded-xl shadow-sm border">
                      {visibleQuestions.map(q => (
                        <div key={q.id} ref={el => { questionRefs[q.id] = el; }}>
                          <label className="block font-medium text-gray-700 mb-2">
                            {q.text}
                          </label>
                          {q.type === "boolean" ? (
                            <div className="flex space-x-4">
                              {["yes", "no"].map(option => (
                                <label
                                  key={option}
                                  className={
                                    "flex-1 py-2 px-4 border rounded-xl text-center cursor-pointer " +
                                    (values[q.id] === option
                                      ? "bg-[#2BCFD5] text-white border-[#2BCFD5]"
                                      : "bg-gray-50 hover:bg-[#25b6bb] hover:text-white")
                                  }
                                  style={{ transition: "all 0.2s" }}
                                >
                                  <Field type="radio" name={q.id} value={option} className="hidden" />
                                  {option.charAt(0).toUpperCase() + option.slice(1)}
                                </label>
                              ))}
                            </div>
                          ) : q.type === "dropdown" && Array.isArray(q.options) ? (
                            <Field as="select" name={q.id} className="w-full border rounded-lg px-3 py-2">
                              <option value="">Select...</option>
                              {q.options.map((option: string) => (
                                <option key={option} value={option}>{option}</option>
                              ))}
                            </Field>
                          ) : (
                            <Field
                              name={q.id}
                              type={
                                q.text.toLowerCase().includes("password") ? "password"
                                : q.type === "currency" ? "number"
                                : "text"
                              }
                              className="w-full border rounded-lg px-3 py-2"
                            />
                          )}
                          <ErrorMessage name={q.id} component="div" className="text-red-500 text-sm mt-1" />
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollToQuestion>

                <div className="mt-6 flex justify-between items-center">
                  <button
                    type="button"
                    onClick={() => setStep(s => s - 1)}
                    disabled={step === 0}
                    className="text-[#2BCFD5] underline disabled:opacity-50"
                  >
                    ← Back
                  </button>
                  {step < steps.length - 1 ? (
                    <button
                      type="button"
                      onClick={handleNext}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      Next →
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={handleSave}
                      disabled={isSubmitting}
                      className="bg-[#2BCFD5] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#25b6bb]"
                    >
                      Save
                    </button>
                  )}
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
      <Footer />
    </>
  );
}
