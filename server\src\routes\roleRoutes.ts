import express from 'express';
import {
  createRole,
  getAllRoles,
  getRoleById,
  getRoleByName,
  updateRole,
  deleteRole,
  initializeDefaultRoles,
  assignRoleToUser,
  getUsersByRole
} from '../controller/roleController';
import { validateCreateRole, validateUpdateRole } from '../validation/roleValidation';
import { combinedAuth } from '../middleware/authMiddleware';

const router = express.Router();

// Public routes (no authentication required)
router.get('/initialize-defaults', initializeDefaultRoles);
router.get('/all', getAllRoles);
router.get('/name/:name', getRoleByName);

// Protected routes (authentication required)
router.use(combinedAuth); // Apply authentication to all routes below

router.post('/', validateCreateRole, createRole);
router.get('/:id', getRoleById);
router.put('/:id', validateUpdateRole, updateRole);
router.delete('/:id', deleteRole);

// User role management routes
router.post('/assign', assignRoleToUser);
router.get('/:roleId/users', getUsersByRole);

export default router;
