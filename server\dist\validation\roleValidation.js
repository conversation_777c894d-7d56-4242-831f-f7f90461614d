"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateUpdateRole = exports.validateCreateRole = void 0;
const joi_1 = __importDefault(require("joi"));
const Role_1 = require("../types/Role");
const createRoleSchema = joi_1.default.object({
    name: joi_1.default.string()
        .valid(...Object.values(Role_1.RoleType))
        .required()
        .messages({
        'any.only': 'Role name must be one of: Owner, Nominee, Family',
        'any.required': 'Role name is required'
    }),
    description: joi_1.default.string()
        .required()
        .min(10)
        .max(500)
        .messages({
        'string.min': 'Description must be at least 10 characters',
        'string.max': 'Description cannot exceed 500 characters',
        'any.required': 'Description is required'
    }),
    permissions: joi_1.default.array()
        .items(joi_1.default.string().valid('canViewAll', 'canEditAll', 'canDeleteAll', 'canCreateAll'))
        .min(1)
        .required()
        .messages({
        'array.min': 'At least one permission is required',
        'any.required': 'Permissions are required'
    }),
    isActive: joi_1.default.boolean().optional()
});
const updateRoleSchema = joi_1.default.object({
    description: joi_1.default.string()
        .optional()
        .min(10)
        .max(500)
        .messages({
        'string.min': 'Description must be at least 10 characters',
        'string.max': 'Description cannot exceed 500 characters'
    }),
    permissions: joi_1.default.array()
        .items(joi_1.default.string().valid('canViewAll', 'canEditAll', 'canDeleteAll', 'canCreateAll'))
        .min(1)
        .optional()
        .messages({
        'array.min': 'At least one permission is required'
    }),
    isActive: joi_1.default.boolean().optional()
});
const validateCreateRole = (req, res, next) => {
    const { error } = createRoleSchema.validate(req.body);
    if (error) {
        res.status(400).json({
            status: 'fail',
            message: 'Validation error',
            errors: error.details.map(detail => detail.message)
        });
        return;
    }
    next();
};
exports.validateCreateRole = validateCreateRole;
const validateUpdateRole = (req, res, next) => {
    const { error } = updateRoleSchema.validate(req.body);
    if (error) {
        res.status(400).json({
            status: 'fail',
            message: 'Validation error',
            errors: error.details.map(detail => detail.message)
        });
        return;
    }
    next();
};
exports.validateUpdateRole = validateUpdateRole;
//# sourceMappingURL=roleValidation.js.map