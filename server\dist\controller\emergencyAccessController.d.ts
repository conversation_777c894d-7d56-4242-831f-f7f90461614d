import { Response } from 'express';
import { AccessControlRequest } from '../middleware/accessControlMiddleware';
export declare const requestEmergencyAccess: (req: AccessControlRequest, res: Response) => Promise<void>;
export declare const grantEmergencyAccess: (req: AccessControlRequest, res: Response) => Promise<void>;
export declare const denyEmergencyAccess: (req: AccessControlRequest, res: Response) => Promise<void>;
export declare const getEmergencyAccessStatus: (req: AccessControlRequest, res: Response) => Promise<void>;
export declare const getKeyHoldersForOwner: (req: AccessControlRequest, res: Response) => Promise<void>;
