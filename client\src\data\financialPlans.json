{"501": [{"id": "f1", "text": "Do you have a Financial Planner Representative?", "type": "boolean", "required": false, "sectionId": "501A", "order": 1}, {"id": "f2_contacts", "text": "Please provide your Financial Planner Representative contact details", "type": "contacts", "isContacts": true, "required": false, "sectionId": "501A", "order": 2, "dependsOn": {"questionId": "f1", "value": "yes"}}, {"id": "f4", "text": "Email", "type": "text", "required": false, "sectionId": "501A", "order": 4, "dependsOn": {"questionId": "f1", "value": "yes"}}, {"id": "f5", "text": "Do you have any Investments?", "type": "boolean", "required": false, "sectionId": "501B", "order": 5}, {"id": "f6_contacts", "text": "Please provide your Investment contact details", "type": "contacts", "isContacts": true, "required": false, "sectionId": "501B", "order": 6, "dependsOn": {"questionId": "f5", "value": "yes"}}, {"id": "f7", "text": "Do you have any stocks?", "type": "boolean", "required": false, "sectionId": "501C", "order": 8}, {"id": "f8_contacts", "text": "Please provide your Stock contact details", "type": "contacts", "isContacts": true, "required": false, "sectionId": "501C", "order": 9, "dependsOn": {"questionId": "f7", "value": "yes"}}, {"id": "f9", "text": "Do you have any bonds?", "type": "boolean", "required": false, "sectionId": "501D", "order": 11}, {"id": "f10_contacts", "text": "Please provide your Bond contact details", "type": "contacts", "isContacts": true, "required": false, "sectionId": "501D", "order": 12, "dependsOn": {"questionId": "f9", "value": "yes"}}, {"id": "f11", "text": "Do you have any money market accounts?", "type": "boolean", "required": false, "sectionId": "501E", "order": 14}, {"id": "f12_contacts", "text": "Please provide your Money Market contact details", "type": "contacts", "isContacts": true, "required": false, "sectionId": "501E", "order": 15, "dependsOn": {"questionId": "f11", "value": "yes"}}]}