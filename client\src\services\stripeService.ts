import api from './api';

export interface StripeCheckoutSession {
  id: string;
  url: string;
  payment_status: string;
  customer: string;
  subscription?: string;
}

export interface StripeSessionResponse {
  id: string;
  url: string;
  payment_status: string;
  customer: string;
  subscription?: string;
}

class StripeService {
  /**
   * Create a Stripe checkout session for subscription
   */
  async createCheckoutSession(planType: string, ownerId: string): Promise<StripeSessionResponse> {
    try {
      const response = await api.get<StripeSessionResponse>(`/v1/api/stripe/subscribe?plan=${planType}&ownerId=${ownerId}`);
      return response.data;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw new Error('Failed to create checkout session');
    }
  }

  /**
   * Retrieve checkout session details
   */
  async getCheckoutSession(sessionId: string): Promise<StripeSessionResponse> {
    try {
      const response = await api.get<StripeSessionResponse>(`/v1/api/stripe/success?session_id=${sessionId}`);
      return response.data;
    } catch (error) {
      console.error('Error retrieving checkout session:', error);
      throw new Error('Failed to retrieve checkout session');
    }
  }

  /**
   * Create customer portal session for subscription management
   */
  async createCustomerPortalSession(customerId: string): Promise<string> {
    try {
      // Since the backend endpoint redirects to Stripe portal, we'll handle it differently
      // For now, we'll return an empty string and handle the redirect in the calling method
      const response = await api.get(`/v1/api/stripe/customer/${customerId}`);
      
      // If the response has a URL in the data, use it
      if (response.data && typeof response.data === 'object' && 'url' in response.data) {
        return (response.data as any).url;
      }
      
      // Check if the response has a redirect URL in headers or other properties
      if (response.headers && response.headers.location) {
        return response.headers.location;
      }
      
      // Otherwise, return empty string - this will be handled by the calling method
      return '';
    } catch (error) {
      console.error('Error creating customer portal session:', error);
      throw new Error('Failed to create customer portal session');
    }
  }

  /**
   * Redirect to Stripe customer portal
   */
  async redirectToCustomerPortal(customerId: string): Promise<void> {
    try {
      const portalUrl = await this.createCustomerPortalSession(customerId);
      if (portalUrl) {
        window.location.href = portalUrl;
      } else {
        // If no URL is returned, we'll need to handle this differently
        // For now, we'll show a message that this feature is coming soon
        throw new Error('Customer portal feature is not yet available');
      }
    } catch (error) {
      console.error('Error redirecting to customer portal:', error);
      throw error;
    }
  }
}

export default new StripeService(); 