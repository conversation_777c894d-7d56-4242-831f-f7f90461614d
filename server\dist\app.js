"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const body_parser_1 = __importDefault(require("body-parser"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const express_session_1 = __importDefault(require("express-session"));
const passport_1 = __importDefault(require("passport"));
const path_1 = __importDefault(require("path"));
const dotenv_1 = __importDefault(require("dotenv"));
const fs_1 = __importDefault(require("fs"));
require("./config/passport");
const authRoutes_1 = __importDefault(require("./routes/authRoutes"));
const categoryRoutes_1 = __importDefault(require("./routes/categoryRoutes"));
const subcategoryRoutes_1 = __importDefault(require("./routes/subcategoryRoutes"));
const questionRoutes_1 = __importDefault(require("./routes/questionRoutes"));
const googleAuthRoutes_1 = __importDefault(require("./routes/googleAuthRoutes"));
const userInputRoutes_1 = __importDefault(require("./routes/userInputRoutes"));
const roleRoutes_1 = __importDefault(require("./routes/roleRoutes"));
const ownerRoutes_1 = __importDefault(require("./routes/ownerRoutes"));
const pricingPlanRoutes_1 = __importDefault(require("./routes/pricingPlanRoutes"));
const subscribedPlanRoutes_1 = __importDefault(require("./routes/subscribedPlanRoutes"));
const invitationRoutes_1 = __importDefault(require("./routes/invitationRoutes"));
const requestedCategoriesRoutes_1 = __importDefault(require("./routes/requestedCategoriesRoutes"));
const emergencyAccessRoutes_1 = __importDefault(require("./routes/emergencyAccessRoutes"));
const testing_1 = __importDefault(require("./routes/testing"));
const stripeRoutes_1 = __importDefault(require("./routes/stripeRoutes"));
dotenv_1.default.config();
const app = (0, express_1.default)();
const uploadsDir = path_1.default.join(__dirname, '../uploads');
if (!fs_1.default.existsSync(uploadsDir)) {
    fs_1.default.mkdirSync(uploadsDir, { recursive: true });
}
// Mount Stripe webhook route BEFORE bodyParser
app.use('/v1/api/stripe/webhooks', stripeRoutes_1.default);
function configureMiddleware() {
    // app.use(cors({
    //   origin: process.env.FRONTEND_URL,
    //   credentials: true,
    //   methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    //   allowedHeaders: ['Content-Type', 'Authorization']
    // }));
    app.use((0, cors_1.default)({
        origin: ['https://heirkeyportal.com', 'http://localhost:3000', 'http://localhost:5173'],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'stripe-signature'],
        exposedHeaders: ['Content-Length', 'Content-Type']
    }));
    app.use((0, helmet_1.default)({
        crossOriginResourcePolicy: { policy: 'cross-origin' }
    }));
    app.use((0, cookie_parser_1.default)());
    // Configure body parser to skip Stripe webhook routes
    app.use(body_parser_1.default.json({
        verify: (req, res, buf) => {
            // Store raw body for Stripe webhook signature verification
            req.rawBody = buf;
        }
    }));
    app.use(body_parser_1.default.urlencoded({ extended: true }));
    app.use((0, express_session_1.default)({
        secret: process.env.SESSION_SECRET || 'default',
        resave: false,
        saveUninitialized: false,
        cookie: {
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: 24 * 60 * 60 * 1000
        }
    }));
    app.use(passport_1.default.initialize());
    app.use(passport_1.default.session());
}
function configureRoutes() {
    app.use('/uploads', express_1.default.static(uploadsDir));
    app.use('/v1/api/auth', authRoutes_1.default);
    app.use('/v1/api/categories', categoryRoutes_1.default);
    app.use('/v1/api/subcategories', subcategoryRoutes_1.default);
    app.use('/v1/api/questions', questionRoutes_1.default);
    app.use('/v1/api/user-inputs', userInputRoutes_1.default);
    app.use('/v1/api/roles', roleRoutes_1.default);
    app.use('/v1/api/owners', ownerRoutes_1.default);
    app.use('/v1/api/pricing-plans', pricingPlanRoutes_1.default);
    app.use('/v1/api/subscriptions', subscribedPlanRoutes_1.default);
    app.use('/v1/api/invitations', invitationRoutes_1.default);
    app.use('/v1/api/category-requests', requestedCategoriesRoutes_1.default);
    app.use('/v1/api/emergency-access', emergencyAccessRoutes_1.default);
    // Mount the rest of the Stripe routes (except webhooks) AFTER bodyParser
    app.use('/v1/api/stripe', stripeRoutes_1.default);
    app.use('/v1/api/testing', testing_1.default);
    // Direct health check route at root level
    app.get('/health', (_req, res) => {
        res.status(200).json({
            status: 'ok',
            message: 'server is running....',
            timestamp: new Date(),
            environment: process.env.NODE_ENV || 'development',
            version: process.env.npm_package_version || '1.0.0',
            uptime: process.uptime() + ' seconds'
        });
    });
    app.use('/', googleAuthRoutes_1.default);
    app.get('/uploads/:filename', (req, res) => {
        const filePath = path_1.default.join(uploadsDir, req.params.filename);
        fs_1.default.access(filePath, fs_1.default.constants.F_OK, (err) => {
            if (err) {
                console.error(`File not found: ${filePath}`);
                return res.status(404).json({ message: 'Image not found' });
            }
            res.sendFile(filePath, (err) => {
                if (err && !res.headersSent) {
                    console.error(`Error sending file: ${err.message}`);
                    res.status(500).json({ message: 'Failed to send file' });
                }
            });
        });
    });
}
function configureErrorHandling() {
    app.use((err, _req, res, _next) => {
        const statusCode = err.statusCode || 500;
        const status = err.status || 'error';
        res.status(statusCode).json({
            status,
            message: err.message,
            stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
        });
    });
}
configureMiddleware();
configureRoutes();
configureErrorHandling();
exports.default = app;
//# sourceMappingURL=app.js.map