import { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Formik, Form, FormikHelpers } from 'formik';
import * as Yup from 'yup';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { useAuth } from '@/contexts/AuthContext';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import SubCategoryHeader from '@/web/components/Global/SubCategoryHeader';
import SubCategoryTitle from '@/web/components/Global/SubCategoryTitle';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import socialMediaData from '@/data/socialMedia.json';
import GoodToKnowBox from '@/web/components/Global/GoodToKnowBox';
import SubCategoryFooterNav from '@/web/components/Global/SubCategoryFooterNav';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  saveUserInput,
  updateUserInput,
  selectUserInputsBySubcategoryId,
  UserInput,
  Answer,
  fetchUserInputs
} from '@/store/slices/socialMediaSlice';
import { generateObjectId, convertUserInputToFormValues } from '@/services/userInputService';
import ScrollToQuestion from '@/web/components/Category/SocialMedia/ScrollToQuestion';
import { QuestionItem, Question, buildValidationSchema, generateInitialValues, handleDependentAnswers } from '@/web/components/Category/SocialMedia/FormFields';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import { createUserInfo } from '@/utils/avatarUtils';

const INSTAGRAM_SECTION_ID = '206C';

const getInstagramQuestions = () => {
  return (socialMediaData['206'] || []).filter(
    (q) => q.sectionId === INSTAGRAM_SECTION_ID
  ) as Question[];
};

const tabs = categoryTabsConfig.socialmedia;

const Instagram = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const [savedAnswers, setSavedAnswers] = useState<Record<string, any>>({});
  const [error, setError] = useState<string | null>(null);
  const isInitialMount = useRef(true);
  const hasLoadedAnswers = useRef(false);

  const existingInputs = useAppSelector(selectUserInputsBySubcategoryId(INSTAGRAM_SECTION_ID));

  useEffect(() => {
    const fetchData = async () => {
      if (user?.id && isInitialMount.current) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in Instagram component');
          }
          isInitialMount.current = false;
        } catch (error) {
          console.error('Error fetching owner ID in Instagram component:', error);
          isInitialMount.current = false;
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  useEffect(() => {
    setQuestions(getInstagramQuestions());
    setLoading(false);
  }, []);

  useEffect(() => {
    if (existingInputs.length > 0 && !loading && !hasLoadedAnswers.current) {
      const userInput = existingInputs[0];
      if (userInput && userInput._id) {
        const formValues = convertUserInputToFormValues(userInput);
        setSavedAnswers(formValues);
        setExistingInputId(userInput._id);
        hasLoadedAnswers.current = true;
      }
    }
  }, [existingInputs, loading]);

  const validationSchema = buildValidationSchema(questions, Yup);
  const baseInitialValues = generateInitialValues(questions);
  const initialValues = { ...baseInitialValues, ...savedAnswers };

  const handleSubmit = async (values: Record<string, string>, { setSubmitting }: FormikHelpers<Record<string, string>>) => {
    if (!user?.id) return;

    try {
      setError(null);
      const answers: Answer[] = questions
        .map((q, index) => {
          const value = values[q.id];
          if (value === undefined || value === null || value === '') {
            return null; // skip unanswered
          }
          return {
            index,
            originalQuestionId: q.id,
            question: q.text,
            type: q.type === 'password' ? 'text' : q.type,
            answer: value
          } as Answer;
        })
        .filter((answer): answer is Answer => answer !== null);

      const userInput: UserInput = {
        userId: user.id,
        categoryId: generateObjectId(),
        originalCategoryId: '6',
        subCategoryId: generateObjectId(),
        originalSubCategoryId: INSTAGRAM_SECTION_ID,
        answersBySection: [{
          originalSectionId: INSTAGRAM_SECTION_ID,
          isCompleted: true,
          answers
        }]
      };

      if (existingInputId) {
        await dispatch(updateUserInput({
          id: existingInputId,
          userData: userInput
        })).unwrap();
      } else {
        const result = await dispatch(saveUserInput(userInput)).unwrap();
        if (result && result._id) {
          setExistingInputId(result._id);
        }
      }

      // Refresh data using owner ID
      const ownerId = await getCachedOwnerIdFromUser(user);
      if (ownerId) {
        await dispatch(fetchUserInputs(ownerId));
      }
      navigate('/category/socialmedia/otheraccounts');
    } catch (error: any) {
      // console.error('Error saving Instagram inputs:', error);
      setError(error.message || 'Failed to save changes. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      <SubCategoryHeader
        title="Social Media and Phone"
        backTo="/dashboard"
        user={{
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'User',
          email: user?.email || '',
          avatar: createUserInfo(user).avatar
        }}
      />
      <SubCategoryTabs tabs={tabs} />
      <div className="container mx-auto px-6">
        <SubCategoryTitle
          mainCategory="Social Media and Phone"
          category="Instagram"
          description="Please answer the following questions about your Instagram account."
        />
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              {error && (
                <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
                  {error}
                </div>
              )}
              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
                enableReinitialize
              >
                {({ values, handleChange, isSubmitting, errors, touched, setFieldValue, isValid, dirty }) => {
                  useEffect(() => {
                    const setValues = (newValues: Record<string, any>) => {
                      Object.entries(newValues).forEach(([field, value]) => {
                        setFieldValue(field, value, false);
                      });
                    };
                    handleDependentAnswers(values, questions, setValues);
                  }, [values, questions, setFieldValue]);

                  return (
                    <Form>
                      <ScrollToQuestion questions={questions}>
                        {(refs) => (
                          <>
                            {questions.map((q) => (
                              <div key={q.id} id={`question-${q.id}`} ref={el => { refs[q.id] = el; }}>
                                <QuestionItem
                                  question={q}
                                  values={values}
                                />
                              </div>
                            ))}
                          </>
                        )}
                      </ScrollToQuestion>
                      <div className="mt-8 flex justify-end">
                        <button
                          type="submit"
                          disabled={isSubmitting || !isValid || !dirty}
                          className={`px-4 py-2 rounded font-semibold ${
                            isSubmitting || !isValid || !dirty
                              ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                              : 'bg-[#2BCFD5] text-white hover:bg-[#19bbb5]'
                          }`}
                        >
                          {isSubmitting ? 'Saving...' : 'Save & Continue'}
                        </button>
                      </div>
                    </Form>
                  );
                }}
              </Formik>
              <GoodToKnowBox
                title="Editing my Answers"
                description="Each topic below is a part of your home documents, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you."
              />
              <SubCategoryFooterNav
                leftLabel="Facebook"
                leftTo="/category/socialmedia/facebook"
                rightLabel="Other Social Media"
                rightTo="/category/socialmedia/otheraccounts"
              />
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Instagram;
